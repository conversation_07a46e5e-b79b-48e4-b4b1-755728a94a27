<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>في ذكرى استشهاد مسلم بن عقيل - سفير الإمام الحسين (عليهما السلام) - AliToucan</title>
    <meta name="description" content="تعزية إسلامية لذكرى استشهاد مسلم بن عقيل، سفير الإمام الحسين عليهما السلام في الكوفة">
    <meta name="keywords" content="مسلم بن عقيل، الإمام الحسين، أهل البيت، شيعة، تعزية، استشهاد، الكوفة، سفير">

    <!-- Arabic Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Local Fonts Fallback -->
    <style>
        @font-face {
            font-family: 'Amiri-Local';
            src: url('./fonts/Amiri-Regular.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'NotoSansArabic-Local';
            src: url('./fonts/NotoSansArabic-Regular.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
        }
    </style>

    <!-- html2canvas Library for High-Quality Image Generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- Meta Tags for Social Media -->
    <meta property="og:title" content="في ذكرى استشهاد مسلم بن عقيل - سفير الإمام الحسين (عليهما السلام)">
    <meta property="og:description" content="تعزية إسلامية لذكرى استشهاد مسلم بن عقيل، سفير الإمام الحسين في الكوفة">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_AR">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="app_icon.ico">

    <style>
        /* ===== CSS RESET AND BASE STYLES ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: 'Amiri', 'Amiri-Local', 'Scheherazade New', 'Noto Sans Arabic', 'NotoSansArabic-Local', serif;
            line-height: 1.8;
            color: #f5f5f5;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            direction: rtl;
            text-align: right;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* ===== CONTAINER FOR 9:16 ASPECT RATIO ===== */
        .main-container {
            max-width: 600px;
            width: 100%;
            margin: 0 auto;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            position: relative;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* ===== CONDOLENCE CARD (9:16 OPTIMIZED) ===== */
        .condolence-card {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            position: relative;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100vh;
            max-height: 100vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        /* ===== BRAND HEADER (HIDDEN) ===== */
        .brand-header {
            display: none;
        }

        /* ===== OPENING QURANIC VERSE ===== */
        .opening-verse {
            font-size: 1.2rem;
            line-height: 1.8;
            text-align: center;
            color: #90ee90;
            font-weight: 600;
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(144, 238, 144, 0.3);
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
            position: relative;
        }

        .opening-verse::before {
            content: '﴿';
            position: absolute;
            top: 5px;
            right: 10px;
            font-size: 1.5rem;
            color: #d4af37;
        }

        .opening-verse::after {
            content: '﴾';
            position: absolute;
            bottom: 5px;
            left: 10px;
            font-size: 1.5rem;
            color: #d4af37;
        }

        /* ===== MAIN TITLE ===== */
        .main-title {
            font-size: 1.6rem;
            text-align: center;
            color: #d4af37;
            font-weight: 700;
            margin-bottom: 0.8rem;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            line-height: 1.4;
        }

        /* ===== MARTYR NAME ===== */
        .martyr-name {
            font-size: 2.2rem;
            text-align: center;
            color: #d4af37;
            font-weight: 700;
            margin-bottom: 0.8rem;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            line-height: 1.3;
            background: rgba(212, 175, 55, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid rgba(212, 175, 55, 0.3);
        }

        .honorific {
            display: block;
            font-size: 1.1rem;
            color: #90ee90;
            margin-top: 0.3rem;
            font-weight: 600;
        }

        /* ===== ROLE DESCRIPTION ===== */
        .role-description {
            font-size: 1.1rem;
            text-align: center;
            color: #cccccc;
            margin-bottom: 1rem;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.05);
            padding: 0.8rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* ===== CONDOLENCE CONTENT ===== */
        .condolence-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: 0.8rem;
        }

        .condolence-text {
            font-size: 0.95rem;
            line-height: 1.6;
            text-align: justify;
            color: #f5f5f5;
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid rgba(212, 175, 55, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        /* ===== HISTORICAL CONTEXT ===== */
        .historical-context {
            background: rgba(22, 33, 62, 0.4);
            padding: 1rem;
            border-radius: 10px;
            border: 2px solid rgba(144, 238, 144, 0.3);
        }

        .historical-context h3 {
            font-size: 1.2rem;
            color: #d4af37;
            margin-bottom: 0.5rem;
            text-align: center;
            font-weight: 700;
        }

        .historical-context p {
            font-size: 0.9rem;
            line-height: 1.5;
            text-align: justify;
            color: #cccccc;
        }

        /* ===== CLOSING PRAYER ===== */
        .closing-prayer {
            font-size: 1rem;
            line-height: 1.6;
            text-align: center;
            color: #90ee90;
            font-weight: 600;
            padding: 1rem;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(144, 238, 144, 0.3);
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
        }

        /* ===== FOOTER BRAND (HIDDEN) ===== */
        .footer-brand {
            display: none;
        }

        /* ===== GEOMETRIC PATTERNS ===== */
        .condolence-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 80% 80%, rgba(144, 238, 144, 0.1) 2px, transparent 2px);
            background-size: 80px 80px;
            background-position: 0 0, 40px 40px;
            opacity: 0.3;
            pointer-events: none;
            z-index: 0;
        }

        .condolence-card > * {
            position: relative;
            z-index: 1;
        }

        /* ===== DOWNLOAD CONTROLS ===== */
        .download-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            z-index: 1000;
            justify-content: center;
            max-width: 90%;
        }

        .download-btn {
            background: linear-gradient(135deg, #d4af37, #b8941f);
            color: #000;
            border: none;
            padding: 10px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            font-family: 'Amiri', serif;
            white-space: nowrap;
            min-width: 120px;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
            background: linear-gradient(135deg, #e6c547, #d4af37);
        }

        .download-btn:active {
            transform: translateY(0);
        }

        .download-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* ===== LOADING INDICATOR ===== */
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: #d4af37;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            z-index: 2000;
            display: none;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(212, 175, 55, 0.3);
            border-top: 4px solid #d4af37;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 768px) {
            .condolence-card {
                padding: 1rem;
            }

            .martyr-name {
                font-size: 1.8rem;
                padding: 0.8rem;
            }

            .main-title {
                font-size: 1.4rem;
            }

            .opening-verse {
                font-size: 1rem;
                padding: 0.8rem;
            }

            .condolence-text {
                font-size: 0.85rem;
                padding: 0.8rem;
            }

            .historical-context {
                padding: 0.8rem;
            }

            .historical-context h3 {
                font-size: 1rem;
            }

            .historical-context p {
                font-size: 0.8rem;
            }

            .closing-prayer {
                font-size: 0.9rem;
                padding: 0.8rem;
            }

            .role-description {
                font-size: 1rem;
                padding: 0.6rem;
            }

            .download-controls {
                bottom: 10px;
                flex-direction: row;
                flex-wrap: wrap;
                gap: 0.3rem;
            }

            .download-btn {
                font-size: 0.8rem;
                padding: 8px 12px;
                min-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .condolence-card {
                padding: 0.8rem;
            }

            .martyr-name {
                font-size: 1.6rem;
            }

            .main-title {
                font-size: 1.2rem;
            }

            .opening-verse {
                font-size: 0.9rem;
            }

            .condolence-text {
                font-size: 0.8rem;
            }

            .download-controls {
                flex-direction: column;
                gap: 0.2rem;
            }

            .download-btn {
                font-size: 0.7rem;
                padding: 6px 10px;
                min-width: 90px;
            }
        }

        /* ===== 3D VISUAL EFFECTS ===== */
        .martyr-name {
            text-shadow:
                3px 3px 6px rgba(0, 0, 0, 0.8),
                0 0 20px rgba(212, 175, 55, 0.3),
                0 0 40px rgba(212, 175, 55, 0.2);
            transform: perspective(1000px) rotateX(5deg);
        }

        .opening-verse {
            box-shadow:
                0 10px 30px rgba(144, 238, 144, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transform: perspective(1000px) rotateX(-2deg);
        }

        .condolence-text {
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="condolence-card" id="condolenceCard">
            <!-- Opening Quranic Verse -->
            <div class="opening-verse">
                وَبَشِّرِ الصَّابِرِينَ * الَّذِينَ إِذَا أَصَابَتْهُم مُّصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ
            </div>

            <!-- Main Title -->
            <h1 class="main-title">في ذكرى استشهاد</h1>

            <!-- Martyr Name -->
            <h2 class="martyr-name">
                مُسْلِم بن عَقِيل
                <span class="honorific">(عليه السلام)</span>
            </h2>

            <!-- Role Description -->
            <div class="role-description">
                سَفِير الإمام الحُسَيْن (عليه السلام) إلى الكُوفَة
            </div>

            <!-- Main Condolence Content -->
            <div class="condolence-content">
                <div class="condolence-text">
                    بقلوب مؤمنة بقضاء الله وقدره، نتقدم بأحر التعازي وأصدق المواساة بمناسبة ذكرى استشهاد البطل الشهيد مسلم بن عقيل (عليه السلام)، ابن عم الإمام الحسين وسفيره الأمين إلى أهل الكوفة، الذي ضحى بنفسه الطاهرة في سبيل الله ونصرة الحق، وقدم أروع الأمثلة في الوفاء والإخلاص لأهل البيت الأطهار (عليهم السلام).
                </div>

                <!-- Historical Context -->
                <div class="historical-context">
                    <h3>السياق التاريخي</h3>
                    <p>
                        أرسل الإمام الحسين (عليه السلام) ابن عمه مسلم بن عقيل إلى الكوفة ليكون سفيره ووكيله، وليأخذ البيعة من أهلها. وقد نجح مسلم في مهمته وبايعه آلاف من أهل الكوفة، لكن عندما علم ابن زياد بوجوده، دبر له المكائد حتى استشهد في دار هانئ بن عروة، وكان استشهاده إيذاناً ببداية مأساة كربلاء.
                    </p>
                </div>

                <!-- Closing Prayer -->
                <div class="closing-prayer">
                    اللهم صل على محمد وآل محمد، وعجل فرج وليك الحجة بن الحسن، واجعلنا من أنصاره وأعوانه والذابين عنه، والمستشهدين بين يديه. وارزقنا شفاعة الحسين يوم الورود.
                </div>
            </div>
        </div>

        <!-- Download Controls -->
        <div class="download-controls">
            <button class="download-btn" onclick="downloadHighQualityImage()">
                📱 تحميل عالي الجودة
            </button>
            <button class="download-btn" onclick="downloadInstagramStory()">
                📸 Instagram Stories
            </button>
            <button class="download-btn" onclick="downloadWithManualCanvas()">
                🎨 تحميل يدوي
            </button>
            <button class="download-btn" onclick="simpleScreenshotMethod()">
                ⚡ تحميل سريع
            </button>
            <button class="download-btn" onclick="testDownload()" style="background: linear-gradient(135deg, #90ee90, #7dd87d);">
                🔧 اختبار التحميل
            </button>
        </div>

        <!-- Loading Indicator -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <div>جاري إنشاء الصورة عالية الجودة...</div>
        </div>
    </div>

    <script>
        // Check if html2canvas is loaded
        function checkHtml2Canvas() {
            if (typeof html2canvas === 'undefined') {
                showNotification('مكتبة html2canvas غير محملة. جاري إعادة المحاولة...', 'error');
                return false;
            }
            return true;
        }

        // High-Quality Image Download Function
        async function downloadHighQualityImage() {
            if (!checkHtml2Canvas()) {
                // Try to reload the library
                loadHtml2Canvas().then(() => {
                    setTimeout(downloadHighQualityImage, 1000);
                });
                return;
            }

            const loadingIndicator = document.getElementById('loadingIndicator');
            const condolenceCard = document.getElementById('condolenceCard');

            try {
                // Show loading indicator
                loadingIndicator.style.display = 'block';

                // Hide download controls temporarily
                const downloadControls = document.querySelector('.download-controls');
                downloadControls.style.display = 'none';

                // Wait for fonts to load
                await document.fonts.ready;

                // Simple, reliable canvas creation
                const canvas = await html2canvas(condolenceCard, {
                    scale: 2, // Reduced scale for better compatibility
                    useCORS: true,
                    allowTaint: false,
                    backgroundColor: '#0a0a0a',
                    logging: false,
                    removeContainer: true,
                    async: true
                });

                // Create download link with simple filename
                const link = document.createElement('a');
                link.download = 'Muslim_Ibn_Aqeel_Condolence_High_Quality.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                // Force download
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();

                // Clean up
                setTimeout(() => {
                    if (link.parentNode) {
                        document.body.removeChild(link);
                    }
                }, 100);

                // Show success notification
                showNotification(`تم تحميل الصورة بنجاح! (${canvas.width}x${canvas.height})`, 'success');

            } catch (error) {
                console.error('Download failed:', error);
                showNotification('فشل التحميل. جاري المحاولة بطريقة أخرى...', 'error');

                // Try manual canvas method
                setTimeout(() => {
                    downloadWithManualCanvas();
                }, 1000);

            } finally {
                // Hide loading indicator and restore controls
                loadingIndicator.style.display = 'none';
                const downloadControls = document.querySelector('.download-controls');
                if (downloadControls) {
                    downloadControls.style.display = 'flex';
                }
            }
        }

        // Instagram Stories Optimized Download
        async function downloadInstagramStory() {
            if (!checkHtml2Canvas()) {
                loadHtml2Canvas().then(() => {
                    setTimeout(downloadInstagramStory, 1000);
                });
                return;
            }

            const loadingIndicator = document.getElementById('loadingIndicator');
            const condolenceCard = document.getElementById('condolenceCard');

            try {
                loadingIndicator.style.display = 'block';

                // Hide download controls
                const downloadControls = document.querySelector('.download-controls');
                downloadControls.style.display = 'none';

                await document.fonts.ready;

                // Simple Instagram Stories canvas
                const canvas = await html2canvas(condolenceCard, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: false,
                    backgroundColor: '#0a0a0a',
                    logging: false,
                    removeContainer: true
                });

                const link = document.createElement('a');
                link.download = 'Muslim_Ibn_Aqeel_Instagram_Story.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();

                setTimeout(() => {
                    if (link.parentNode) {
                        document.body.removeChild(link);
                    }
                }, 100);

                showNotification('تم تحميل صورة Instagram Stories بنجاح!', 'success');

            } catch (error) {
                console.error('Instagram Stories download failed:', error);
                showNotification('فشل تحميل صورة Instagram Stories. جاري المحاولة بطريقة أخرى...', 'error');
                setTimeout(() => {
                    downloadWithManualCanvas();
                }, 1000);
            } finally {
                loadingIndicator.style.display = 'none';
                const downloadControls = document.querySelector('.download-controls');
                if (downloadControls) {
                    downloadControls.style.display = 'flex';
                }
            }
        }

        // Load html2canvas library dynamically
        function loadHtml2Canvas() {
            return new Promise((resolve, reject) => {
                if (typeof html2canvas !== 'undefined') {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                script.onload = () => {
                    showNotification('تم تحميل مكتبة html2canvas بنجاح', 'success');
                    resolve();
                };
                script.onerror = () => {
                    showNotification('فشل تحميل مكتبة html2canvas', 'error');
                    reject();
                };
                document.head.appendChild(script);
            });
        }

        // Manual Canvas Method (Most Reliable)
        async function downloadWithManualCanvas() {
            const loadingIndicator = document.getElementById('loadingIndicator');

            try {
                loadingIndicator.style.display = 'block';

                const condolenceCard = document.getElementById('condolenceCard');
                const rect = condolenceCard.getBoundingClientRect();

                // Create manual canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Set canvas size (9:16 ratio)
                canvas.width = 1080;
                canvas.height = 1920;

                // Fill background
                ctx.fillStyle = '#0a0a0a';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Add gradient background
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#0a0a0a');
                gradient.addColorStop(0.5, '#1a1a2e');
                gradient.addColorStop(1, '#16213e');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Use html2canvas if available, otherwise show message
                if (typeof html2canvas !== 'undefined') {
                    const tempCanvas = await html2canvas(condolenceCard, {
                        scale: 1,
                        useCORS: true,
                        allowTaint: false,
                        backgroundColor: '#0a0a0a',
                        logging: false
                    });

                    // Draw the captured content onto our manual canvas
                    ctx.drawImage(tempCanvas, 0, 0, canvas.width, canvas.height);
                } else {
                    // Draw text manually if html2canvas fails
                    ctx.fillStyle = '#d4af37';
                    ctx.font = 'bold 48px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('مسلم بن عقيل', canvas.width/2, 400);
                    ctx.fillText('(عليه السلام)', canvas.width/2, 480);

                    ctx.fillStyle = '#90ee90';
                    ctx.font = '32px Arial';
                    ctx.fillText('سفير الإمام الحسين إلى الكوفة', canvas.width/2, 600);

                    ctx.fillStyle = '#f5f5f5';
                    ctx.font = '24px Arial';
                    ctx.fillText('في ذكرى استشهاد', canvas.width/2, 300);
                }

                // Create download link
                const link = document.createElement('a');
                link.download = 'Muslim_Ibn_Aqeel_Manual_Download.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();

                setTimeout(() => {
                    if (link.parentNode) {
                        document.body.removeChild(link);
                    }
                }, 100);

                showNotification('تم التحميل بالطريقة اليدوية بنجاح!', 'success');

            } catch (error) {
                console.error('Manual download failed:', error);
                showNotification('فشل التحميل اليدوي. جاري المحاولة بطريقة أخرى...', 'error');

                // Last resort: simple screenshot method
                setTimeout(simpleScreenshotMethod, 1000);
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }

        // Simple Screenshot Method (Last Resort)
        function simpleScreenshotMethod() {
            const loadingIndicator = document.getElementById('loadingIndicator');

            try {
                loadingIndicator.style.display = 'block';

                const condolenceCard = document.getElementById('condolenceCard');

                // Create a simple canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = 1080;
                canvas.height = 1920;

                // Fill with gradient background
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#0a0a0a');
                gradient.addColorStop(0.5, '#1a1a2e');
                gradient.addColorStop(1, '#16213e');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Add decorative border
                ctx.strokeStyle = '#d4af37';
                ctx.lineWidth = 8;
                ctx.strokeRect(40, 40, canvas.width-80, canvas.height-80);

                // Add decorative header area
                ctx.fillStyle = 'rgba(212, 175, 55, 0.1)';
                ctx.fillRect(200, 100, 680, 80);
                ctx.strokeStyle = '#d4af37';
                ctx.lineWidth = 3;
                ctx.strokeRect(200, 100, 680, 80);

                // Add Quranic verse
                ctx.fillStyle = '#90ee90';
                ctx.font = '32px Arial';
                ctx.fillText('وَبَشِّرِ الصَّابِرِينَ', canvas.width/2, 300);
                ctx.fillText('إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ', canvas.width/2, 350);

                // Add main title
                ctx.fillStyle = '#d4af37';
                ctx.font = 'bold 48px Arial';
                ctx.fillText('في ذكرى استشهاد', canvas.width/2, 450);

                // Add martyr name
                ctx.fillStyle = '#d4af37';
                ctx.font = 'bold 72px Arial';
                ctx.fillText('مُسْلِم بن عَقِيل', canvas.width/2, 600);

                ctx.fillStyle = '#90ee90';
                ctx.font = '48px Arial';
                ctx.fillText('(عليه السلام)', canvas.width/2, 680);

                // Add role description
                ctx.fillStyle = '#cccccc';
                ctx.font = '42px Arial';
                ctx.fillText('سَفِير الإمام الحُسَيْن', canvas.width/2, 800);
                ctx.fillText('إلى الكُوفَة', canvas.width/2, 860);

                // Add condolence text
                ctx.fillStyle = '#f5f5f5';
                ctx.font = '28px Arial';
                ctx.textAlign = 'right';
                const condolenceLines = [
                    'بقلوب مؤمنة بقضاء الله وقدره، نتقدم',
                    'بأحر التعازي بمناسبة ذكرى استشهاد',
                    'البطل الشهيد مسلم بن عقيل، سفير',
                    'الإمام الحسين الأمين إلى أهل الكوفة'
                ];

                condolenceLines.forEach((line, index) => {
                    ctx.fillText(line, canvas.width - 100, 1050 + (index * 50));
                });

                // Add closing prayer
                ctx.fillStyle = '#90ee90';
                ctx.font = '32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('اللهم صل على محمد وآل محمد', canvas.width/2, 1350);
                ctx.fillText('وعجل فرج وليك الحجة بن الحسن', canvas.width/2, 1400);
                ctx.fillText('وارزقنا شفاعة الحسين يوم الورود', canvas.width/2, 1450);

                // Download using Blob API for better compatibility
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.download = 'Muslim_Ibn_Aqeel_Simple_High_Quality.png';
                    link.href = url;

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Clean up
                    setTimeout(() => URL.revokeObjectURL(url), 1000);

                    showNotification('✅ تم إنشاء صورة عالية الجودة بنجاح!', 'success');
                }, 'image/png', 1.0);

            } catch (error) {
                console.error('Simple method failed:', error);
                showNotification('❌ فشل التحميل السريع. جاري المحاولة بطريقة أخرى...', 'error');

                // Try basic fallback
                basicFallbackDownload();
            } finally {
                setTimeout(() => {
                    loadingIndicator.style.display = 'none';
                }, 1000);
            }
        }

        // Basic Fallback Download
        function basicFallbackDownload() {
            try {
                // Create minimal canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = 600;
                canvas.height = 800;

                ctx.fillStyle = '#0a0a0a';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#d4af37';
                ctx.font = 'bold 40px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('مسلم بن عقيل', canvas.width/2, 300);
                ctx.fillText('(عليه السلام)', canvas.width/2, 360);

                ctx.fillStyle = '#90ee90';
                ctx.font = '24px Arial';
                ctx.fillText('سفير الإمام الحسين', canvas.width/2, 450);
                ctx.fillText('إلى الكوفة', canvas.width/2, 490);

                ctx.fillStyle = '#f5f5f5';
                ctx.font = '18px Arial';
                ctx.fillText('في ذكرى استشهاد البطل الشهيد', canvas.width/2, 600);

                const link = document.createElement('a');
                link.download = 'Muslim_Ibn_Aqeel_Basic.png';
                link.href = canvas.toDataURL('image/png');

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showNotification('تم إنشاء صورة أساسية بنجاح!', 'success');

            } catch (error) {
                console.error('All methods failed:', error);
                showNotification('❌ فشل جميع طرق التحميل. يرجى تحديث المتصفح.', 'error');
            }
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 1rem 2rem;
                border-radius: 10px;
                z-index: 3000;
                font-family: 'Amiri', serif;
                font-size: 1rem;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                direction: rtl;
                text-align: right;
                max-width: 300px;
                word-wrap: break-word;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Auto remove after 4 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
        }

        // Test Download Functionality
        function testDownload() {
            showNotification('اختبار وظيفة التحميل...', 'info');

            // Test if html2canvas is available
            if (typeof html2canvas === 'undefined') {
                showNotification('مكتبة html2canvas غير متوفرة. جاري التحميل...', 'error');
                loadHtml2Canvas().then(() => {
                    showNotification('تم تحميل المكتبة بنجاح! يمكنك الآن التحميل.', 'success');
                });
                return;
            }

            // Test canvas creation
            try {
                const testCanvas = document.createElement('canvas');
                const ctx = testCanvas.getContext('2d');
                testCanvas.width = 100;
                testCanvas.height = 100;
                ctx.fillStyle = '#d4af37';
                ctx.fillRect(0, 0, 100, 100);

                const dataURL = testCanvas.toDataURL('image/png');
                if (dataURL.startsWith('data:image/png')) {
                    showNotification('✅ وظيفة التحميل تعمل بشكل صحيح!', 'success');
                } else {
                    showNotification('❌ مشكلة في إنشاء الصور', 'error');
                }
            } catch (error) {
                showNotification('❌ خطأ في اختبار التحميل: ' + error.message, 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Test download functionality on load
            setTimeout(testDownload, 2000);

            // Ensure fonts are loaded
            document.fonts.ready.then(() => {
                console.log('Arabic fonts loaded successfully');
                showNotification('تم تحميل الخطوط العربية بنجاح', 'success');
            });

            // Add smooth animations
            const elements = document.querySelectorAll('.condolence-card > *');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                element.style.transition = 'all 0.6s ease';

                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // Add download instructions
            setTimeout(() => {
                showNotification('💡 نصيحة: استخدم "تحميل سريع" إذا لم تعمل الطرق الأخرى', 'info');
            }, 5000);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                downloadHighQualityImage();
            }
            if (e.ctrlKey && e.key === 'i') {
                e.preventDefault();
                downloadInstagramStory();
            }
        });
    </script>
</body>
</html>
