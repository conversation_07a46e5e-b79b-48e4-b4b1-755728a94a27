"""
Utility functions for the Professional Image Editor
Contains helper functions for file operations, image processing, and UI utilities
"""

import os
import json
import hashlib
from pathlib import Path
from typing import Tuple, Optional, List, Any
from PIL import Image, ImageTk
import tkinter as tk
from config import DEFAULT_SETTINGS, TEMP_DIR, SUPPORTED_FORMATS

class ImageUtils:
    """Utility functions for image operations"""
    
    @staticmethod
    def get_image_info(image_path: str) -> dict:
        """Get comprehensive information about an image file"""
        try:
            with Image.open(image_path) as img:
                file_size = os.path.getsize(image_path)
                return {
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode,
                    'file_size': file_size,
                    'file_size_mb': round(file_size / (1024 * 1024), 2),
                    'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                }
        except Exception as e:
            return {'error': str(e)}
    
    @staticmethod
    def calculate_aspect_ratio(width: int, height: int) -> float:
        """Calculate aspect ratio of image"""
        return width / height if height != 0 else 1.0
    
    @staticmethod
    def resize_to_fit(image_size: Tuple[int, int], container_size: Tuple[int, int]) -> Tuple[int, int]:
        """Calculate new size to fit image in container while maintaining aspect ratio"""
        img_width, img_height = image_size
        container_width, container_height = container_size
        
        # Calculate scaling factors
        scale_x = container_width / img_width
        scale_y = container_height / img_height
        scale = min(scale_x, scale_y)
        
        # Calculate new dimensions
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        
        return (new_width, new_height)
    
    @staticmethod
    def create_thumbnail(image_path: str, size: Tuple[int, int] = (150, 150)) -> Optional[ImageTk.PhotoImage]:
        """Create a thumbnail of the image for UI display"""
        try:
            with Image.open(image_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                return ImageTk.PhotoImage(img)
        except Exception:
            return None
    
    @staticmethod
    def generate_temp_filename(extension: str = '.png') -> str:
        """Generate a unique temporary filename"""
        import time
        timestamp = str(int(time.time() * 1000))
        return str(TEMP_DIR / f"temp_{timestamp}{extension}")

class FileUtils:
    """Utility functions for file operations"""
    
    @staticmethod
    def is_supported_format(file_path: str, format_type: str = 'input') -> bool:
        """Check if file format is supported"""
        extension = Path(file_path).suffix.lower()
        return extension in SUPPORTED_FORMATS.get(format_type, [])
    
    @staticmethod
    def get_safe_filename(filename: str) -> str:
        """Generate a safe filename by removing invalid characters"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename
    
    @staticmethod
    def ensure_extension(filename: str, default_ext: str = '.png') -> str:
        """Ensure filename has an extension"""
        if not Path(filename).suffix:
            filename += default_ext
        return filename
    
    @staticmethod
    def get_unique_filename(file_path: str) -> str:
        """Generate unique filename if file already exists"""
        path = Path(file_path)
        if not path.exists():
            return str(path)
        
        counter = 1
        while True:
            new_name = f"{path.stem}_{counter}{path.suffix}"
            new_path = path.parent / new_name
            if not new_path.exists():
                return str(new_path)
            counter += 1
    
    @staticmethod
    def calculate_file_hash(file_path: str) -> str:
        """Calculate MD5 hash of file for comparison"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""

class SettingsManager:
    """Manage application settings"""
    
    def __init__(self, settings_file: str = "settings.json"):
        self.settings_file = Path(settings_file)
        self.settings = DEFAULT_SETTINGS.copy()
        self.load_settings()
    
    def load_settings(self):
        """Load settings from file"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.settings.update(saved_settings)
        except Exception as e:
            print(f"Error loading settings: {e}")
    
    def save_settings(self):
        """Save settings to file"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def get(self, key: str, default=None):
        """Get setting value"""
        return self.settings.get(key, default)
    
    def set(self, key: str, value: Any):
        """Set setting value"""
        self.settings[key] = value
        self.save_settings()

class UIUtils:
    """Utility functions for UI operations"""
    
    @staticmethod
    def center_window(window: tk.Tk, width: int, height: int):
        """Center window on screen"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @staticmethod
    def create_tooltip(widget: tk.Widget, text: str):
        """Create tooltip for widget"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            
            label = tk.Label(tooltip, text=text, background="lightyellow",
                           relief="solid", borderwidth=1, font=("Arial", 8))
            label.pack()
            
            widget.tooltip = tooltip
        
        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    @staticmethod
    def validate_numeric_input(value: str, min_val: float = None, max_val: float = None) -> bool:
        """Validate numeric input"""
        try:
            num_val = float(value)
            if min_val is not None and num_val < min_val:
                return False
            if max_val is not None and num_val > max_val:
                return False
            return True
        except ValueError:
            return False

class HistoryManager:
    """Manage undo/redo history"""
    
    def __init__(self, max_steps: int = 50):
        self.max_steps = max_steps
        self.history = []
        self.current_index = -1
    
    def add_state(self, state: Any):
        """Add new state to history"""
        # Remove any states after current index
        self.history = self.history[:self.current_index + 1]
        
        # Add new state
        self.history.append(state)
        self.current_index += 1
        
        # Limit history size
        if len(self.history) > self.max_steps:
            self.history.pop(0)
            self.current_index -= 1
    
    def can_undo(self) -> bool:
        """Check if undo is possible"""
        return self.current_index > 0
    
    def can_redo(self) -> bool:
        """Check if redo is possible"""
        return self.current_index < len(self.history) - 1
    
    def undo(self) -> Optional[Any]:
        """Get previous state"""
        if self.can_undo():
            self.current_index -= 1
            return self.history[self.current_index]
        return None
    
    def redo(self) -> Optional[Any]:
        """Get next state"""
        if self.can_redo():
            self.current_index += 1
            return self.history[self.current_index]
        return None
    
    def clear(self):
        """Clear history"""
        self.history.clear()
        self.current_index = -1
