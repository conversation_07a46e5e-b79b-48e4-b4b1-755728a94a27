/**
 * Arabic Image Enhancer - Professional Image Enhancement Tool
 * Developed by AliToucan for Arabic-speaking users
 */

class ArabicImageEnhancer {
    constructor() {
        this.originalCanvas = null;
        this.enhancedCanvas = null;
        this.originalImageData = null;
        this.currentFile = null;
        this.isProcessing = false;

        // Enhancement settings
        this.settings = {
            brightness: 0,
            contrast: 0,
            saturation: 0,
            sharpness: 100
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.hideLoadingScreen();
        this.setupDragAndDrop();
        this.setupSliders();
    }

    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loadingScreen');
            loadingScreen.classList.add('hidden');
        }, 1000);
    }

    setupEventListeners() {
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        uploadArea.addEventListener('click', () => fileInput.click());
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => this.highlight(uploadArea), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => this.unhighlight(uploadArea), false);
        });

        uploadArea.addEventListener('drop', (e) => this.handleDrop(e), false);
    }

    setupSliders() {
        const sliders = ['brightness', 'contrast', 'saturation', 'sharpness'];

        sliders.forEach(slider => {
            const sliderElement = document.getElementById(`${slider}Slider`);
            const valueElement = document.getElementById(`${slider}Value`);

            sliderElement.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                this.settings[slider] = value;
                valueElement.textContent = value + (slider === 'sharpness' ? '%' : '');
                this.applyEnhancements();
            });
        });
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlight(element) {
        element.classList.add('dragover');
    }

    unhighlight(element) {
        element.classList.remove('dragover');
    }

    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        this.handleFiles(files);
    }

    handleFileSelect(e) {
        const files = e.target.files;
        this.handleFiles(files);
    }

    handleFiles(files) {
        if (files.length === 0) return;

        const file = files[0];

        // Validate file type
        if (!this.isValidImageFile(file)) {
            this.showError('يرجى اختيار ملف صورة صالح (JPG, PNG, WebP, GIF, BMP)');
            return;
        }

        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            this.showError('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 10 ميجابايت');
            return;
        }

        this.currentFile = file;
        this.loadImage(file);
    }

    isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/bmp'];
        return validTypes.includes(file.type);
    }

    loadImage(file) {
        const reader = new FileReader();

        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.processImage(img);
                this.showImagePreview();
                this.showControls();
                this.updateUploadArea();
            };
            img.src = e.target.result;
        };

        reader.readAsDataURL(file);
    }

    processImage(img) {
        // Create canvases
        this.originalCanvas = document.createElement('canvas');
        this.enhancedCanvas = document.createElement('canvas');

        const originalCtx = this.originalCanvas.getContext('2d');
        const enhancedCtx = this.enhancedCanvas.getContext('2d');

        // Set canvas dimensions
        const maxWidth = 800;
        const maxHeight = 600;
        let { width, height } = this.calculateDimensions(img.width, img.height, maxWidth, maxHeight);

        this.originalCanvas.width = width;
        this.originalCanvas.height = height;
        this.enhancedCanvas.width = width;
        this.enhancedCanvas.height = height;

        // Draw original image
        originalCtx.drawImage(img, 0, 0, width, height);
        enhancedCtx.drawImage(img, 0, 0, width, height);

        // Store original image data
        this.originalImageData = originalCtx.getImageData(0, 0, width, height);

        // Update preview images
        this.updatePreviewImages();
    }

    calculateDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
        let width = originalWidth;
        let height = originalHeight;

        if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
        }

        if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
        }

        return { width: Math.round(width), height: Math.round(height) };
    }

    updatePreviewImages() {
        const originalImg = document.getElementById('originalImage');
        const enhancedImg = document.getElementById('enhancedImage');

        originalImg.src = this.originalCanvas.toDataURL();
        enhancedImg.src = this.enhancedCanvas.toDataURL();
    }

    applyEnhancements() {
        if (!this.originalImageData || this.isProcessing) return;

        this.isProcessing = true;

        // Use requestAnimationFrame for smooth performance
        requestAnimationFrame(() => {
            const enhancedCtx = this.enhancedCanvas.getContext('2d');
            const imageData = new ImageData(
                new Uint8ClampedArray(this.originalImageData.data),
                this.originalImageData.width,
                this.originalImageData.height
            );

            // Apply enhancements
            this.applyBrightness(imageData, this.settings.brightness);
            this.applyContrast(imageData, this.settings.contrast);
            this.applySaturation(imageData, this.settings.saturation);

            enhancedCtx.putImageData(imageData, 0, 0);

            // Apply sharpness (requires different approach)
            if (this.settings.sharpness !== 100) {
                this.applySharpness(enhancedCtx, this.settings.sharpness);
            }

            // Update enhanced preview
            document.getElementById('enhancedImage').src = this.enhancedCanvas.toDataURL();

            this.isProcessing = false;
        });
    }

    applyBrightness(imageData, brightness) {
        const data = imageData.data;
        const factor = brightness * 2.55; // Convert to 0-255 range

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.max(0, Math.min(255, data[i] + factor));     // Red
            data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + factor)); // Green
            data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + factor)); // Blue
        }
    }

    applyContrast(imageData, contrast) {
        const data = imageData.data;
        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.max(0, Math.min(255, factor * (data[i] - 128) + 128));
            data[i + 1] = Math.max(0, Math.min(255, factor * (data[i + 1] - 128) + 128));
            data[i + 2] = Math.max(0, Math.min(255, factor * (data[i + 2] - 128) + 128));
        }
    }

    applySaturation(imageData, saturation) {
        const data = imageData.data;
        const factor = saturation / 100;

        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            const gray = 0.299 * r + 0.587 * g + 0.114 * b;

            data[i] = Math.max(0, Math.min(255, gray + factor * (r - gray)));
            data[i + 1] = Math.max(0, Math.min(255, gray + factor * (g - gray)));
            data[i + 2] = Math.max(0, Math.min(255, gray + factor * (b - gray)));
        }
    }

    applySharpness(ctx, sharpness) {
        const factor = sharpness / 100;
        if (factor === 1) return;

        // Simple sharpening using CSS filter as fallback
        ctx.filter = `contrast(${factor * 100}%) brightness(${factor * 100}%)`;
        ctx.drawImage(this.enhancedCanvas, 0, 0);
        ctx.filter = 'none';
    }

    showImagePreview() {
        document.getElementById('imagePreview').style.display = 'block';
    }

    showControls() {
        document.getElementById('controlsPanel').style.display = 'block';
    }

    updateUploadArea() {
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.classList.add('has-file');

        const uploadText = uploadArea.querySelector('.upload-text');
        uploadText.innerHTML = `
            <strong>تم رفع الصورة بنجاح!</strong><br>
            ${this.currentFile.name}<br>
            <small>انقر لاختيار صورة أخرى</small>
        `;
    }

    showError(message) {
        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--danger-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: var(--radius-medium);
            box-shadow: var(--shadow-heavy);
            z-index: 10000;
            animation: slideInRight 0.3s ease;
        `;
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            ${message}
        `;

        document.body.appendChild(errorDiv);

        // Remove after 5 seconds
        setTimeout(() => {
            errorDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(errorDiv);
            }, 300);
        }, 5000);
    }

    // Download functionality
    downloadImage(format = 'jpeg', quality = 0.9) {
        if (!this.enhancedCanvas) return;

        const link = document.createElement('a');
        link.download = `enhanced_image_${Date.now()}.${format === 'jpeg' ? 'jpg' : format}`;
        link.href = this.enhancedCanvas.toDataURL(`image/${format}`, quality);
        link.click();
    }

    // Reset all enhancements
    resetEnhancements() {
        this.settings = {
            brightness: 0,
            contrast: 0,
            saturation: 0,
            sharpness: 100
        };

        // Update sliders
        Object.keys(this.settings).forEach(key => {
            const slider = document.getElementById(`${key}Slider`);
            const value = document.getElementById(`${key}Value`);
            slider.value = this.settings[key];
            value.textContent = this.settings[key] + (key === 'sharpness' ? '%' : '');
        });

        this.applyEnhancements();
    }

    // Apply preset filters
    applyPreset(presetName) {
        const presets = {
            portrait: {
                brightness: 10,
                contrast: 15,
                saturation: 5,
                sharpness: 120
            },
            landscape: {
                brightness: 5,
                contrast: 20,
                saturation: 15,
                sharpness: 110
            },
            social: {
                brightness: 15,
                contrast: 25,
                saturation: 20,
                sharpness: 130
            },
            vintage: {
                brightness: -10,
                contrast: 10,
                saturation: -20,
                sharpness: 90
            }
        };

        if (presets[presetName]) {
            this.settings = { ...presets[presetName] };

            // Update sliders
            Object.keys(this.settings).forEach(key => {
                const slider = document.getElementById(`${key}Slider`);
                const value = document.getElementById(`${key}Value`);
                slider.value = this.settings[key];
                value.textContent = this.settings[key] + (key === 'sharpness' ? '%' : '');
            });

            this.applyEnhancements();
            this.showSuccess(`تم تطبيق فلتر ${this.getPresetNameInArabic(presetName)} بنجاح!`);
        }
    }

    getPresetNameInArabic(presetName) {
        const names = {
            portrait: 'الصورة الشخصية',
            landscape: 'المنظر الطبيعي',
            social: 'وسائل التواصل',
            vintage: 'الكلاسيكي'
        };
        return names[presetName] || presetName;
    }

    // Show success notification
    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-notification';
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--success-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: var(--radius-medium);
            box-shadow: var(--shadow-heavy);
            z-index: 10000;
            animation: slideInRight 0.3s ease;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
        `;
        successDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            ${message}
        `;

        document.body.appendChild(successDiv);

        // Remove after 3 seconds
        setTimeout(() => {
            successDiv.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (document.body.contains(successDiv)) {
                    document.body.removeChild(successDiv);
                }
            }, 300);
        }, 3000);
    }

    // Batch processing for multiple images
    processBatch(files) {
        if (files.length === 0) return;

        this.showSuccess(`بدء معالجة ${files.length} صورة...`);

        Array.from(files).forEach((file, index) => {
            setTimeout(() => {
                this.processImageForBatch(file, index + 1, files.length);
            }, index * 1000); // Process one image per second
        });
    }

    processImageForBatch(file, current, total) {
        const reader = new FileReader();

        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                // Create temporary canvas for batch processing
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                // Apply current settings
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                this.applyBrightness(imageData, this.settings.brightness);
                this.applyContrast(imageData, this.settings.contrast);
                this.applySaturation(imageData, this.settings.saturation);
                ctx.putImageData(imageData, 0, 0);

                // Download processed image
                const link = document.createElement('a');
                const fileName = file.name.replace(/\.[^/.]+$/, '');
                link.download = `${fileName}_enhanced.jpg`;
                link.href = canvas.toDataURL('image/jpeg', 0.9);
                link.click();

                if (current === total) {
                    this.showSuccess('تم الانتهاء من معالجة جميع الصور!');
                }
            };
            img.src = e.target.result;
        };

        reader.readAsDataURL(file);
    }

    // Advanced noise reduction (simplified version)
    applyNoiseReduction(imageData) {
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;

        // Simple blur filter for noise reduction
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const idx = (y * width + x) * 4;

                // Average with surrounding pixels
                for (let c = 0; c < 3; c++) {
                    let sum = 0;
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const neighborIdx = ((y + dy) * width + (x + dx)) * 4 + c;
                            sum += data[neighborIdx];
                        }
                    }
                    data[idx + c] = sum / 9;
                }
            }
        }
    }

    // Image upscaling (basic interpolation)
    upscaleImage(factor = 2) {
        if (!this.enhancedCanvas) return;

        const originalCanvas = this.enhancedCanvas;
        const newWidth = originalCanvas.width * factor;
        const newHeight = originalCanvas.height * factor;

        const newCanvas = document.createElement('canvas');
        const newCtx = newCanvas.getContext('2d');

        newCanvas.width = newWidth;
        newCanvas.height = newHeight;

        // Use built-in scaling with smoothing
        newCtx.imageSmoothingEnabled = true;
        newCtx.imageSmoothingQuality = 'high';
        newCtx.drawImage(originalCanvas, 0, 0, newWidth, newHeight);

        // Update enhanced canvas
        this.enhancedCanvas = newCanvas;
        document.getElementById('enhancedImage').src = newCanvas.toDataURL();

        this.showSuccess(`تم تكبير الصورة إلى ${newWidth}x${newHeight} بكسل!`);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.imageEnhancer = new ArabicImageEnhancer();
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    .error-notification {
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
        direction: rtl;
        text-align: right;
    }
`;
document.head.appendChild(style);
