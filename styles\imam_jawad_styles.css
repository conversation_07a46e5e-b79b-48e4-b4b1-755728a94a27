/* ===== IMAM MUHAMMAD AL-JAWAD (AS) CONDOLENCE WEBSITE STYLES ===== */

/* ===== CSS RESET AND BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Amiri', 'Amiri-Local', 'Noto Sans Arabic', 'NotoSansArabic-Local', serif;
    line-height: 1.8;
    color: #f5f5f5;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Amiri', 'Amiri-Local', serif;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* ===== CONTAINER AND LAYOUT ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ===== HEADER STYLES ===== */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 2px solid #d4af37;
}

.main-header.scrolled {
    background: rgba(0, 0, 0, 0.98);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.brand-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.brand-logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #d4af37;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    color: #f5f5f5;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
    position: relative;
}

.main-nav a:hover {
    color: #d4af37;
}

.main-nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 0;
    height: 2px;
    background: #d4af37;
    transition: width 0.3s ease;
}

.main-nav a:hover::after {
    width: 100%;
}

/* ===== HERO SECTION ===== */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
}

.main-title {
    font-size: 4rem;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.title-line {
    display: block;
    font-size: 2.5rem;
    color: #f5f5f5;
    margin-bottom: 0.5rem;
}

.imam-name {
    display: block;
    font-size: 4.5rem;
    color: #d4af37;
    font-weight: 700;
    margin: 1rem 0;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
}

.honorific {
    display: block;
    font-size: 2rem;
    color: #90ee90;
    margin-top: 0.5rem;
}

.hero-subtitle {
    font-size: 1.8rem;
    color: #cccccc;
    margin-bottom: 2rem;
}

.date-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    font-size: 1.3rem;
    color: #d4af37;
}

.separator {
    color: #666;
}

/* ===== GEOMETRIC PATTERN ===== */
.geometric-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    background-image:
        radial-gradient(circle at 25% 25%, #d4af37 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, #d4af37 2px, transparent 2px);
    background-size: 100px 100px;
    background-position: 0 0, 50px 50px;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* ===== MAIN CONTENT ===== */
.main-content {
    padding-top: 2rem;
}

/* ===== SECTION STYLES ===== */
section {
    padding: 4rem 0;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

section.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 1rem;
    position: relative;
}

.title-decoration {
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #d4af37, #90ee90);
    margin: 0 auto;
    border-radius: 2px;
}

/* ===== CONDOLENCE SECTION ===== */
.condolence-section {
    background: rgba(0, 0, 0, 0.3);
    border-top: 3px solid #d4af37;
    border-bottom: 3px solid #d4af37;
}

.condolence-content {
    max-width: 900px;
    margin: 0 auto;
}

.opening-verse {
    font-size: 1.8rem;
    text-align: center;
    color: #90ee90;
    font-weight: 600;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(144, 238, 144, 0.1);
    border-radius: 15px;
    border: 2px solid rgba(144, 238, 144, 0.3);
}

.main-condolence p {
    font-size: 1.3rem;
    line-height: 2;
    margin-bottom: 2rem;
    text-align: justify;
}

.imam-full-name {
    text-align: center;
    font-size: 2.5rem;
    color: #d4af37;
    margin: 3rem 0;
    padding: 2rem;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 15px;
    border: 2px solid rgba(212, 175, 55, 0.3);
}

.peace-be-upon {
    display: block;
    font-size: 1.5rem;
    color: #90ee90;
    margin-top: 0.5rem;
}

.closing-prayer {
    font-size: 1.4rem;
    text-align: center;
    color: #90ee90;
    font-weight: 600;
    padding: 2rem;
    background: rgba(144, 238, 144, 0.1);
    border-radius: 15px;
    border: 2px solid rgba(144, 238, 144, 0.3);
    margin-top: 3rem;
}

/* ===== BIOGRAPHY SECTION ===== */
.biography-section {
    background: rgba(26, 26, 46, 0.5);
}

.bio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.bio-card {
    background: rgba(0, 0, 0, 0.4);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    transition: all 0.3s ease;
    text-align: center;
}

.bio-card:hover {
    transform: translateY(-10px);
    border-color: #d4af37;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.2);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.bio-card h3 {
    color: #d4af37;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.bio-card p {
    line-height: 1.8;
    text-align: justify;
}

/* ===== VERSES SECTION ===== */
.verses-section {
    background: rgba(22, 33, 62, 0.5);
}

.verses-content {
    display: grid;
    gap: 2rem;
    margin-top: 2rem;
}

.verse-card, .hadith-card {
    background: rgba(0, 0, 0, 0.4);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(144, 238, 144, 0.3);
    transition: all 0.3s ease;
}

.verse-card:hover, .hadith-card:hover {
    transform: translateY(-5px);
    border-color: #90ee90;
    box-shadow: 0 10px 30px rgba(144, 238, 144, 0.2);
}

.verse-header, .hadith-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(144, 238, 144, 0.3);
}

.verse-header h3, .hadith-header h3 {
    color: #d4af37;
    font-size: 1.5rem;
}

.verse-reference, .hadith-source {
    color: #90ee90;
    font-size: 1rem;
    font-weight: 600;
}

.arabic-verse, .arabic-hadith {
    font-size: 1.8rem;
    line-height: 2.2;
    color: #90ee90;
    text-align: center;
    margin-bottom: 1rem;
    font-weight: 600;
}

.verse-translation {
    font-size: 1.2rem;
    color: #cccccc;
    text-align: center;
    font-style: italic;
}

/* ===== HISTORY SECTION ===== */
.history-section {
    background: rgba(0, 0, 0, 0.3);
}

.history-content {
    max-width: 900px;
    margin: 0 auto;
}

.timeline {
    position: relative;
    margin: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #d4af37, #90ee90);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin: 3rem 0;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.timeline-item:nth-child(odd) {
    flex-direction: row-reverse;
}

.timeline-date {
    background: #d4af37;
    color: #000;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 1.2rem;
    min-width: 120px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.timeline-content {
    flex: 1;
    background: rgba(0, 0, 0, 0.4);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(212, 175, 55, 0.3);
}

.timeline-content h3 {
    color: #d4af37;
    margin-bottom: 1rem;
}

.historical-note {
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(144, 238, 144, 0.1);
    border-radius: 15px;
    border: 2px solid rgba(144, 238, 144, 0.3);
}

.historical-note h3 {
    color: #90ee90;
    margin-bottom: 1rem;
    text-align: center;
}

.historical-note p {
    line-height: 2;
    text-align: justify;
}

/* ===== FOOTER STYLES ===== */
.main-footer {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    border-top: 3px solid #d4af37;
    padding: 3rem 0 2rem;
    margin-top: 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    align-items: center;
    text-align: center;
}

.footer-brand {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.footer-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.footer-brand-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: #d4af37;
}

.footer-prayer {
    font-size: 1.3rem;
    color: #90ee90;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-date {
    color: #d4af37;
    font-weight: 600;
}

.footer-links p {
    margin-bottom: 0.5rem;
    color: #cccccc;
}

.footer-note {
    font-style: italic;
    color: #999;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .main-nav ul {
        gap: 1rem;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .imam-name {
        font-size: 3rem;
    }

    .title-line {
        font-size: 1.8rem;
    }

    .honorific {
        font-size: 1.5rem;
    }

    .hero-subtitle {
        font-size: 1.3rem;
    }

    .date-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .bio-grid {
        grid-template-columns: 1fr;
    }

    .timeline::before {
        right: 20px;
    }

    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
        padding-right: 3rem;
    }

    .timeline-item:nth-child(odd) {
        flex-direction: column;
    }

    .timeline-date {
        min-width: auto;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .main-title {
        font-size: 2rem;
    }

    .imam-name {
        font-size: 2.5rem;
    }

    .opening-verse, .arabic-verse, .arabic-hadith {
        font-size: 1.4rem;
    }

    .imam-full-name {
        font-size: 2rem;
    }

    .bio-card, .verse-card, .hadith-card, .timeline-content {
        padding: 1.5rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.imam-name {
    animation: pulse 3s ease-in-out infinite;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .main-header,
    .main-nav,
    .geometric-pattern {
        display: none;
    }

    body {
        background: white;
        color: black;
    }

    .hero-section {
        background: none;
        min-height: auto;
        padding: 2rem 0;
    }

    section {
        page-break-inside: avoid;
        opacity: 1;
        transform: none;
    }
}
