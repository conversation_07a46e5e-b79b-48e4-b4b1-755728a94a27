#!/usr/bin/env python3
"""
Professional GDP Analysis Excel Creator
Creates a comprehensive, professionally formatted Excel workbook with charts and analysis
"""

import csv
import os
from datetime import datetime

def create_professional_excel():
    """Create a professional Excel file with advanced formatting"""
    
    # Check if CSV exists
    csv_file = 'GDP_Analysis_Comprehensive.csv'
    if not os.path.exists(csv_file):
        print(f"Error: {csv_file} not found!")
        return
    
    # Read CSV data
    data = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            data.append(row)
    
    # Create HTML file that can be opened in Excel with full formatting
    html_content = create_formatted_html(data)
    
    # Save as HTML (Excel can open and save as .xlsx)
    with open('GDP_Analysis_Professional.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("Professional Excel file created: GDP_Analysis_Professional.html")
    print("This file can be opened in Excel and saved as .xlsx format")
    
    # Create additional analysis files
    create_charts_html(data)
    create_summary_analysis(data)

def create_formatted_html(data):
    """Create HTML with Excel-compatible formatting"""
    
    html = """<!DOCTYPE html>
<html dir="ltr" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GDP Analysis - Professional Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
            direction: ltr;
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        
        .header h2 {
            margin: 5px 0 0 0;
            font-size: 18px;
            opacity: 0.9;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .data-table th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            font-size: 11px;
            border: 1px solid #34495e;
            position: sticky;
            top: 0;
        }
        
        .data-table td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11px;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tr:hover {
            background-color: #e3f2fd;
            transition: background-color 0.3s;
        }
        
        .rank-cell {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            font-weight: bold;
        }
        
        .country-cell {
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .arabic-text {
            font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', sans-serif;
            direction: rtl;
            text-align: right;
            color: #27ae60;
        }
        
        .gdp-cell {
            font-weight: bold;
            color: #2980b9;
        }
        
        .growth-positive {
            color: #27ae60;
            font-weight: bold;
        }
        
        .growth-negative {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .per-capita-high {
            background-color: #d5f4e6;
            color: #27ae60;
            font-weight: bold;
        }
        
        .per-capita-medium {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .per-capita-low {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .summary-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .summary-title {
            color: #2c3e50;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .key-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .footer {
            text-align: center;
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 30px;
            padding: 20px;
            border-top: 1px solid #ecf0f1;
        }
        
        @media print {
            body { margin: 0; }
            .header { break-inside: avoid; }
            .data-table { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌍 تحليل الناتج المحلي الإجمالي العالمي - Global GDP Analysis</h1>
        <h2>أكبر 20 اقتصاد في العالم 2024-2025 - Top 20 World Economies</h2>
    </div>
    
    <div class="summary-box">
        <div class="summary-title">📊 الإحصائيات الرئيسية - Key Statistics</div>
        <div class="key-stats">
            <div class="stat-card">
                <div class="stat-value">$74.5T</div>
                <div class="stat-label">إجمالي ناتج أكبر 10 دول<br>Total GDP Top 10</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">2.7%</div>
                <div class="stat-label">متوسط معدل النمو<br>Average Growth Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">4.6B</div>
                <div class="stat-label">إجمالي السكان<br>Total Population</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">6.4%</div>
                <div class="stat-label">أسرع نمو (الهند)<br>Fastest Growth (India)</div>
            </div>
        </div>
    </div>
"""
    
    # Add the data table
    html += '<table class="data-table">\n'
    
    # Headers
    if data:
        html += '<thead><tr>'
        headers = data[0]
        for i, header in enumerate(headers):
            if i == 0:  # Rank
                html += f'<th style="width: 50px;">{header}<br>الترتيب</th>'
            elif i == 1:  # Country English
                html += f'<th style="width: 120px;">{header}<br>الدولة</th>'
            elif i == 2:  # Country Arabic
                html += f'<th style="width: 120px;">{header}<br>Country</th>'
            elif 'GDP' in header and 'Per Capita' not in header:
                year = header.split()[-3] if len(header.split()) > 3 else ''
                html += f'<th style="width: 90px;">GDP {year}<br>(Billion $)</th>'
            elif 'Per Capita' in header:
                html += f'<th style="width: 90px;">GDP Per Capita<br>نصيب الفرد ($)</th>'
            elif 'Growth' in header:
                html += f'<th style="width: 80px;">Growth Rate<br>معدل النمو (%)</th>'
            elif 'Population' in header:
                html += f'<th style="width: 80px;">Population<br>السكان (M)</th>'
            else:
                html += f'<th style="width: 70px;">{header}</th>'
        html += '</tr></thead>\n'
        
        # Data rows
        html += '<tbody>'
        for row_idx, row in enumerate(data[1:], 1):
            html += '<tr>'
            for col_idx, cell in enumerate(row):
                css_class = ""
                formatted_value = cell
                
                if col_idx == 0:  # Rank
                    css_class = "rank-cell"
                elif col_idx == 1:  # Country English
                    css_class = "country-cell"
                elif col_idx == 2:  # Country Arabic
                    css_class = "arabic-text"
                elif col_idx >= 3 and col_idx <= 9:  # GDP columns
                    css_class = "gdp-cell"
                    try:
                        value = float(cell)
                        formatted_value = f"${value:,.1f}B"
                    except:
                        formatted_value = cell
                elif col_idx == 11:  # GDP Per Capita
                    try:
                        value = float(cell)
                        if value > 50000:
                            css_class = "per-capita-high"
                        elif value > 20000:
                            css_class = "per-capita-medium"
                        else:
                            css_class = "per-capita-low"
                        formatted_value = f"${value:,.0f}"
                    except:
                        formatted_value = cell
                elif col_idx == 12:  # Growth Rate
                    try:
                        value = float(cell)
                        css_class = "growth-positive" if value > 0 else "growth-negative"
                        formatted_value = f"{value}%"
                    except:
                        formatted_value = cell
                elif col_idx >= 13:  # Sector percentages
                    try:
                        value = float(cell)
                        formatted_value = f"{value}%"
                    except:
                        formatted_value = cell
                
                html += f'<td class="{css_class}">{formatted_value}</td>'
            html += '</tr>\n'
        html += '</tbody>'
    
    html += '</table>\n'
    
    # Footer
    html += f"""
    <div class="footer">
        <p><strong>مصادر البيانات - Data Sources:</strong> صندوق النقد الدولي (IMF) • البنك الدولي (World Bank) • ستاتيستا (Statista)</p>
        <p><strong>آخر تحديث - Last Updated:</strong> {datetime.now().strftime('%Y-%m-%d')} | <strong>العملة - Currency:</strong> الدولار الأمريكي الحالي - Current USD</p>
        <p><strong>ملاحظة:</strong> أرقام 2025 هي توقعات صندوق النقد الدولي • 2025 figures are IMF projections</p>
    </div>
</body>
</html>"""
    
    return html

def create_charts_html(data):
    """Create HTML file with interactive charts"""
    
    charts_html = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>GDP Analysis Charts</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .chart-container { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .chart-title { text-align: center; color: #2c3e50; font-size: 18px; font-weight: bold; margin-bottom: 20px; }
        canvas { max-height: 400px; }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #2c3e50;">📈 GDP Analysis Charts - الرسوم البيانية لتحليل الناتج المحلي</h1>
    
    <div class="chart-container">
        <div class="chart-title">أكبر 10 اقتصادات في العالم 2024 - Top 10 Economies by GDP 2024</div>
        <canvas id="gdpChart"></canvas>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">نصيب الفرد من الناتج المحلي - GDP Per Capita Comparison</div>
        <canvas id="perCapitaChart"></canvas>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">معدلات النمو الاقتصادي - Economic Growth Rates 2024</div>
        <canvas id="growthChart"></canvas>
    </div>

    <script>
        // GDP Chart
        const gdpCtx = document.getElementById('gdpChart').getContext('2d');
        new Chart(gdpCtx, {
            type: 'bar',
            data: {
                labels: ['USA', 'China', 'Germany', 'India', 'Japan', 'UK', 'France', 'Italy', 'Canada', 'Brazil'],
                datasets: [{
                    label: 'GDP (Trillion USD)',
                    data: [29.18, 18.75, 4.66, 3.91, 4.03, 3.64, 3.16, 2.37, 2.24, 2.17],
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6', '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#2ecc71']
                }]
            },
            options: {
                responsive: true,
                plugins: { legend: { display: false } },
                scales: { y: { beginAtZero: true, title: { display: true, text: 'GDP (Trillion USD)' } } }
            }
        });
        
        // Per Capita Chart
        const perCapitaCtx = document.getElementById('perCapitaChart').getContext('2d');
        new Chart(perCapitaCtx, {
            type: 'horizontalBar',
            data: {
                labels: ['Netherlands', 'Australia', 'Canada', 'Germany', 'UK', 'France', 'Italy', 'Japan', 'Spain', 'South Korea'],
                datasets: [{
                    label: 'GDP Per Capita (USD)',
                    data: [69726, 67564, 57334, 55133, 53844, 46228, 40272, 32656, 36030, 36167],
                    backgroundColor: '#2ecc71'
                }]
            },
            options: {
                responsive: true,
                plugins: { legend: { display: false } },
                scales: { x: { beginAtZero: true, title: { display: true, text: 'GDP Per Capita (USD)' } } }
            }
        });
        
        // Growth Chart
        const growthCtx = document.getElementById('growthChart').getContext('2d');
        new Chart(growthCtx, {
            type: 'bar',
            data: {
                labels: ['India', 'Indonesia', 'China', 'Turkey', 'Russia', 'USA', 'Poland', 'South Korea', 'Spain', 'Canada'],
                datasets: [{
                    label: 'Growth Rate (%)',
                    data: [6.4, 5.0, 5.2, 4.5, 3.6, 2.8, 2.9, 2.6, 2.4, 1.2],
                    backgroundColor: '#27ae60'
                }]
            },
            options: {
                responsive: true,
                plugins: { legend: { display: false } },
                scales: { y: { beginAtZero: true, title: { display: true, text: 'Growth Rate (%)' } } }
            }
        });
    </script>
</body>
</html>"""
    
    with open('GDP_Charts_Interactive.html', 'w', encoding='utf-8') as f:
        f.write(charts_html)
    
    print("Interactive charts created: GDP_Charts_Interactive.html")

def create_summary_analysis(data):
    """Create detailed summary analysis"""
    
    summary = f"""
# 📊 GDP ANALYSIS COMPREHENSIVE REPORT
# تقرير تحليل الناتج المحلي الإجمالي الشامل

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🏆 TOP ECONOMIC PERFORMERS - أفضل الأداءات الاقتصادية

### Largest Economies (GDP 2024):
1. 🇺🇸 United States: $29.18 Trillion
2. 🇨🇳 China: $18.75 Trillion  
3. 🇩🇪 Germany: $4.66 Trillion
4. 🇮🇳 India: $3.91 Trillion
5. 🇯🇵 Japan: $4.03 Trillion

### Fastest Growing Economies:
1. 🇮🇳 India: 6.4% growth
2. 🇮🇩 Indonesia: 5.0% growth
3. 🇨🇳 China: 5.2% growth
4. 🇹🇷 Turkey: 4.5% growth
5. 🇷🇺 Russia: 3.6% growth

### Highest GDP Per Capita:
1. 🇳🇱 Netherlands: $69,726
2. 🇦🇺 Australia: $67,564
3. 🇨🇦 Canada: $57,334
4. 🇩🇪 Germany: $55,133
5. 🇬🇧 United Kingdom: $53,844

## 📈 ECONOMIC INSIGHTS - رؤى اقتصادية

### Regional Distribution:
- **North America**: 31% of global GDP (US, Canada, Mexico)
- **Asia-Pacific**: 29% of global GDP (China, Japan, India, Australia)
- **Europe**: 18% of global GDP (Germany, UK, France, Italy)
- **Others**: 22% of global GDP

### Economic Structure Analysis:
- **Services-Dominated**: US (80%), UK (81.8%), Netherlands (80.3%)
- **Manufacturing-Heavy**: China (40.5% industry), Germany (30.7%)
- **Emerging Markets**: Strong growth in India, Indonesia, Turkey

### Key Trends:
- Asia-Pacific region showing strongest growth momentum
- Developed economies focusing on services and high-tech industries
- Emerging markets driving global economic expansion
- Digital transformation accelerating across all regions

## 📊 DATA METHODOLOGY - منهجية البيانات

### Sources:
- International Monetary Fund (IMF) World Economic Outlook
- World Bank World Development Indicators  
- OECD National Accounts Statistics
- Statista Economic Database

### Notes:
- All GDP figures in current USD (nominal values)
- 2025 figures are IMF projections
- Growth rates are real GDP growth (inflation-adjusted)
- Population data from UN World Population Prospects
- Sector breakdowns from World Bank structural indicators

### Calculation Methods:
- GDP Per Capita = Total GDP ÷ Population
- Growth Rate = ((Current Year GDP - Previous Year GDP) / Previous Year GDP) × 100
- Regional totals = Sum of individual country GDPs

## 🔮 FUTURE OUTLOOK - التوقعات المستقبلية

### 2025 Projections:
- Global GDP expected to reach $110+ trillion
- India likely to become 3rd largest economy
- Continued shift toward services in developed economies
- Digital economy expansion across all regions
- Sustainable development focus increasing

### Key Challenges:
- Inflation management
- Supply chain resilience
- Climate change adaptation
- Digital divide reduction
- Geopolitical stability

---
Report prepared with data from IMF, World Bank, and Statista
تم إعداد التقرير بناءً على بيانات من صندوق النقد الدولي والبنك الدولي وستاتيستا
"""
    
    with open('GDP_Comprehensive_Analysis.txt', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print("Comprehensive analysis created: GDP_Comprehensive_Analysis.txt")

if __name__ == "__main__":
    print("Creating Professional GDP Analysis Excel Files...")
    create_professional_excel()
    print("\n✅ All files created successfully!")
    print("\nFiles generated:")
    print("1. GDP_Analysis_Professional.html - Main formatted spreadsheet")
    print("2. GDP_Charts_Interactive.html - Interactive charts")
    print("3. GDP_Comprehensive_Analysis.txt - Detailed analysis")
    print("\nTo convert to Excel:")
    print("1. Open GDP_Analysis_Professional.html in Excel")
    print("2. Save as .xlsx format")
    print("3. The formatting will be preserved!")
