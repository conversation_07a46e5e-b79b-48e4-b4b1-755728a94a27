/**
 * Arabic Image Enhancer Advanced - Professional Image Enhancement Tool
 * Enhanced version with AI-powered features and advanced editing tools
 * Developed by AliToucan for Arabic-speaking users
 */

class AdvancedArabicImageEnhancer {
    constructor() {
        this.originalCanvas = null;
        this.enhancedCanvas = null;
        this.originalImageData = null;
        this.currentFile = null;
        this.isProcessing = false;
        this.history = [];
        this.historyIndex = -1;

        // Enhanced settings with new features
        this.settings = {
            brightness: 0,
            contrast: 0,
            saturation: 0,
            sharpness: 100,
            rotation: 0,
            flipHorizontal: false,
            flipVertical: false,
            cropData: null
        };

        // AI Enhancement presets
        this.aiPresets = {
            auto: { brightness: 15, contrast: 20, saturation: 10, sharpness: 120 },
            portrait: { brightness: 10, contrast: 15, saturation: 5, sharpness: 130 },
            landscape: { brightness: 5, contrast: 25, saturation: 20, sharpness: 110 },
            lowLight: { brightness: 30, contrast: 35, saturation: 15, sharpness: 140 },
            vintage: { brightness: -5, contrast: 10, saturation: -15, sharpness: 95 }
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.hideLoadingScreen();
        this.setupDragAndDrop();
        this.setupSliders();
        this.setupAdvancedFeatures();
    }

    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
            }
        }, 1000);
    }

    setupEventListeners() {
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        if (fileInput) fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        if (uploadArea) uploadArea.addEventListener('click', () => fileInput?.click());
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => this.highlight(uploadArea), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => this.unhighlight(uploadArea), false);
        });

        uploadArea.addEventListener('drop', (e) => this.handleDrop(e), false);
    }

    setupSliders() {
        const sliders = ['brightness', 'contrast', 'saturation', 'sharpness'];

        sliders.forEach(slider => {
            const sliderElement = document.getElementById(`${slider}Slider`);
            const valueElement = document.getElementById(`${slider}Value`);

            if (sliderElement && valueElement) {
                sliderElement.addEventListener('input', (e) => {
                    const value = parseInt(e.target.value);
                    this.settings[slider] = value;
                    valueElement.textContent = value + (slider === 'sharpness' ? '%' : '');
                    this.applyEnhancements();
                });
            }
        });
    }

    setupAdvancedFeatures() {
        // Setup keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'z':
                        e.preventDefault();
                        this.undo();
                        break;
                    case 'y':
                        e.preventDefault();
                        this.redo();
                        break;
                    case 's':
                        e.preventDefault();
                        this.downloadImage();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.resetEnhancements();
                        break;
                }
            }
        });
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlight(element) {
        element.classList.add('dragover');
    }

    unhighlight(element) {
        element.classList.remove('dragover');
    }

    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        this.handleFiles(files);
    }

    handleFileSelect(e) {
        const files = e.target.files;
        this.handleFiles(files);
    }

    handleFiles(files) {
        if (files.length === 0) return;

        const file = files[0];

        // Enhanced validation
        if (!this.isValidImageFile(file)) {
            this.showError('يرجى اختيار ملف صورة صالح (JPG, PNG, WebP, GIF, BMP, TIFF)');
            return;
        }

        // Increased file size limit to 50MB
        if (file.size > 50 * 1024 * 1024) {
            this.showError('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 50 ميجابايت');
            return;
        }

        this.currentFile = file;
        this.loadImage(file);
    }

    isValidImageFile(file) {
        const validTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/webp',
            'image/gif', 'image/bmp', 'image/tiff', 'image/svg+xml'
        ];
        return validTypes.includes(file.type);
    }

    loadImage(file) {
        this.showProgress('جاري تحميل الصورة...', 0);

        const reader = new FileReader();

        reader.onprogress = (e) => {
            if (e.lengthComputable) {
                const progress = (e.loaded / e.total) * 50; // 50% for loading
                this.updateProgress(progress);
            }
        };

        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.updateProgress(75);
                this.processImage(img);
                this.showImagePreview();
                this.showControls();
                this.updateUploadArea();
                this.saveToHistory();
                this.hideProgress();
                this.showSuccess('تم تحميل الصورة بنجاح!');
            };
            img.onerror = () => {
                this.hideProgress();
                this.showError('فشل في تحميل الصورة. يرجى المحاولة مرة أخرى.');
            };
            img.src = e.target.result;
        };

        reader.onerror = () => {
            this.hideProgress();
            this.showError('فشل في قراءة الملف. يرجى المحاولة مرة أخرى.');
        };

        reader.readAsDataURL(file);
    }

    processImage(img) {
        // Create canvases with higher quality
        this.originalCanvas = document.createElement('canvas');
        this.enhancedCanvas = document.createElement('canvas');

        const originalCtx = this.originalCanvas.getContext('2d');
        const enhancedCtx = this.enhancedCanvas.getContext('2d');

        // Enhanced quality settings
        originalCtx.imageSmoothingEnabled = true;
        originalCtx.imageSmoothingQuality = 'high';
        enhancedCtx.imageSmoothingEnabled = true;
        enhancedCtx.imageSmoothingQuality = 'high';

        // Calculate optimal dimensions (increased max size)
        const maxWidth = 1920;
        const maxHeight = 1080;
        let { width, height } = this.calculateDimensions(img.width, img.height, maxWidth, maxHeight);

        this.originalCanvas.width = width;
        this.originalCanvas.height = height;
        this.enhancedCanvas.width = width;
        this.enhancedCanvas.height = height;

        // Draw original image
        originalCtx.drawImage(img, 0, 0, width, height);
        enhancedCtx.drawImage(img, 0, 0, width, height);

        // Store original image data
        this.originalImageData = originalCtx.getImageData(0, 0, width, height);

        // Update preview images
        this.updatePreviewImages();

        // Calculate and display image statistics
        this.calculateImageStats();
    }

    calculateDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
        let width = originalWidth;
        let height = originalHeight;

        // Maintain aspect ratio while fitting within max dimensions
        const aspectRatio = width / height;

        if (width > maxWidth) {
            width = maxWidth;
            height = width / aspectRatio;
        }

        if (height > maxHeight) {
            height = maxHeight;
            width = height * aspectRatio;
        }

        return { width: Math.round(width), height: Math.round(height) };
    }

    calculateImageStats() {
        if (!this.originalImageData) return;

        const data = this.originalImageData.data;
        const pixels = data.length / 4;

        let totalR = 0, totalG = 0, totalB = 0;
        let minBrightness = 255, maxBrightness = 0;

        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            totalR += r;
            totalG += g;
            totalB += b;

            const brightness = (r + g + b) / 3;
            minBrightness = Math.min(minBrightness, brightness);
            maxBrightness = Math.max(maxBrightness, brightness);
        }

        this.imageStats = {
            width: this.originalCanvas.width,
            height: this.originalCanvas.height,
            pixels: pixels,
            fileSize: this.currentFile ? this.formatFileSize(this.currentFile.size) : 'غير معروف',
            avgRed: Math.round(totalR / pixels),
            avgGreen: Math.round(totalG / pixels),
            avgBlue: Math.round(totalB / pixels),
            minBrightness: Math.round(minBrightness),
            maxBrightness: Math.round(maxBrightness),
            contrast: Math.round(maxBrightness - minBrightness)
        };
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updatePreviewImages() {
        const originalImg = document.getElementById('originalImage');
        const enhancedImg = document.getElementById('enhancedImage');

        if (originalImg) originalImg.src = this.originalCanvas.toDataURL('image/jpeg', 0.9);
        if (enhancedImg) enhancedImg.src = this.enhancedCanvas.toDataURL('image/jpeg', 0.9);
    }

    // Enhanced image processing with Web Workers support
    applyEnhancements() {
        if (!this.originalImageData || this.isProcessing) return;

        this.isProcessing = true;
        this.showProgress('جاري تطبيق التحسينات...', 0);

        // Use requestAnimationFrame for smooth performance
        requestAnimationFrame(() => {
            try {
                const enhancedCtx = this.enhancedCanvas.getContext('2d');
                const imageData = new ImageData(
                    new Uint8ClampedArray(this.originalImageData.data),
                    this.originalImageData.width,
                    this.originalImageData.height
                );

                this.updateProgress(25);

                // Apply enhancements in sequence
                this.applyBrightness(imageData, this.settings.brightness);
                this.updateProgress(40);

                this.applyContrast(imageData, this.settings.contrast);
                this.updateProgress(55);

                this.applySaturation(imageData, this.settings.saturation);
                this.updateProgress(70);

                enhancedCtx.putImageData(imageData, 0, 0);

                // Apply transformations
                this.applyTransformations(enhancedCtx);
                this.updateProgress(85);

                // Apply sharpness
                if (this.settings.sharpness !== 100) {
                    this.applySharpness(enhancedCtx, this.settings.sharpness);
                }

                this.updateProgress(100);

                // Update enhanced preview
                const enhancedImg = document.getElementById('enhancedImage');
                if (enhancedImg) {
                    enhancedImg.src = this.enhancedCanvas.toDataURL('image/jpeg', 0.9);
                }

                this.hideProgress();
                this.isProcessing = false;

            } catch (error) {
                console.error('Error applying enhancements:', error);
                this.showError('حدث خطأ أثناء تطبيق التحسينات');
                this.hideProgress();
                this.isProcessing = false;
            }
        });
    }

    // Advanced image processing functions
    applyBrightness(imageData, brightness) {
        const data = imageData.data;
        const factor = brightness * 2.55;

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.max(0, Math.min(255, data[i] + factor));
            data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + factor));
            data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + factor));
        }
    }

    applyContrast(imageData, contrast) {
        const data = imageData.data;
        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.max(0, Math.min(255, factor * (data[i] - 128) + 128));
            data[i + 1] = Math.max(0, Math.min(255, factor * (data[i + 1] - 128) + 128));
            data[i + 2] = Math.max(0, Math.min(255, factor * (data[i + 2] - 128) + 128));
        }
    }

    applySaturation(imageData, saturation) {
        const data = imageData.data;
        const factor = saturation / 100;

        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            const gray = 0.299 * r + 0.587 * g + 0.114 * b;

            data[i] = Math.max(0, Math.min(255, gray + factor * (r - gray)));
            data[i + 1] = Math.max(0, Math.min(255, gray + factor * (g - gray)));
            data[i + 2] = Math.max(0, Math.min(255, gray + factor * (b - gray)));
        }
    }

    applySharpness(ctx, sharpness) {
        if (sharpness === 100) return;

        const factor = sharpness / 100;
        const imageData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;

        // Sharpening kernel
        const kernel = [
            0, -1, 0,
            -1, 5, -1,
            0, -1, 0
        ];

        const output = new Uint8ClampedArray(data);

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                for (let c = 0; c < 3; c++) {
                    let sum = 0;
                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const idx = ((y + ky) * width + (x + kx)) * 4 + c;
                            sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];
                        }
                    }
                    const idx = (y * width + x) * 4 + c;
                    output[idx] = Math.max(0, Math.min(255, sum * factor + data[idx] * (1 - factor)));
                }
            }
        }

        const newImageData = new ImageData(output, width, height);
        ctx.putImageData(newImageData, 0, 0);
    }

    applyTransformations(ctx) {
        if (this.settings.rotation === 0 && !this.settings.flipHorizontal && !this.settings.flipVertical) {
            return;
        }

        const canvas = ctx.canvas;
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');

        tempCanvas.width = canvas.width;
        tempCanvas.height = canvas.height;
        tempCtx.drawImage(canvas, 0, 0);

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.save();

        // Apply transformations
        ctx.translate(canvas.width / 2, canvas.height / 2);

        if (this.settings.rotation !== 0) {
            ctx.rotate((this.settings.rotation * Math.PI) / 180);
        }

        let scaleX = this.settings.flipHorizontal ? -1 : 1;
        let scaleY = this.settings.flipVertical ? -1 : 1;
        ctx.scale(scaleX, scaleY);

        ctx.drawImage(tempCanvas, -canvas.width / 2, -canvas.height / 2);
        ctx.restore();
    }

    // Advanced editing tools
    rotateImage(degrees) {
        this.settings.rotation = (this.settings.rotation + degrees) % 360;
        this.applyEnhancements();
        this.saveToHistory();
        this.showSuccess(`تم تدوير الصورة ${degrees} درجة`);
    }

    flipImage(direction) {
        if (direction === 'horizontal') {
            this.settings.flipHorizontal = !this.settings.flipHorizontal;
            this.showSuccess('تم انعكاس الصورة أفقياً');
        } else if (direction === 'vertical') {
            this.settings.flipVertical = !this.settings.flipVertical;
            this.showSuccess('تم انعكاس الصورة عمودياً');
        }
        this.applyEnhancements();
        this.saveToHistory();
    }

    cropImage() {
        // Simplified crop implementation
        this.showInfo('ميزة القص ستكون متاحة قريباً. يمكنك استخدام أدوات التحسين الأخرى في الوقت الحالي.');
    }

    autoEnhance() {
        if (!this.imageStats) {
            this.showError('يرجى تحميل صورة أولاً');
            return;
        }

        this.showProgress('جاري التحسين التلقائي الذكي...', 0);

        // AI-powered auto enhancement based on image analysis
        let preset = 'auto';

        // Analyze image characteristics
        const avgBrightness = (this.imageStats.minBrightness + this.imageStats.maxBrightness) / 2;
        const contrast = this.imageStats.contrast;

        if (avgBrightness < 100) {
            preset = 'lowLight';
        } else if (contrast < 50) {
            preset = 'portrait';
        } else if (contrast > 150) {
            preset = 'landscape';
        }

        const enhancement = this.aiPresets[preset];

        // Apply AI enhancement
        this.settings.brightness = enhancement.brightness;
        this.settings.contrast = enhancement.contrast;
        this.settings.saturation = enhancement.saturation;
        this.settings.sharpness = enhancement.sharpness;

        // Update sliders
        this.updateSliders();

        setTimeout(() => {
            this.applyEnhancements();
            this.saveToHistory();
            this.hideProgress();
            this.showSuccess(`تم تطبيق التحسين التلقائي الذكي (${this.getPresetNameInArabic(preset)})`);
        }, 500);
    }

    removeBackground() {
        this.showInfo('ميزة إزالة الخلفية بالذكاء الاصطناعي ستكون متاحة في التحديث القادم.');
    }

    // History management
    saveToHistory() {
        const state = {
            settings: { ...this.settings },
            imageData: this.enhancedCanvas.toDataURL('image/jpeg', 0.9),
            timestamp: Date.now()
        };

        // Remove future history if we're not at the end
        if (this.historyIndex < this.history.length - 1) {
            this.history = this.history.slice(0, this.historyIndex + 1);
        }

        this.history.push(state);
        this.historyIndex = this.history.length - 1;

        // Limit history size
        if (this.history.length > 20) {
            this.history.shift();
            this.historyIndex--;
        }
    }

    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.restoreFromHistory();
            this.showSuccess('تم التراجع');
        } else {
            this.showInfo('لا توجد خطوات للتراجع');
        }
    }

    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.restoreFromHistory();
            this.showSuccess('تم الإعادة');
        } else {
            this.showInfo('لا توجد خطوات للإعادة');
        }
    }

    restoreFromHistory() {
        if (this.historyIndex >= 0 && this.historyIndex < this.history.length) {
            const state = this.history[this.historyIndex];
            this.settings = { ...state.settings };
            this.updateSliders();
            this.applyEnhancements();
        }
    }

    // UI Helper functions
    updateSliders() {
        const sliders = ['brightness', 'contrast', 'saturation', 'sharpness'];

        sliders.forEach(slider => {
            const sliderElement = document.getElementById(`${slider}Slider`);
            const valueElement = document.getElementById(`${slider}Value`);

            if (sliderElement && valueElement) {
                sliderElement.value = this.settings[slider];
                valueElement.textContent = this.settings[slider] + (slider === 'sharpness' ? '%' : '');
            }
        });
    }

    showImagePreview() {
        const preview = document.getElementById('imagePreview');
        if (preview) preview.style.display = 'block';
    }

    showControls() {
        const controls = document.getElementById('controlsPanel');
        if (controls) controls.style.display = 'block';
    }

    updateUploadArea() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        uploadArea.classList.add('has-file');

        const uploadText = uploadArea.querySelector('.upload-text');
        if (uploadText && this.currentFile) {
            uploadText.innerHTML = `
                <strong>تم رفع الصورة بنجاح!</strong><br>
                ${this.currentFile.name}<br>
                <small>انقر لاختيار صورة أخرى</small>
            `;
        }
    }

    // Statistics and Info functions
    showStats() {
        if (!this.imageStats) {
            this.showError('يرجى تحميل صورة أولاً لعرض الإحصائيات');
            return;
        }

        const overlay = document.getElementById('overlay');
        const statsPanel = document.getElementById('statsPanel');
        const statsGrid = document.getElementById('statsGrid');

        if (!overlay || !statsPanel || !statsGrid) return;

        // Populate stats
        statsGrid.innerHTML = `
            <div class="stat-item">
                <div class="stat-value">${this.imageStats.width}×${this.imageStats.height}</div>
                <div class="stat-label">الأبعاد (بكسل)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${this.imageStats.fileSize}</div>
                <div class="stat-label">حجم الملف</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${this.imageStats.pixels.toLocaleString()}</div>
                <div class="stat-label">عدد البكسل</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${this.imageStats.contrast}</div>
                <div class="stat-label">مستوى التباين</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">RGB(${this.imageStats.avgRed}, ${this.imageStats.avgGreen}, ${this.imageStats.avgBlue})</div>
                <div class="stat-label">متوسط الألوان</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${this.imageStats.minBrightness} - ${this.imageStats.maxBrightness}</div>
                <div class="stat-label">نطاق السطوع</div>
            </div>
        `;

        overlay.style.display = 'block';
        statsPanel.style.display = 'block';
    }

    showHelp() {
        const helpContent = `
            <h3>دليل الاستخدام السريع</h3>
            <div style="text-align: right; line-height: 1.8;">
                <h4>🎯 الخطوات الأساسية:</h4>
                <p>1. ارفع صورتك بالسحب والإفلات أو النقر على "اختيار الصور"</p>
                <p>2. استخدم أشرطة التمرير لتعديل السطوع والتباين والتشبع والحدة</p>
                <p>3. جرب الفلاتر الجاهزة للحصول على نتائج سريعة</p>
                <p>4. استخدم الأدوات المتقدمة للتدوير والانعكاس</p>
                <p>5. انقر "تحميل الصورة" لحفظ النتيجة</p>

                <h4>⌨️ اختصارات لوحة المفاتيح:</h4>
                <p>• Ctrl+Z: التراجع</p>
                <p>• Ctrl+Y: الإعادة</p>
                <p>• Ctrl+S: تحميل الصورة</p>
                <p>• Ctrl+R: إعادة تعيين</p>

                <h4>💡 نصائح:</h4>
                <p>• استخدم "التحسين الذكي" للحصول على نتائج تلقائية</p>
                <p>• جرب الثيمات المختلفة والوضع الليلي</p>
                <p>• احفظ إعداداتك المفضلة لاستخدامها لاحقاً</p>
            </div>
        `;

        this.showModal('المساعدة والدعم', helpContent);
    }

    showModal(title, content) {
        const overlay = document.getElementById('overlay');

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'stats-panel';
        modal.style.display = 'block';
        modal.style.maxWidth = '600px';
        modal.innerHTML = `
            <div class="stats-header">
                <h3 style="color: var(--primary-color); margin: 0;">
                    <i class="fas fa-info-circle"></i>
                    ${title}
                </h3>
                <button onclick="this.parentElement.parentElement.remove(); document.getElementById('overlay').style.display='none'"
                        style="background: none; border: none; font-size: 1.5rem; color: var(--text-secondary); cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div style="max-height: 400px; overflow-y: auto;">
                ${content}
            </div>
        `;

        document.body.appendChild(modal);
        overlay.style.display = 'block';
    }

    closeModals() {
        const overlay = document.getElementById('overlay');
        const statsPanel = document.getElementById('statsPanel');

        if (overlay) overlay.style.display = 'none';
        if (statsPanel) statsPanel.style.display = 'none';

        // Remove any temporary modals
        document.querySelectorAll('.stats-panel').forEach(modal => {
            if (modal.id !== 'statsPanel') {
                modal.remove();
            }
        });
    }

    // Progress and notification functions
    showProgress(message, progress) {
        const container = document.getElementById('progressContainer');
        const bar = document.getElementById('progressBar');
        const text = document.getElementById('progressText');

        if (container && bar && text) {
            container.style.display = 'block';
            bar.style.width = progress + '%';
            text.textContent = message;
        }
    }

    updateProgress(progress) {
        const bar = document.getElementById('progressBar');
        if (bar) {
            bar.style.width = Math.min(100, Math.max(0, progress)) + '%';
        }
    }

    hideProgress() {
        const container = document.getElementById('progressContainer');
        if (container) {
            setTimeout(() => {
                container.style.display = 'none';
            }, 500);
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showInfo(message) {
        this.showNotification(message, 'info');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `${type}-notification`;

        const colors = {
            success: 'var(--success-color)',
            error: 'var(--danger-color)',
            info: 'var(--info-color)'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type]};
            color: white;
            padding: 1rem 2rem;
            border-radius: var(--radius-medium);
            box-shadow: var(--shadow-heavy);
            z-index: 10000;
            animation: slideInRight 0.3s ease;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            max-width: 400px;
        `;

        notification.innerHTML = `
            <i class="${icons[type]}"></i>
            ${message}
        `;

        document.body.appendChild(notification);

        // Auto remove
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, type === 'error' ? 5000 : 3000);
    }

    // Preset and settings management
    applyPreset(presetName) {
        const presets = {
            portrait: { brightness: 10, contrast: 15, saturation: 5, sharpness: 120 },
            landscape: { brightness: 5, contrast: 20, saturation: 15, sharpness: 110 },
            social: { brightness: 15, contrast: 25, saturation: 20, sharpness: 130 },
            vintage: { brightness: -10, contrast: 10, saturation: -20, sharpness: 90 }
        };

        if (presets[presetName]) {
            this.settings = { ...this.settings, ...presets[presetName] };
            this.updateSliders();
            this.applyEnhancements();
            this.saveToHistory();
            this.showSuccess(`تم تطبيق فلتر ${this.getPresetNameInArabic(presetName)} بنجاح!`);
        }
    }

    getPresetNameInArabic(presetName) {
        const names = {
            portrait: 'الصورة الشخصية',
            landscape: 'المنظر الطبيعي',
            social: 'وسائل التواصل',
            vintage: 'الكلاسيكي',
            auto: 'التلقائي',
            lowLight: 'الإضاءة المنخفضة'
        };
        return names[presetName] || presetName;
    }

    savePreset() {
        const presetName = prompt('أدخل اسم الإعداد المخصص:');
        if (presetName && presetName.trim()) {
            const customPresets = JSON.parse(localStorage.getItem('customPresets') || '{}');
            customPresets[presetName.trim()] = { ...this.settings };
            localStorage.setItem('customPresets', JSON.stringify(customPresets));
            this.showSuccess(`تم حفظ الإعداد "${presetName.trim()}" بنجاح!`);
        }
    }

    loadPreset() {
        const customPresets = JSON.parse(localStorage.getItem('customPresets') || '{}');
        const presetNames = Object.keys(customPresets);

        if (presetNames.length === 0) {
            this.showInfo('لا توجد إعدادات محفوظة. احفظ إعداداتك أولاً.');
            return;
        }

        const presetList = presetNames.map((name, index) => `${index + 1}. ${name}`).join('\n');
        const choice = prompt(`اختر الإعداد المحفوظ:\n${presetList}\n\nأدخل رقم الإعداد:`);

        if (choice && !isNaN(choice)) {
            const index = parseInt(choice) - 1;
            if (index >= 0 && index < presetNames.length) {
                const presetName = presetNames[index];
                this.settings = { ...this.settings, ...customPresets[presetName] };
                this.updateSliders();
                this.applyEnhancements();
                this.saveToHistory();
                this.showSuccess(`تم تحميل الإعداد "${presetName}" بنجاح!`);
            }
        }
    }

    resetEnhancements() {
        this.settings = {
            brightness: 0,
            contrast: 0,
            saturation: 0,
            sharpness: 100,
            rotation: 0,
            flipHorizontal: false,
            flipVertical: false,
            cropData: null
        };

        this.updateSliders();
        this.applyEnhancements();
        this.saveToHistory();
        this.showSuccess('تم إعادة تعيين جميع الإعدادات');
    }

    // Download functionality with multiple formats
    downloadImage(format = 'jpeg', quality = 0.9) {
        if (!this.enhancedCanvas) {
            this.showError('يرجى تحميل صورة أولاً');
            return;
        }

        this.showProgress('جاري تحضير الصورة للتحميل...', 0);

        setTimeout(() => {
            try {
                const link = document.createElement('a');
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const extension = format === 'jpeg' ? 'jpg' : format;

                link.download = `enhanced_image_${timestamp}.${extension}`;
                link.href = this.enhancedCanvas.toDataURL(`image/${format}`, quality);

                this.updateProgress(100);
                link.click();

                this.hideProgress();
                this.showSuccess(`تم تحميل الصورة بصيغة ${format.toUpperCase()} بنجاح!`);

            } catch (error) {
                this.hideProgress();
                this.showError('فشل في تحميل الصورة. يرجى المحاولة مرة أخرى.');
                console.error('Download error:', error);
            }
        }, 500);
    }

    // Batch processing for multiple images
    processBatch(files) {
        if (files.length === 0) return;

        this.showSuccess(`بدء معالجة ${files.length} صورة...`);

        Array.from(files).forEach((file, index) => {
            setTimeout(() => {
                this.processImageForBatch(file, index + 1, files.length);
            }, index * 1000);
        });
    }

    processImageForBatch(file, current, total) {
        const reader = new FileReader();

        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                // Apply current settings
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                this.applyBrightness(imageData, this.settings.brightness);
                this.applyContrast(imageData, this.settings.contrast);
                this.applySaturation(imageData, this.settings.saturation);
                ctx.putImageData(imageData, 0, 0);

                // Download processed image
                const link = document.createElement('a');
                const fileName = file.name.replace(/\.[^/.]+$/, '');
                link.download = `${fileName}_enhanced.jpg`;
                link.href = canvas.toDataURL('image/jpeg', 0.9);
                link.click();

                if (current === total) {
                    this.showSuccess('تم الانتهاء من معالجة جميع الصور!');
                }
            };
            img.src = e.target.result;
        };

        reader.readAsDataURL(file);
    }
}

// Initialize the enhanced application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.imageEnhancer = new AdvancedArabicImageEnhancer();
});

// Add enhanced CSS animations
const enhancedStyle = document.createElement('style');
enhancedStyle.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    @keyframes fadeInUp {
        from { transform: translateY(30px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .success-notification, .error-notification, .info-notification {
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
        direction: rtl;
        text-align: right;
    }

    .tool-btn:active {
        transform: translateY(-1px) scale(0.98);
    }

    .theme-btn:active {
        transform: scale(0.95);
    }

    .stats-panel {
        animation: fadeInUp 0.3s ease;
    }

    .progress-bar {
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        box-shadow: 0 2px 10px rgba(44, 90, 160, 0.3);
    }

    /* Enhanced hover effects */
    .preset-btn:hover, .tool-btn:hover {
        animation: pulse 0.6s ease-in-out;
    }

    /* Smooth transitions for theme changes */
    * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
`;
document.head.appendChild(enhancedStyle);
