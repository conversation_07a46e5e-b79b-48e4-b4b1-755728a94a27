"""
Core image processing functionality for the Professional Image Editor
Handles all image manipulation operations using Pillow and OpenCV
"""

import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageOps, ImageDraw
import cv2
from typing import Tuple, Optional, List, Union
from config import PERFORMANCE

class ImageProcessor:
    """Core image processing operations"""
    
    def __init__(self):
        self.max_size = PERFORMANCE['max_image_size']
    
    def load_image(self, file_path: str) -> Optional[Image.Image]:
        """Load image from file with size validation"""
        try:
            image = Image.open(file_path)
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA'):
                # Preserve transparency by converting to RGBA
                if image.mode == 'LA':
                    image = image.convert('RGBA')
            elif image.mode not in ('RGB', 'RGBA'):
                image = image.convert('RGB')
            
            # Check size limits
            if image.width > self.max_size[0] or image.height > self.max_size[1]:
                image.thumbnail(self.max_size, Image.Resampling.LANCZOS)
            
            return image
        except Exception as e:
            print(f"Error loading image: {e}")
            return None
    
    def save_image(self, image: Image.Image, file_path: str, quality: int = 95) -> bool:
        """Save image to file"""
        try:
            # Determine format from file extension
            file_ext = file_path.lower().split('.')[-1]
            
            if file_ext in ['jpg', 'jpeg']:
                # Convert RGBA to RGB for JPEG
                if image.mode == 'RGBA':
                    rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                    rgb_image.paste(image, mask=image.split()[-1])
                    image = rgb_image
                image.save(file_path, 'JPEG', quality=quality, optimize=True)
            elif file_ext == 'png':
                image.save(file_path, 'PNG', optimize=True)
            elif file_ext == 'gif':
                image.save(file_path, 'GIF', optimize=True)
            elif file_ext == 'bmp':
                if image.mode == 'RGBA':
                    image = image.convert('RGB')
                image.save(file_path, 'BMP')
            else:
                image.save(file_path)
            
            return True
        except Exception as e:
            print(f"Error saving image: {e}")
            return False
    
    def resize_image(self, image: Image.Image, new_size: Tuple[int, int], 
                    maintain_aspect: bool = True) -> Image.Image:
        """Resize image with optional aspect ratio preservation"""
        if maintain_aspect:
            image.thumbnail(new_size, Image.Resampling.LANCZOS)
            return image
        else:
            return image.resize(new_size, Image.Resampling.LANCZOS)
    
    def crop_image(self, image: Image.Image, crop_box: Tuple[int, int, int, int]) -> Image.Image:
        """Crop image to specified box (left, top, right, bottom)"""
        return image.crop(crop_box)
    
    def rotate_image(self, image: Image.Image, angle: float, expand: bool = True) -> Image.Image:
        """Rotate image by specified angle"""
        return image.rotate(angle, expand=expand, fillcolor='white')
    
    def flip_image(self, image: Image.Image, direction: str) -> Image.Image:
        """Flip image horizontally or vertically"""
        if direction.lower() == 'horizontal':
            return image.transpose(Image.Transpose.FLIP_LEFT_RIGHT)
        elif direction.lower() == 'vertical':
            return image.transpose(Image.Transpose.FLIP_TOP_BOTTOM)
        else:
            raise ValueError("Direction must be 'horizontal' or 'vertical'")
    
    def adjust_brightness(self, image: Image.Image, factor: float) -> Image.Image:
        """Adjust image brightness (factor: 0.0 = black, 1.0 = original, 2.0 = twice as bright)"""
        enhancer = ImageEnhance.Brightness(image)
        return enhancer.enhance(factor)
    
    def adjust_contrast(self, image: Image.Image, factor: float) -> Image.Image:
        """Adjust image contrast (factor: 0.0 = gray, 1.0 = original, 2.0 = twice contrast)"""
        enhancer = ImageEnhance.Contrast(image)
        return enhancer.enhance(factor)
    
    def adjust_saturation(self, image: Image.Image, factor: float) -> Image.Image:
        """Adjust image saturation (factor: 0.0 = grayscale, 1.0 = original, 2.0 = twice saturation)"""
        enhancer = ImageEnhance.Color(image)
        return enhancer.enhance(factor)
    
    def adjust_sharpness(self, image: Image.Image, factor: float) -> Image.Image:
        """Adjust image sharpness (factor: 0.0 = blurred, 1.0 = original, 2.0 = sharpened)"""
        enhancer = ImageEnhance.Sharpness(image)
        return enhancer.enhance(factor)
    
    def apply_blur(self, image: Image.Image, radius: float = 1.0) -> Image.Image:
        """Apply Gaussian blur to image"""
        return image.filter(ImageFilter.GaussianBlur(radius=radius))
    
    def apply_sharpen(self, image: Image.Image) -> Image.Image:
        """Apply sharpening filter to image"""
        return image.filter(ImageFilter.SHARPEN)
    
    def apply_edge_enhance(self, image: Image.Image) -> Image.Image:
        """Apply edge enhancement filter"""
        return image.filter(ImageFilter.EDGE_ENHANCE)
    
    def convert_to_grayscale(self, image: Image.Image) -> Image.Image:
        """Convert image to grayscale"""
        return image.convert('L').convert('RGB')
    
    def apply_sepia(self, image: Image.Image, factor: float = 1.0) -> Image.Image:
        """Apply sepia tone effect"""
        # Convert to numpy array for processing
        img_array = np.array(image)
        
        # Sepia transformation matrix
        sepia_filter = np.array([
            [0.393, 0.769, 0.189],
            [0.349, 0.686, 0.168],
            [0.272, 0.534, 0.131]
        ])
        
        # Apply sepia effect
        sepia_img = img_array.dot(sepia_filter.T)
        sepia_img = np.clip(sepia_img, 0, 255).astype(np.uint8)
        
        # Blend with original based on factor
        if factor < 1.0:
            sepia_img = (sepia_img * factor + img_array * (1 - factor)).astype(np.uint8)
        
        return Image.fromarray(sepia_img)
    
    def adjust_hue(self, image: Image.Image, hue_shift: int) -> Image.Image:
        """Adjust image hue (hue_shift: -180 to 180)"""
        # Convert PIL to OpenCV format
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2HSV)
        
        # Adjust hue
        cv_image[:, :, 0] = (cv_image[:, :, 0] + hue_shift) % 180
        
        # Convert back to PIL
        rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_HSV2RGB)
        return Image.fromarray(rgb_image)
    
    def apply_noise_reduction(self, image: Image.Image, strength: int = 10) -> Image.Image:
        """Apply noise reduction using OpenCV"""
        # Convert to OpenCV format
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # Apply denoising
        denoised = cv2.fastNlMeansDenoisingColored(cv_image, None, strength, strength, 7, 21)
        
        # Convert back to PIL
        rgb_image = cv2.cvtColor(denoised, cv2.COLOR_BGR2RGB)
        return Image.fromarray(rgb_image)
    
    def auto_enhance(self, image: Image.Image) -> Image.Image:
        """Automatically enhance image (auto levels, contrast, etc.)"""
        # Auto contrast
        enhanced = ImageOps.autocontrast(image)
        
        # Slight sharpening
        enhanced = self.adjust_sharpness(enhanced, 1.1)
        
        # Slight saturation boost
        enhanced = self.adjust_saturation(enhanced, 1.05)
        
        return enhanced
    
    def create_vignette(self, image: Image.Image, strength: float = 0.5) -> Image.Image:
        """Add vignette effect to image"""
        width, height = image.size
        
        # Create vignette mask
        mask = Image.new('L', (width, height), 0)
        draw = ImageDraw.Draw(mask)
        
        # Calculate vignette parameters
        center_x, center_y = width // 2, height // 2
        max_radius = min(width, height) // 2
        
        # Create gradient circles
        for i in range(max_radius, 0, -1):
            alpha = int(255 * (1 - strength * (max_radius - i) / max_radius))
            draw.ellipse([center_x - i, center_y - i, center_x + i, center_y + i], 
                        fill=alpha)
        
        # Apply vignette
        vignette_img = Image.composite(image, Image.new('RGB', image.size, 'black'), mask)
        return vignette_img
    
    def add_border(self, image: Image.Image, border_width: int, border_color: str = 'white') -> Image.Image:
        """Add border around image"""
        return ImageOps.expand(image, border=border_width, fill=border_color)
    
    def create_thumbnail_with_aspect(self, image: Image.Image, size: Tuple[int, int], 
                                   bg_color: str = 'white') -> Image.Image:
        """Create thumbnail maintaining aspect ratio with background"""
        # Calculate new size maintaining aspect ratio
        image.thumbnail(size, Image.Resampling.LANCZOS)
        
        # Create background
        background = Image.new('RGB', size, bg_color)
        
        # Center the image
        offset = ((size[0] - image.width) // 2, (size[1] - image.height) // 2)
        background.paste(image, offset)
        
        return background
