#!/usr/bin/env python3
"""
Simple launcher for Professional Image Editor
Provides easy startup with error handling and dependency checking
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        return False, f"Python 3.7+ required. Current version: {sys.version}"
    return True, "Python version OK"

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'PIL',
        'cv2', 
        'numpy',
        'arabic_reshaper',
        'bidi'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        return False, f"Missing packages: {', '.join(missing)}"
    return True, "All dependencies available"

def install_dependencies():
    """Try to install missing dependencies"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        return True
    except subprocess.CalledProcessError:
        return False
    except FileNotFoundError:
        return False

def show_error_dialog(title, message):
    """Show error dialog"""
    try:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(title, message)
        root.destroy()
    except:
        print(f"ERROR: {title}\n{message}")

def show_info_dialog(title, message):
    """Show info dialog"""
    try:
        root = tk.Tk()
        root.withdraw()
        messagebox.showinfo(title, message)
        root.destroy()
    except:
        print(f"INFO: {title}\n{message}")

def main():
    """Main launcher function"""
    print("Professional Image Editor Launcher")
    print("=" * 40)
    
    # Check Python version
    version_ok, version_msg = check_python_version()
    print(f"Python Version: {version_msg}")
    
    if not version_ok:
        show_error_dialog("Python Version Error", 
                         f"{version_msg}\n\nPlease upgrade Python to version 3.7 or higher.")
        return False
    
    # Check dependencies
    deps_ok, deps_msg = check_dependencies()
    print(f"Dependencies: {deps_msg}")
    
    if not deps_ok:
        print("Attempting to install missing dependencies...")
        
        # Ask user if they want to install dependencies
        try:
            root = tk.Tk()
            root.withdraw()
            install = messagebox.askyesno(
                "Missing Dependencies",
                f"{deps_msg}\n\nWould you like to install them automatically?"
            )
            root.destroy()
            
            if install:
                print("Installing dependencies...")
                if install_dependencies():
                    print("Dependencies installed successfully!")
                    show_info_dialog("Success", "Dependencies installed successfully!\nStarting application...")
                else:
                    error_msg = ("Failed to install dependencies automatically.\n\n"
                               "Please run manually:\n"
                               "pip install -r requirements.txt")
                    show_error_dialog("Installation Failed", error_msg)
                    return False
            else:
                return False
                
        except:
            # Fallback to console
            response = input("Install missing dependencies? (y/n): ")
            if response.lower() in ['y', 'yes']:
                if install_dependencies():
                    print("Dependencies installed successfully!")
                else:
                    print("Failed to install dependencies.")
                    print("Please run: pip install -r requirements.txt")
                    return False
            else:
                return False
    
    # Check if main application file exists
    if not os.path.exists('main.py'):
        error_msg = "main.py not found!\n\nPlease ensure all application files are present."
        show_error_dialog("File Not Found", error_msg)
        return False
    
    # Launch the application
    print("Starting Professional Image Editor...")
    try:
        import main
        return True
    except Exception as e:
        error_msg = f"Failed to start application:\n\n{str(e)}\n\nPlease check the console for more details."
        show_error_dialog("Startup Error", error_msg)
        print(f"Error details: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("Press Enter to exit...")
    except KeyboardInterrupt:
        print("\nLauncher interrupted by user.")
    except Exception as e:
        print(f"Launcher error: {e}")
        input("Press Enter to exit...")
