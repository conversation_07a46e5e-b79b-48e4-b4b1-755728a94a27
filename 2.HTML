html
<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عواصم البلدان العربية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
        }
        header {
            background-color: #4CAF50;
            color: white;
            padding: 10px 0;
            text-align: center;
        }
        main {
            padding: 20px;
        }
        ul {
            list-style-type: none;
            padding: 0;
        }
        li {
            padding: 8px;
            background: white;
            margin: 5px 0;
            border-radius: 3px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <header>
        <h1>عواصم البلدان العربية</h1>
    </header>
    <main>
        <input type="text" id="search" placeholder="ابحث عن دولة..." />
        <ul id="country-list">
            <li>مصر - القاهرة</li>
            <li>السعودية - الرياض</li>
            <li>الأردن - عمان</li>
            <li>العراق - بغداد</li>
            <li>المغرب - الرباط</li>
            <!-- أضف المزيد من الدول هنا -->
        </ul>
    </main>
    <footer>
        <p>حقوق الطبع والنشر © 2023</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');

            searchInput.addEventListener('keyup', function() {
                const filter = searchInput.value.toLowerCase();
                const listItems = document.querySelectorAll('#country-list li');

                listItems.forEach(function(item) {
                    if (item.textContent.toLowerCase().includes(filter)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>