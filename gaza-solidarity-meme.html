<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تضامن الأمة العربية مع غزة</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', '<PERSON>i', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .main-title {
            font-family: 'Amiri', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            line-height: 1.4;
        }

        .meme-container {
            background: #ffffff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .meme-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff0000, #000000, #00ff00);
        }

        .meme-image {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: #f0f0f0;
            border: 3px solid #333;
            border-radius: 10px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .illustration {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #e0e0e0 25%, transparent 25%), 
                        linear-gradient(-45deg, #e0e0e0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #e0e0e0 75%), 
                        linear-gradient(-45deg, transparent 75%, #e0e0e0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .heart-symbol {
            font-size: 4rem;
            color: #ff0000;
            margin-bottom: 10px;
            animation: heartbeat 2s infinite;
        }

        .solidarity-text {
            font-family: 'Amiri', serif;
            font-size: 1.2rem;
            color: #333;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .meme-text-top {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-shadow: 2px 2px 0px #fff, -2px -2px 0px #fff, 2px -2px 0px #fff, -2px 2px 0px #fff;
        }

        .meme-text-bottom {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1.6rem;
            font-weight: 700;
            color: #333;
            margin-top: 20px;
            text-shadow: 2px 2px 0px #fff, -2px -2px 0px #fff, 2px -2px 0px #fff, -2px 2px 0px #fff;
        }

        .description {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1.2rem;
            line-height: 1.8;
            color: #e0e0e0;
            margin-top: 30px;
            text-align: center;
        }

        .hashtags {
            margin-top: 30px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        .hashtag {
            background: rgba(255, 255, 255, 0.1);
            color: #4CAF50;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            border: 1px solid rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
        }

        .hashtag:hover {
            background: rgba(76, 175, 80, 0.2);
            transform: translateY(-2px);
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.9rem;
            color: #ccc;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .main-title {
                font-size: 2rem;
            }

            .meme-text-top, .meme-text-bottom {
                font-size: 1.4rem;
            }

            .meme-image {
                height: 250px;
            }

            .heart-symbol {
                font-size: 3rem;
            }

            .hashtags {
                flex-direction: column;
                align-items: center;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 1.8rem;
            }

            .meme-text-top, .meme-text-bottom {
                font-size: 1.2rem;
            }

            .description {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="main-title">صوت الأمة العربية لغزة</h1>
        
        <div class="meme-container">
            <div class="meme-text-top">عندما يسألونك عن موقف العرب من غزة</div>
            
            <div class="meme-image">
                <div class="illustration">
                    <div class="heart-symbol">❤️</div>
                    <div class="solidarity-text">فلسطين في القلب</div>
                </div>
            </div>
            
            <div class="meme-text-bottom">قلوبنا مع غزة... دائماً وأبداً</div>
        </div>

        <div class="description">
            رغم كل التحديات والصعوبات، تبقى فلسطين وغزة في قلب كل عربي.
            <br>
            التضامن الحقيقي يبدأ من القلب ويترجم إلى أفعال.
            <br>
            <strong>لن ننسى... لن نتخلى... لن نستسلم</strong>
        </div>

        <div class="hashtags">
            <span class="hashtag">#غزة_في_القلب</span>
            <span class="hashtag">#فلسطين_حرة</span>
            <span class="hashtag">#التضامن_العربي</span>
            <span class="hashtag">#لن_ننسى</span>
            <span class="hashtag">#العدالة_لفلسطين</span>
        </div>

        <div class="footer">
            تم إنشاء هذا المحتوى للتعبير عن التضامن الإنساني والعربي مع الشعب الفلسطيني
        </div>
    </div>
</body>
</html>
