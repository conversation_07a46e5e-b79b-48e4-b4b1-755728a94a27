from moviepy.editor import *
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
import os

def create_text_clip(text, font_size=70, color='white', duration=5):
    # Reshape Arabic text
    reshaped_text = arabic_reshaper.reshape(text)
    bidi_text = get_display(reshaped_text)
    
    # Create text clip
    txt_clip = TextClip(bidi_text, fontsize=font_size, color=color, font='Arial')
    txt_clip = txt_clip.set_duration(duration)
    txt_clip = txt_clip.set_position('center')
    return txt_clip

def create_imam_jawad_video():
    # Create a black background
    background = ColorClip(size=(1920, 1080), color=(0, 0, 0))
    background = background.set_duration(20)  # 20 seconds video

    # Create text clips
    title = create_text_clip("ذكرى استشهاد الإمام محمد الجواد عليه السلام", font_size=80, duration=5)
    date = create_text_clip("29 ذي القعدة", font_size=60, duration=5)
    quote = create_text_clip("إن الله يحب كل قلب حزين", font_size=70, duration=5)
    
    # Position clips at different times
    title = title.set_start(0)
    date = date.set_start(6)
    quote = quote.set_start(12)

    # Combine all clips
    final_video = CompositeVideoClip([background, title, date, quote])
    
    # Add background music (you'll need to provide the audio file)
    # audio = AudioFileClip("background_music.mp3")
    # final_video = final_video.set_audio(audio)

    # Write the result to a file
    final_video.write_videofile("imam_jawad_commemoration.mp4", fps=24)

if __name__ == "__main__":
    create_imam_jawad_video() 