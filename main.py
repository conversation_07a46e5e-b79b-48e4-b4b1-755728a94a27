#!/usr/bin/env python3
"""
Professional Image Editor - Main Application Entry Point
A comprehensive image editing application with Arabic support and advanced features

Features:
- Professional image editing tools
- Layer management with blending modes
- Arabic text rendering (RTL)
- Batch processing capabilities
- Multiple export formats
- Customizable themes
- Drawing and annotation tools
- Advanced filters and effects

Author: AliToucan
Version: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # Import main application
    from image_editor import ImageEditor
    from config import APP_NAME, APP_VERSION
    
    def check_dependencies():
        """Check if all required dependencies are available"""
        missing_deps = []
        
        try:
            import PIL
        except ImportError:
            missing_deps.append("Pillow")
        
        try:
            import cv2
        except ImportError:
            missing_deps.append("opencv-python")
        
        try:
            import numpy
        except ImportError:
            missing_deps.append("numpy")
        
        try:
            import arabic_reshaper
        except ImportError:
            missing_deps.append("arabic-reshaper")
        
        try:
            import bidi
        except ImportError:
            missing_deps.append("python-bidi")
        
        if missing_deps:
            error_msg = f"Missing required dependencies:\n\n"
            error_msg += "\n".join(f"• {dep}" for dep in missing_deps)
            error_msg += f"\n\nPlease install them using:\n"
            error_msg += f"pip install {' '.join(missing_deps)}"
            
            # Show error in GUI if possible, otherwise print
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("Missing Dependencies", error_msg)
                root.destroy()
            except:
                print(error_msg)
            
            return False
        
        return True
    
    def setup_error_handling():
        """Setup global error handling"""
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            error_msg = "An unexpected error occurred:\n\n"
            error_msg += "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            
            print(error_msg)
            
            # Try to show error dialog
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("Application Error", 
                                   f"An unexpected error occurred. Please check the console for details.\n\n"
                                   f"Error: {exc_value}")
                root.destroy()
            except:
                pass
        
        sys.excepthook = handle_exception
    
    def create_sample_image():
        """Create a sample image for testing if no image is loaded"""
        from PIL import Image, ImageDraw, ImageFont
        from arabic_support import ArabicTextRenderer
        
        # Create sample image
        width, height = 800, 600
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # Add gradient background
        for y in range(height):
            color_value = int(255 * (1 - y / height * 0.3))
            color = (color_value, color_value + 20, color_value + 40)
            draw.line([(0, y), (width, y)], fill=color)
        
        # Add title
        try:
            font = ImageFont.truetype("arial.ttf", 48)
        except:
            font = ImageFont.load_default()
        
        title = f"{APP_NAME} v{APP_VERSION}"
        text_width = draw.textlength(title, font=font)
        text_x = (width - text_width) // 2
        text_y = height // 3
        
        # Add text shadow
        draw.text((text_x + 2, text_y + 2), title, font=font, fill='gray')
        draw.text((text_x, text_y), title, font=font, fill='darkblue')
        
        # Add subtitle
        try:
            subtitle_font = ImageFont.truetype("arial.ttf", 24)
        except:
            subtitle_font = ImageFont.load_default()
        
        subtitle = "Professional Image Editor with Arabic Support"
        subtitle_width = draw.textlength(subtitle, font=subtitle_font)
        subtitle_x = (width - subtitle_width) // 2
        subtitle_y = text_y + 80
        
        draw.text((subtitle_x, subtitle_y), subtitle, font=subtitle_font, fill='darkgreen')
        
        # Add Arabic text sample
        arabic_renderer = ArabicTextRenderer()
        if arabic_renderer.available_fonts:
            arabic_text = "محرر الصور الاحترافي"
            try:
                arabic_font = arabic_renderer.get_font(size=32)
                arabic_processed = arabic_renderer.process_arabic_text(arabic_text)
                
                # Calculate position for RTL text
                temp_img = Image.new('RGB', (1, 1))
                temp_draw = ImageDraw.Draw(temp_img)
                arabic_width, arabic_height = arabic_renderer.get_text_size(arabic_text, arabic_font)
                
                arabic_x = width - 50  # Right aligned
                arabic_y = subtitle_y + 60
                
                draw.text((arabic_x - arabic_width, arabic_y), arabic_processed, 
                         font=arabic_font, fill='darkred')
            except Exception as e:
                print(f"Could not render Arabic text: {e}")
        
        # Add feature list
        features = [
            "✓ Advanced editing tools",
            "✓ Layer support",
            "✓ Arabic text (RTL)",
            "✓ Batch processing",
            "✓ Multiple formats",
            "✓ Custom filters"
        ]
        
        try:
            feature_font = ImageFont.truetype("arial.ttf", 16)
        except:
            feature_font = ImageFont.load_default()
        
        feature_y = height // 2 + 50
        for i, feature in enumerate(features):
            draw.text((50, feature_y + i * 25), feature, font=feature_font, fill='black')
        
        # Add instructions
        instruction = "File → Open to load an image, or start creating!"
        try:
            inst_font = ImageFont.truetype("arial.ttf", 18)
        except:
            inst_font = ImageFont.load_default()
        
        inst_width = draw.textlength(instruction, font=inst_font)
        inst_x = (width - inst_width) // 2
        inst_y = height - 80
        
        draw.text((inst_x, inst_y), instruction, font=inst_font, fill='blue')
        
        return image
    
    def main():
        """Main application entry point"""
        print(f"Starting {APP_NAME} v{APP_VERSION}...")
        
        # Setup error handling
        setup_error_handling()
        
        # Check dependencies
        if not check_dependencies():
            return 1
        
        try:
            # Create and run application
            app = ImageEditor()
            
            # Load sample image if no arguments provided
            if len(sys.argv) == 1:
                try:
                    sample_image = create_sample_image()
                    app.current_image = sample_image
                    app.current_file_path = None
                    app.is_modified = False
                    
                    # Initialize layer manager with sample
                    from layer_manager import LayerManager, Layer
                    app.layer_manager = LayerManager(sample_image.size)
                    background_layer = Layer("Welcome", sample_image)
                    app.layer_manager.add_layer(background_layer)
                    
                    # Update UI
                    app.image_preview.set_image(sample_image)
                    app.update_layer_panel()
                    app.update_image_info()
                    app.update_status("Welcome! Load an image to start editing.")
                    
                except Exception as e:
                    print(f"Could not create sample image: {e}")
            
            # Load image from command line argument
            elif len(sys.argv) == 2:
                image_path = sys.argv[1]
                if os.path.exists(image_path):
                    app.load_image(image_path)
                else:
                    print(f"File not found: {image_path}")
            
            # Run application
            print("Application started successfully!")
            app.run()
            
            return 0
            
        except Exception as e:
            error_msg = f"Failed to start application: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("Startup Error", error_msg)
                root.destroy()
            except:
                pass
            
            return 1

except ImportError as e:
    print(f"Import error: {e}")
    print("Please make sure all required files are present and dependencies are installed.")
    sys.exit(1)

if __name__ == "__main__":
    sys.exit(main())
