"""
Build configuration for Professional Image Editor
Contains settings and utilities for creating standalone executable
"""

import os
import sys
from pathlib import Path

# Application information
APP_NAME = "Professional Image Editor"
APP_VERSION = "1.0.0"
APP_AUTHOR = "AliToucan"
APP_DESCRIPTION = "Professional Image Editor with Arabic Support"

# Build settings
BUILD_SETTINGS = {
    'app_name': 'Professional_Image_Editor',
    'main_script': 'main.py',
    'icon_file': 'app_icon.ico',
    'console': False,  # Set to True for debugging
    'onefile': True,   # Create single executable file
    'upx': True,       # Compress executable
    'clean': True,     # Clean build directory
}

# Files to include in distribution
INCLUDE_FILES = [
    'fonts',
    'README_ImageEditor.md',
    'requirements.txt',
]

# Python modules to include explicitly
HIDDEN_IMPORTS = [
    # Core GUI
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.colorchooser',
    
    # Image processing
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageEnhance',
    'PIL.ImageFilter',
    'PIL.ImageOps',
    'PIL._tkinter_finder',
    
    # Computer vision
    'cv2',
    'numpy',
    
    # Arabic support
    'arabic_reshaper',
    'bidi.algorithm',
    
    # Application modules
    'config',
    'utils',
    'image_processor',
    'filters',
    'layer_manager',
    'drawing_tools',
    'batch_processor',
    'arabic_support',
    'ui_components',
    'dialogs',
]

# Modules to exclude (reduce file size)
EXCLUDE_MODULES = [
    'matplotlib.tests',
    'numpy.tests',
    'PIL.tests',
    'cv2.tests',
    'test',
    'tests',
    'unittest',
    'doctest',
    'pdb',
    'pydoc',
]

def get_data_files():
    """Get list of data files to include"""
    data_files = []
    
    # Add fonts directory
    fonts_dir = Path('fonts')
    if fonts_dir.exists():
        for font_file in fonts_dir.glob('*.ttf'):
            data_files.append((str(font_file), 'fonts'))
    
    # Add individual files
    for file_path in INCLUDE_FILES:
        if Path(file_path).exists():
            if Path(file_path).is_file():
                data_files.append((file_path, '.'))
            elif Path(file_path).is_dir():
                # Add directory recursively
                for item in Path(file_path).rglob('*'):
                    if item.is_file():
                        rel_path = item.relative_to(file_path)
                        data_files.append((str(item), str(Path(file_path) / rel_path.parent)))
    
    return data_files

def create_version_file():
    """Create version file for Windows executable"""
    version_content = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'{APP_AUTHOR}'),
        StringStruct(u'FileDescription', u'{APP_DESCRIPTION}'),
        StringStruct(u'FileVersion', u'{APP_VERSION}'),
        StringStruct(u'InternalName', u'{BUILD_SETTINGS["app_name"]}'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024 {APP_AUTHOR}'),
        StringStruct(u'OriginalFilename', u'{BUILD_SETTINGS["app_name"]}.exe'),
        StringStruct(u'ProductName', u'{APP_NAME}'),
        StringStruct(u'ProductVersion', u'{APP_VERSION}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    return 'version_info.txt'

def get_pyinstaller_args():
    """Get PyInstaller command line arguments"""
    args = [
        '--name', BUILD_SETTINGS['app_name'],
        '--distpath', 'dist',
        '--workpath', 'build',
    ]
    
    if BUILD_SETTINGS['onefile']:
        args.append('--onefile')
    else:
        args.append('--onedir')
    
    if not BUILD_SETTINGS['console']:
        args.append('--windowed')
    
    if BUILD_SETTINGS['clean']:
        args.append('--clean')
    
    if BUILD_SETTINGS['upx']:
        args.append('--upx-dir')
        args.append('upx')  # Assumes UPX is in PATH or upx directory
    
    # Add icon
    if Path(BUILD_SETTINGS['icon_file']).exists():
        args.extend(['--icon', BUILD_SETTINGS['icon_file']])
    
    # Add version info (Windows only)
    if sys.platform == 'win32':
        version_file = create_version_file()
        args.extend(['--version-file', version_file])
    
    # Add hidden imports
    for module in HIDDEN_IMPORTS:
        args.extend(['--hidden-import', module])
    
    # Add excludes
    for module in EXCLUDE_MODULES:
        args.extend(['--exclude-module', module])
    
    # Add data files
    for src, dst in get_data_files():
        args.extend(['--add-data', f'{src}{os.pathsep}{dst}'])
    
    # Add main script
    args.append(BUILD_SETTINGS['main_script'])
    
    return args

def optimize_build():
    """Optimize the build for smaller size and better performance"""
    optimizations = {
        'strip_debug': True,
        'optimize_imports': True,
        'compress_executable': True,
        'remove_unused_modules': True,
    }
    
    return optimizations

# Platform-specific settings
PLATFORM_SETTINGS = {
    'win32': {
        'executable_extension': '.exe',
        'installer_format': 'nsis',  # Nullsoft Scriptable Install System
        'icon_format': 'ico',
    },
    'darwin': {
        'executable_extension': '.app',
        'installer_format': 'dmg',
        'icon_format': 'icns',
    },
    'linux': {
        'executable_extension': '',
        'installer_format': 'deb',
        'icon_format': 'png',
    }
}

def get_platform_settings():
    """Get platform-specific settings"""
    return PLATFORM_SETTINGS.get(sys.platform, PLATFORM_SETTINGS['linux'])

# Build hooks for special handling
BUILD_HOOKS = {
    'pre_build': [
        'check_dependencies',
        'create_icon',
        'prepare_fonts',
    ],
    'post_build': [
        'create_installer',
        'create_portable_package',
        'verify_executable',
    ]
}

def run_build_hooks(hook_type):
    """Run build hooks"""
    hooks = BUILD_HOOKS.get(hook_type, [])
    for hook in hooks:
        print(f"Running {hook_type} hook: {hook}")
        # Hook implementations would go here

# Distribution settings
DISTRIBUTION_SETTINGS = {
    'create_installer': True,
    'create_portable': True,
    'include_source': False,
    'include_documentation': True,
    'compress_distribution': True,
    'sign_executable': False,  # Code signing (requires certificate)
}

def get_distribution_name():
    """Generate distribution package name"""
    platform = get_platform_settings()
    arch = 'x64' if sys.maxsize > 2**32 else 'x86'
    
    return f"{BUILD_SETTINGS['app_name']}_v{APP_VERSION}_{sys.platform}_{arch}"

# Error handling and logging
import logging

def setup_build_logging():
    """Setup logging for build process"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('build.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger('build')

# Validation functions
def validate_build_environment():
    """Validate that build environment is ready"""
    checks = []
    
    # Check Python version
    if sys.version_info < (3, 7):
        checks.append(f"Python 3.7+ required, found {sys.version}")
    
    # Check main script exists
    if not Path(BUILD_SETTINGS['main_script']).exists():
        checks.append(f"Main script not found: {BUILD_SETTINGS['main_script']}")
    
    # Check required modules
    required_modules = ['PyInstaller', 'PIL', 'cv2', 'numpy']
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            checks.append(f"Required module not found: {module}")
    
    return checks

if __name__ == "__main__":
    print("Build Configuration for Professional Image Editor")
    print("=" * 50)
    
    # Validate environment
    issues = validate_build_environment()
    if issues:
        print("Build environment issues:")
        for issue in issues:
            print(f"  ✗ {issue}")
    else:
        print("✓ Build environment is ready")
    
    # Show configuration
    print(f"\nBuild Settings:")
    for key, value in BUILD_SETTINGS.items():
        print(f"  {key}: {value}")
    
    print(f"\nPlatform: {sys.platform}")
    print(f"Architecture: {'x64' if sys.maxsize > 2**32 else 'x86'}")
    print(f"Distribution name: {get_distribution_name()}")
