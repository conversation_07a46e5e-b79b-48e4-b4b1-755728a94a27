"""
Image filters and effects for the Professional Image Editor
Contains various artistic filters, vintage effects, and creative transformations
"""

import numpy as np
from PIL import Image, ImageFilter, ImageEnhance, ImageOps
import cv2
from typing import Dict, Any, Tuple
from config import FILTER_PRESETS

class FilterEngine:
    """Main filter engine for applying various effects"""
    
    def __init__(self):
        self.presets = FILTER_PRESETS
    
    def apply_preset(self, image: Image.Image, preset_name: str) -> Image.Image:
        """Apply a predefined filter preset"""
        if preset_name not in self.presets:
            return image
        
        preset = self.presets[preset_name]
        result = image.copy()
        
        # Apply each effect in the preset
        for effect, value in preset.items():
            if effect == 'sepia':
                result = self.sepia_effect(result, value)
            elif effect == 'contrast':
                result = ImageEnhance.Contrast(result).enhance(value)
            elif effect == 'brightness':
                result = ImageEnhance.Brightness(result).enhance(value)
            elif effect == 'saturation':
                result = ImageEnhance.Color(result).enhance(value)
            elif effect == 'temperature':
                result = self.temperature_adjustment(result, value)
            elif effect == 'blur':
                result = result.filter(ImageFilter.GaussianBlur(radius=value))
        
        return result
    
    def vintage_effect(self, image: Image.Image, intensity: float = 0.7) -> Image.Image:
        """Apply vintage/retro effect"""
        # Apply sepia tone
        vintage = self.sepia_effect(image, intensity * 0.5)
        
        # Reduce contrast slightly
        vintage = ImageEnhance.Contrast(vintage).enhance(0.9)
        
        # Add slight vignette
        vintage = self._add_vignette(vintage, intensity * 0.3)
        
        # Add film grain
        vintage = self._add_film_grain(vintage, intensity * 0.1)
        
        return vintage
    
    def sepia_effect(self, image: Image.Image, intensity: float = 1.0) -> Image.Image:
        """Apply sepia tone effect"""
        img_array = np.array(image)
        
        # Sepia transformation matrix
        sepia_filter = np.array([
            [0.393, 0.769, 0.189],
            [0.349, 0.686, 0.168],
            [0.272, 0.534, 0.131]
        ])
        
        sepia_img = img_array.dot(sepia_filter.T)
        sepia_img = np.clip(sepia_img, 0, 255).astype(np.uint8)
        
        # Blend with original
        if intensity < 1.0:
            sepia_img = (sepia_img * intensity + img_array * (1 - intensity)).astype(np.uint8)
        
        return Image.fromarray(sepia_img)
    
    def black_and_white_effect(self, image: Image.Image, method: str = 'luminance') -> Image.Image:
        """Convert to black and white with different methods"""
        if method == 'luminance':
            # Standard luminance conversion
            return image.convert('L').convert('RGB')
        elif method == 'red_channel':
            # Use red channel only
            r, g, b = image.split()
            return Image.merge('RGB', (r, r, r))
        elif method == 'green_channel':
            # Use green channel only
            r, g, b = image.split()
            return Image.merge('RGB', (g, g, g))
        elif method == 'blue_channel':
            # Use blue channel only
            r, g, b = image.split()
            return Image.merge('RGB', (b, b, b))
        elif method == 'high_contrast':
            # High contrast B&W
            bw = image.convert('L')
            bw = ImageEnhance.Contrast(bw).enhance(1.5)
            return bw.convert('RGB')
        else:
            return image.convert('L').convert('RGB')
    
    def temperature_adjustment(self, image: Image.Image, temperature: int) -> Image.Image:
        """Adjust color temperature (-500 to 500, negative = cooler, positive = warmer)"""
        img_array = np.array(image, dtype=np.float32)
        
        if temperature > 0:
            # Warmer - increase red, decrease blue
            factor = temperature / 500.0
            img_array[:, :, 0] *= (1 + factor * 0.3)  # Red
            img_array[:, :, 2] *= (1 - factor * 0.3)  # Blue
        else:
            # Cooler - decrease red, increase blue
            factor = abs(temperature) / 500.0
            img_array[:, :, 0] *= (1 - factor * 0.3)  # Red
            img_array[:, :, 2] *= (1 + factor * 0.3)  # Blue
        
        img_array = np.clip(img_array, 0, 255).astype(np.uint8)
        return Image.fromarray(img_array)
    
    def dramatic_effect(self, image: Image.Image) -> Image.Image:
        """Apply dramatic effect with high contrast and saturation"""
        # Increase contrast
        dramatic = ImageEnhance.Contrast(image).enhance(1.4)
        
        # Increase saturation
        dramatic = ImageEnhance.Color(dramatic).enhance(1.3)
        
        # Slight sharpening
        dramatic = ImageEnhance.Sharpness(dramatic).enhance(1.2)
        
        # Darken slightly
        dramatic = ImageEnhance.Brightness(dramatic).enhance(0.95)
        
        return dramatic
    
    def soft_effect(self, image: Image.Image) -> Image.Image:
        """Apply soft, dreamy effect"""
        # Slight blur
        soft = image.filter(ImageFilter.GaussianBlur(radius=1))
        
        # Increase brightness slightly
        soft = ImageEnhance.Brightness(soft).enhance(1.05)
        
        # Reduce contrast
        soft = ImageEnhance.Contrast(soft).enhance(0.9)
        
        # Blend with original
        blended = Image.blend(image, soft, 0.6)
        
        return blended
    
    def cross_process_effect(self, image: Image.Image) -> Image.Image:
        """Apply cross-processing effect"""
        # Convert to LAB color space simulation
        img_array = np.array(image, dtype=np.float32)
        
        # Adjust color curves to simulate cross-processing
        # Increase contrast in highlights, decrease in shadows
        img_array = img_array / 255.0
        
        # Apply S-curve to each channel
        for i in range(3):
            img_array[:, :, i] = self._apply_s_curve(img_array[:, :, i])
        
        # Color shift
        img_array[:, :, 0] *= 1.1  # Slight red boost
        img_array[:, :, 1] *= 0.95  # Slight green reduction
        img_array[:, :, 2] *= 1.05  # Slight blue boost
        
        img_array = np.clip(img_array * 255, 0, 255).astype(np.uint8)
        return Image.fromarray(img_array)
    
    def lomography_effect(self, image: Image.Image) -> Image.Image:
        """Apply lomography/toy camera effect"""
        # Add vignette
        lomo = self._add_vignette(image, 0.4)
        
        # Increase saturation
        lomo = ImageEnhance.Color(lomo).enhance(1.3)
        
        # Slight color shift
        lomo = self.temperature_adjustment(lomo, 50)
        
        # Add slight blur to edges
        lomo = self._add_edge_blur(lomo)
        
        return lomo
    
    def orton_effect(self, image: Image.Image) -> Image.Image:
        """Apply Orton effect (dreamy, glowing look)"""
        # Create overexposed copy
        overexposed = ImageEnhance.Brightness(image).enhance(1.5)
        overexposed = overexposed.filter(ImageFilter.GaussianBlur(radius=20))
        
        # Blend with original using overlay mode
        orton = self._overlay_blend(image, overexposed, 0.5)
        
        return orton
    
    def _apply_s_curve(self, channel: np.ndarray) -> np.ndarray:
        """Apply S-curve for contrast adjustment"""
        return 0.5 * (1 + np.tanh(4 * (channel - 0.5)))
    
    def _add_vignette(self, image: Image.Image, strength: float) -> Image.Image:
        """Add vignette effect"""
        width, height = image.size
        
        # Create vignette mask
        x, y = np.meshgrid(np.linspace(-1, 1, width), np.linspace(-1, 1, height))
        radius = np.sqrt(x**2 + y**2)
        
        # Create vignette
        vignette = 1 - strength * np.clip(radius - 0.5, 0, 1)
        vignette = np.stack([vignette] * 3, axis=-1)
        
        # Apply vignette
        img_array = np.array(image, dtype=np.float32)
        vignetted = img_array * vignette
        vignetted = np.clip(vignetted, 0, 255).astype(np.uint8)
        
        return Image.fromarray(vignetted)
    
    def _add_film_grain(self, image: Image.Image, intensity: float) -> Image.Image:
        """Add film grain noise"""
        img_array = np.array(image, dtype=np.float32)
        
        # Generate noise
        noise = np.random.normal(0, intensity * 255, img_array.shape)
        
        # Add noise to image
        grainy = img_array + noise
        grainy = np.clip(grainy, 0, 255).astype(np.uint8)
        
        return Image.fromarray(grainy)
    
    def _add_edge_blur(self, image: Image.Image) -> Image.Image:
        """Add blur to edges while keeping center sharp"""
        width, height = image.size
        
        # Create mask for center sharpness
        x, y = np.meshgrid(np.linspace(-1, 1, width), np.linspace(-1, 1, height))
        radius = np.sqrt(x**2 + y**2)
        mask = np.clip(1 - radius, 0, 1)
        
        # Create blurred version
        blurred = image.filter(ImageFilter.GaussianBlur(radius=2))
        
        # Blend based on mask
        img_array = np.array(image, dtype=np.float32)
        blur_array = np.array(blurred, dtype=np.float32)
        
        mask_3d = np.stack([mask] * 3, axis=-1)
        result = img_array * mask_3d + blur_array * (1 - mask_3d)
        result = np.clip(result, 0, 255).astype(np.uint8)
        
        return Image.fromarray(result)
    
    def _overlay_blend(self, base: Image.Image, overlay: Image.Image, opacity: float) -> Image.Image:
        """Blend two images using overlay mode"""
        base_array = np.array(base, dtype=np.float32) / 255.0
        overlay_array = np.array(overlay, dtype=np.float32) / 255.0
        
        # Overlay blend mode
        mask = base_array < 0.5
        result = np.where(mask, 
                         2 * base_array * overlay_array,
                         1 - 2 * (1 - base_array) * (1 - overlay_array))
        
        # Apply opacity
        result = base_array * (1 - opacity) + result * opacity
        result = np.clip(result * 255, 0, 255).astype(np.uint8)
        
        return Image.fromarray(result)
    
    def get_available_filters(self) -> list:
        """Get list of available filter names"""
        return list(self.presets.keys()) + [
            'Vintage', 'Black & White', 'Dramatic', 'Soft', 
            'Cross Process', 'Lomography', 'Orton Effect'
        ]
