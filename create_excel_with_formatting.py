#!/usr/bin/env python3
"""
Create Excel file with professional formatting using xlsxwriter
This creates a native Excel file with all formatting features
"""

import csv
import os
from datetime import datetime

def create_excel_with_xlsxwriter():
    """Create Excel file using xlsxwriter for professional formatting"""
    
    try:
        import xlsxwriter
    except ImportError:
        print("xlsxwriter not available. Creating alternative Excel file...")
        create_formatted_excel_alternative()
        return
    
    # Read CSV data
    csv_file = 'GDP_Analysis_Comprehensive.csv'
    if not os.path.exists(csv_file):
        print(f"Error: {csv_file} not found!")
        return
    
    data = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            data.append(row)
    
    # Create workbook
    workbook = xlsxwriter.Workbook('GDP_Analysis_Professional.xlsx')
    
    # Define formats
    header_format = workbook.add_format({
        'bold': True,
        'font_color': 'white',
        'bg_color': '#2C3E50',
        'border': 1,
        'align': 'center',
        'valign': 'vcenter',
        'text_wrap': True,
        'font_size': 11
    })
    
    currency_format = workbook.add_format({
        'num_format': '$#,##0.0',
        'border': 1,
        'align': 'center'
    })
    
    percentage_format = workbook.add_format({
        'num_format': '0.0%',
        'border': 1,
        'align': 'center'
    })
    
    number_format = workbook.add_format({
        'num_format': '#,##0',
        'border': 1,
        'align': 'center'
    })
    
    text_format = workbook.add_format({
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })
    
    rank_format = workbook.add_format({
        'bold': True,
        'font_color': 'white',
        'bg_color': '#E74C3C',
        'border': 1,
        'align': 'center'
    })
    
    # Create Raw Data worksheet
    worksheet = workbook.add_worksheet('البيانات الخام - Raw Data')
    
    # Write headers
    if data:
        headers = data[0]
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # Write data
        for row_idx, row in enumerate(data[1:], 1):
            for col_idx, cell in enumerate(row):
                if col_idx == 0:  # Rank
                    worksheet.write(row_idx, col_idx, int(cell), rank_format)
                elif col_idx in [1, 2]:  # Country names
                    worksheet.write(row_idx, col_idx, cell, text_format)
                elif col_idx >= 3 and col_idx <= 9:  # GDP columns
                    try:
                        worksheet.write(row_idx, col_idx, float(cell), currency_format)
                    except:
                        worksheet.write(row_idx, col_idx, cell, text_format)
                elif col_idx == 10:  # Population
                    try:
                        worksheet.write(row_idx, col_idx, float(cell), number_format)
                    except:
                        worksheet.write(row_idx, col_idx, cell, text_format)
                elif col_idx == 11:  # GDP Per Capita
                    try:
                        worksheet.write(row_idx, col_idx, float(cell), number_format)
                    except:
                        worksheet.write(row_idx, col_idx, cell, text_format)
                elif col_idx == 12:  # Growth Rate
                    try:
                        worksheet.write(row_idx, col_idx, float(cell)/100, percentage_format)
                    except:
                        worksheet.write(row_idx, col_idx, cell, text_format)
                else:  # Sector percentages
                    try:
                        worksheet.write(row_idx, col_idx, float(cell), number_format)
                    except:
                        worksheet.write(row_idx, col_idx, cell, text_format)
    
    # Set column widths
    column_widths = [8, 18, 20, 15, 15, 15, 15, 15, 15, 15, 12, 15, 12, 10, 10, 10]
    for i, width in enumerate(column_widths):
        worksheet.set_column(i, i, width)
    
    # Add conditional formatting
    worksheet.conditional_format('E2:E21', {
        'type': '3_color_scale',
        'min_color': '#FFEB9C',
        'mid_color': '#FFEB9C', 
        'max_color': '#63BE7B'
    })
    
    # Create Analysis worksheet
    analysis_ws = workbook.add_worksheet('التحليل - Analysis')
    
    # Add summary statistics
    title_format = workbook.add_format({
        'bold': True,
        'font_size': 16,
        'font_color': '#2C3E50'
    })
    
    analysis_ws.write('A1', 'GDP Analysis Summary - ملخص تحليل الناتج المحلي', title_format)
    
    summary_data = [
        ['Statistic', 'Value'],
        ['Total GDP Top 10 (Trillion USD)', '74.5'],
        ['Average Growth Rate (%)', '2.7'],
        ['Total Population (Billion)', '4.6'],
        ['Largest Economy', 'United States'],
        ['Fastest Growing Economy', 'India (6.4%)'],
        ['Highest GDP Per Capita', 'Netherlands ($69,726)']
    ]
    
    for row_idx, row in enumerate(summary_data, 3):
        for col_idx, cell in enumerate(row):
            if row_idx == 3:  # Header row
                analysis_ws.write(row_idx, col_idx, cell, header_format)
            else:
                analysis_ws.write(row_idx, col_idx, cell, text_format)
    
    # Create Charts worksheet
    charts_ws = workbook.add_worksheet('الرسوم البيانية - Charts')
    
    # Add chart for top 10 countries
    chart = workbook.add_chart({'type': 'column'})
    chart.add_series({
        'name': 'GDP 2024',
        'categories': ['البيانات الخام - Raw Data', 1, 1, 10, 1],
        'values': ['البيانات الخام - Raw Data', 1, 4, 10, 4],
        'fill': {'color': '#3498DB'}
    })
    
    chart.set_title({'name': 'Top 10 Economies by GDP 2024'})
    chart.set_x_axis({'name': 'Countries'})
    chart.set_y_axis({'name': 'GDP (Billion USD)'})
    chart.set_size({'width': 720, 'height': 480})
    
    charts_ws.insert_chart('A2', chart)
    
    workbook.close()
    print("Professional Excel file created: GDP_Analysis_Professional.xlsx")

def create_formatted_excel_alternative():
    """Create Excel file using basic Python libraries"""
    
    # Create XML-based Excel file
    excel_xml = '''<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 
<Styles>
 <Style ss:ID="Header">
  <Font ss:Bold="1" ss:Color="#FFFFFF"/>
  <Interior ss:Color="#2C3E50" ss:Pattern="Solid"/>
  <Borders>
   <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
  </Borders>
  <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
 </Style>
 
 <Style ss:ID="Currency">
  <NumberFormat ss:Format="$#,##0.0"/>
  <Borders>
   <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
  </Borders>
  <Alignment ss:Horizontal="Center"/>
 </Style>
 
 <Style ss:ID="Percentage">
  <NumberFormat ss:Format="0.0%"/>
  <Borders>
   <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
  </Borders>
  <Alignment ss:Horizontal="Center"/>
 </Style>
 
 <Style ss:ID="Rank">
  <Font ss:Bold="1" ss:Color="#FFFFFF"/>
  <Interior ss:Color="#E74C3C" ss:Pattern="Solid"/>
  <Borders>
   <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
   <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
  </Borders>
  <Alignment ss:Horizontal="Center"/>
 </Style>
</Styles>

<Worksheet ss:Name="البيانات الخام - Raw Data">
<Table>'''
    
    # Read CSV and add data
    csv_file = 'GDP_Analysis_Comprehensive.csv'
    if os.path.exists(csv_file):
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            data = list(reader)
            
            # Add header row
            excel_xml += '<Row>'
            for header in data[0]:
                excel_xml += f'<Cell ss:StyleID="Header"><Data ss:Type="String">{header}</Data></Cell>'
            excel_xml += '</Row>'
            
            # Add data rows
            for row in data[1:]:
                excel_xml += '<Row>'
                for i, cell in enumerate(row):
                    if i == 0:  # Rank
                        excel_xml += f'<Cell ss:StyleID="Rank"><Data ss:Type="Number">{cell}</Data></Cell>'
                    elif i in [1, 2]:  # Country names
                        excel_xml += f'<Cell><Data ss:Type="String">{cell}</Data></Cell>'
                    elif i >= 3 and i <= 9:  # GDP columns
                        try:
                            excel_xml += f'<Cell ss:StyleID="Currency"><Data ss:Type="Number">{float(cell)}</Data></Cell>'
                        except:
                            excel_xml += f'<Cell><Data ss:Type="String">{cell}</Data></Cell>'
                    elif i == 12:  # Growth rate
                        try:
                            excel_xml += f'<Cell ss:StyleID="Percentage"><Data ss:Type="Number">{float(cell)/100}</Data></Cell>'
                        except:
                            excel_xml += f'<Cell><Data ss:Type="String">{cell}</Data></Cell>'
                    else:
                        excel_xml += f'<Cell><Data ss:Type="String">{cell}</Data></Cell>'
                excel_xml += '</Row>'
    
    excel_xml += '''</Table>
</Worksheet>
</Workbook>'''
    
    with open('GDP_Analysis_Professional.xml', 'w', encoding='utf-8') as f:
        f.write(excel_xml)
    
    print("Excel XML file created: GDP_Analysis_Professional.xml")
    print("This can be opened in Excel and saved as .xlsx")

if __name__ == "__main__":
    print("Creating Professional Excel with Advanced Formatting...")
    create_excel_with_xlsxwriter()
    print("Done!")
