@echo off
echo Professional Image Editor Installer
echo =====================================
echo.

REM Check if the executable exists
if not exist "Professional_Image_Editor.exe" (
    echo Error: Professional_Image_Editor.exe not found!
    echo Please make sure you're running this from the correct directory.
    pause
    exit /b 1
)

REM Create installation directory
set INSTALL_DIR=%PROGRAMFILES%\Professional Image Editor
echo Creating installation directory: %INSTALL_DIR%
mkdir "%INSTALL_DIR%" 2>nul

REM Copy files
echo Copying application files...
copy "Professional_Image_Editor.exe" "%INSTALL_DIR%\" >nul
if exist "README_ImageEditor.md" copy "README_ImageEditor.md" "%INSTALL_DIR%\" >nul
if exist "app_icon.ico" copy "app_icon.ico" "%INSTALL_DIR%\" >nul

REM Create desktop shortcut
echo Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\Professional Image Editor.lnk" >> "%TEMP%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\shortcut.vbs"
echo oLink.TargetPath = "%INSTALL_DIR%\Professional_Image_Editor.exe" >> "%TEMP%\shortcut.vbs"
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> "%TEMP%\shortcut.vbs"
echo oLink.Description = "Professional Image Editor with Arabic Support" >> "%TEMP%\shortcut.vbs"
if exist "%INSTALL_DIR%\app_icon.ico" echo oLink.IconLocation = "%INSTALL_DIR%\app_icon.ico" >> "%TEMP%\shortcut.vbs"
echo oLink.Save >> "%TEMP%\shortcut.vbs"
cscript "%TEMP%\shortcut.vbs" >nul
del "%TEMP%\shortcut.vbs"

REM Create start menu entry
echo Creating start menu entry...
set STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs
mkdir "%STARTMENU%\Professional Image Editor" 2>nul
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\startmenu.vbs"
echo sLinkFile = "%STARTMENU%\Professional Image Editor\Professional Image Editor.lnk" >> "%TEMP%\startmenu.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\startmenu.vbs"
echo oLink.TargetPath = "%INSTALL_DIR%\Professional_Image_Editor.exe" >> "%TEMP%\startmenu.vbs"
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> "%TEMP%\startmenu.vbs"
echo oLink.Description = "Professional Image Editor with Arabic Support" >> "%TEMP%\startmenu.vbs"
if exist "%INSTALL_DIR%\app_icon.ico" echo oLink.IconLocation = "%INSTALL_DIR%\app_icon.ico" >> "%TEMP%\startmenu.vbs"
echo oLink.Save >> "%TEMP%\startmenu.vbs"
cscript "%TEMP%\startmenu.vbs" >nul
del "%TEMP%\startmenu.vbs"

echo.
echo Installation completed successfully!
echo.
echo The application has been installed to: %INSTALL_DIR%
echo Desktop shortcut created: %DESKTOP%\Professional Image Editor.lnk
echo Start menu entry created in: Professional Image Editor
echo.
echo You can now run the application from the desktop shortcut or start menu.
echo.
pause
