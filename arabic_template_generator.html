<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد القوالب العربية - أنشئ تصاميمك بالعربية</title>

    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- html2canvas for image download -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', 'Amiri', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-family: 'Amiri', serif;
            font-size: 3rem;
            color: #2c3e50;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 1.2rem;
            color: #7f8c8d;
            font-weight: 300;
        }

        /* Main Content Layout */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        /* Upload Section */
        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .section-title {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(52, 152, 219, 0.05);
            margin-bottom: 20px;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: rgba(52, 152, 219, 0.1);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: #3498db;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 10px;
        }

        .upload-subtext {
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        #imageInput {
            display: none;
        }

        .uploaded-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin-top: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Text Input Section */
        .text-input-group {
            margin-bottom: 20px;
        }

        .text-input-group label {
            display: block;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .text-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1.1rem;
            direction: rtl;
            text-align: right;
            transition: border-color 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .text-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.2);
        }

        .textarea-input {
            min-height: 100px;
            resize: vertical;
        }

        /* Styling Options */
        .styling-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .option-group {
            display: flex;
            flex-direction: column;
        }

        .option-group label {
            font-size: 0.9rem;
            color: #555;
            margin-bottom: 5px;
        }

        .option-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-family: 'Noto Sans Arabic', sans-serif;
            direction: rtl;
        }

        .color-input {
            width: 50px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        .range-input {
            direction: ltr;
        }

        /* Preview Section */
        .preview-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .preview-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            border: 2px dashed #dee2e6;
        }

        .preview-placeholder {
            color: #6c757d;
            font-size: 1.1rem;
        }

        #previewCanvas {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Format Selection */
        .format-selection {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .format-btn {
            padding: 10px 20px;
            border: 2px solid #3498db;
            background: transparent;
            color: #3498db;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .format-btn.active {
            background: #3498db;
            color: white;
        }

        .format-btn:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            width: 100%;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #2980b9, #1f4e79);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(45deg, #229954, #1e8449);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(45deg, #e67e22, #d35400);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        /* Position Controls */
        .position-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .position-btn {
            padding: 10px;
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .position-btn.active {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }

        .position-btn:hover {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 40px;
        }

        .footer p {
            color: #7f8c8d;
            font-size: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .styling-options {
                grid-template-columns: 1fr;
            }

            .format-selection {
                flex-direction: column;
                align-items: center;
            }

            .format-btn {
                width: 200px;
            }

            .position-controls {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
            }
        }

        /* Loading Animation */
        .loading {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 1000;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎨 مولد القوالب العربية</h1>
            <p>أنشئ تصاميمك المخصصة بالنصوص العربية بسهولة ومهنية</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Upload and Text Input Section -->
            <div class="upload-section">
                <h2 class="section-title">📤 رفع الصورة والنص</h2>

                <!-- Image Upload -->
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">اسحب وأفلت الصورة هنا أو انقر للاختيار</div>
                    <div class="upload-subtext">يدعم: JPG, PNG, GIF (حد أقصى 10 ميجابايت)</div>
                    <input type="file" id="imageInput" accept="image/*">
                    <img id="uploadedImage" class="uploaded-image" style="display: none;">
                </div>

                <!-- Text Input -->
                <div class="text-input-group">
                    <label for="arabicText">النص العربي:</label>
                    <textarea id="arabicText" class="text-input textarea-input" placeholder="اكتب النص العربي هنا..."></textarea>
                </div>

                <!-- Font Selection -->
                <div class="text-input-group">
                    <label for="fontFamily">نوع الخط:</label>
                    <select id="fontFamily" class="text-input">
                        <option value="Amiri">أميري (Amiri)</option>
                        <option value="Scheherazade New">شهرزاد (Scheherazade)</option>
                        <option value="Noto Sans Arabic">نوتو العربية (Noto Sans Arabic)</option>
                    </select>
                </div>

                <!-- Styling Options -->
                <div class="styling-options">
                    <div class="option-group">
                        <label for="fontSize">حجم الخط:</label>
                        <input type="range" id="fontSize" class="range-input" min="20" max="120" value="48">
                        <span id="fontSizeValue">48px</span>
                    </div>
                    <div class="option-group">
                        <label for="textColor">لون النص:</label>
                        <input type="color" id="textColor" class="color-input" value="#ffffff">
                    </div>
                    <div class="option-group">
                        <label for="backgroundColor">لون الخلفية:</label>
                        <input type="color" id="backgroundColor" class="color-input" value="#000000">
                    </div>
                    <div class="option-group">
                        <label for="backgroundOpacity">شفافية الخلفية:</label>
                        <input type="range" id="backgroundOpacity" class="range-input" min="0" max="100" value="70">
                        <span id="backgroundOpacityValue">70%</span>
                    </div>
                </div>

                <!-- Text Position -->
                <div class="text-input-group">
                    <label>موضع النص:</label>
                    <div class="position-controls">
                        <button class="position-btn" data-position="top">أعلى</button>
                        <button class="position-btn active" data-position="center">وسط</button>
                        <button class="position-btn" data-position="bottom">أسفل</button>
                    </div>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="preview-section">
                <h2 class="section-title">👁️ معاينة التصميم</h2>

                <!-- Format Selection -->
                <div class="format-selection">
                    <button class="format-btn active" data-format="square">مربع (1:1)</button>
                    <button class="format-btn" data-format="story">ستوري (9:16)</button>
                </div>

                <!-- Preview Container -->
                <div class="preview-container">
                    <div class="preview-placeholder" id="previewPlaceholder">
                        قم برفع صورة وإدخال نص لرؤية المعاينة
                    </div>
                    <canvas id="previewCanvas" style="display: none;"></canvas>
                </div>

                <!-- Action Buttons -->
                <div class="btn-group">
                    <button class="btn btn-primary" id="generateBtn" disabled>
                        <i class="fas fa-magic"></i>
                        إنشاء التصميم
                    </button>
                    <button class="btn btn-secondary" id="downloadBtn" disabled>
                        <i class="fas fa-download"></i>
                        تحميل الصورة
                    </button>
                    <button class="btn btn-warning" id="resetBtn">
                        <i class="fas fa-redo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2024 مولد القوالب العربية - أداة مجانية لإنشاء التصاميم العربية</p>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div class="loading" id="loadingIndicator">
        <div class="spinner"></div>
        <p>جاري تحميل الصورة...</p>
    </div>

    <script src="template_generator.js"></script>
</body>
</html>
