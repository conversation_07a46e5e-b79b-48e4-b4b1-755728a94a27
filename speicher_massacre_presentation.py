#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional PowerPoint Presentation Generator
Speicher (Camp Speicher) Massacre Commemoration
For Iraqi/Arab Community - Memorial and Historical Documentation
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE
import arabic_reshaper
from bidi.algorithm import get_display
import os

class SpecherMassacrePresentation:
    def __init__(self):
        self.prs = Presentation()
        self.prs.slide_width = Inches(16)
        self.prs.slide_height = Inches(9)

        # Memorial/mourning colors
        self.colors = {
            'black': RGBColor(0, 0, 0),
            'dark_navy': RGBColor(26, 26, 46),
            'dark_green': RGBColor(15, 52, 96),
            'white': RGBColor(255, 255, 255),
            'gold': RGBColor(212, 175, 55),
            'blood_red': RGBColor(139, 0, 0)
        }

    def format_arabic_text(self, text):
        """Format Arabic text for proper RTL display"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text

    def add_title_slide(self):
        """Title slide for Speicher massacre commemoration"""
        slide_layout = self.prs.slide_layouts[6]  # Blank layout
        slide = self.prs.slides.add_slide(slide_layout)

        # Background
        background = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE, 0, 0,
            self.prs.slide_width, self.prs.slide_height
        )
        background.fill.solid()
        background.fill.fore_color.rgb = self.colors['black']
        background.line.fill.background()

        # Main title in Arabic
        title_text = "ذكرى مجزرة سبايكر"
        title_box = slide.shapes.add_textbox(
            Inches(1), Inches(1.5), Inches(14), Inches(2)
        )
        title_frame = title_box.text_frame
        title_frame.text = self.format_arabic_text(title_text)

        # Format title
        p = title_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(56)
        p.font.color.rgb = self.colors['gold']
        p.font.bold = True

        # Subtitle in Arabic
        subtitle_text = "تخليداً لذكرى الشهداء الأبرار"
        subtitle_box = slide.shapes.add_textbox(
            Inches(1), Inches(3.5), Inches(14), Inches(1.5)
        )
        subtitle_frame = subtitle_box.text_frame
        subtitle_frame.text = self.format_arabic_text(subtitle_text)

        # Format subtitle
        p = subtitle_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(32)
        p.font.color.rgb = self.colors['white']

        # English subtitle
        eng_subtitle_text = "Camp Speicher Massacre Memorial\nIn Memory of the Martyred Heroes"
        eng_subtitle_box = slide.shapes.add_textbox(
            Inches(1), Inches(5), Inches(14), Inches(1.5)
        )
        eng_subtitle_frame = eng_subtitle_box.text_frame
        eng_subtitle_frame.text = eng_subtitle_text

        # Format English subtitle
        for p in eng_subtitle_frame.paragraphs:
            p.alignment = PP_ALIGN.CENTER
            p.font.name = 'Arial'
            p.font.size = Pt(24)
            p.font.color.rgb = self.colors['white']

        # Date
        date_text = "١٢ حزيران ٢٠١٤ - June 12, 2014"
        date_box = slide.shapes.add_textbox(
            Inches(1), Inches(6.8), Inches(14), Inches(1)
        )
        date_frame = date_box.text_frame
        date_frame.text = self.format_arabic_text(date_text)

        # Format date
        p = date_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(20)
        p.font.color.rgb = self.colors['blood_red']
        p.font.bold = True

        return slide

    def add_background_slide(self):
        """Historical background of the massacre"""
        slide_layout = self.prs.slide_layouts[6]
        slide = self.prs.slides.add_slide(slide_layout)

        # Background
        background = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE, 0, 0,
            self.prs.slide_width, self.prs.slide_height
        )
        background.fill.solid()
        background.fill.fore_color.rgb = self.colors['dark_navy']
        background.line.fill.background()

        # Title
        title_text = "الخلفية التاريخية لمجزرة سبايكر"
        title_box = slide.shapes.add_textbox(
            Inches(1), Inches(0.5), Inches(14), Inches(1)
        )
        title_frame = title_box.text_frame
        title_frame.text = self.format_arabic_text(title_text)

        p = title_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(32)
        p.font.color.rgb = self.colors['gold']
        p.font.bold = True

        # Background content in Arabic
        background_content = """
• قاعدة سبايكر العسكرية قرب تكريت في محافظة صلاح الدين
• كانت تضم آلاف الجنود العراقيين من مختلف المحافظات
• سقطت القاعدة بيد تنظيم داعش الإرهابي في يونيو ٢٠١٤
• تم أسر المئات من الجنود العراقيين العزل
• ارتكب التنظيم الإرهابي جريمة إبادة جماعية بحق الأسرى
• استهدف التنظيم بشكل خاص الجنود الشيعة والأقليات
        """

        content_box = slide.shapes.add_textbox(
            Inches(1), Inches(2), Inches(14), Inches(4.5)
        )
        content_frame = content_box.text_frame
        content_frame.text = self.format_arabic_text(background_content.strip())

        for p in content_frame.paragraphs:
            p.alignment = PP_ALIGN.RIGHT
            p.font.name = 'Amiri'
            p.font.size = Pt(18)
            p.font.color.rgb = self.colors['white']
            p.space_after = Pt(12)

        # English translation
        eng_content = """Historical Background of the Speicher Massacre:
• Camp Speicher military base near Tikrit in Salah al-Din province
• Housed thousands of Iraqi soldiers from various provinces
• The base fell to ISIS terrorist organization in June 2014
• Hundreds of unarmed Iraqi soldiers were captured
• The terrorist organization committed genocide against the prisoners
• The organization specifically targeted Shia soldiers and minorities"""

        eng_box = slide.shapes.add_textbox(
            Inches(1), Inches(6.8), Inches(14), Inches(1.8)
        )
        eng_frame = eng_box.text_frame
        eng_frame.text = eng_content

        for p in eng_frame.paragraphs:
            p.alignment = PP_ALIGN.LEFT
            p.font.name = 'Arial'
            p.font.size = Pt(14)
            p.font.color.rgb = self.colors['white']

        return slide

    def add_timeline_slide(self):
        """Timeline of the tragic events"""
        slide_layout = self.prs.slide_layouts[6]
        slide = self.prs.slides.add_slide(slide_layout)

        # Background
        background = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE, 0, 0,
            self.prs.slide_width, self.prs.slide_height
        )
        background.fill.solid()
        background.fill.fore_color.rgb = self.colors['dark_green']
        background.line.fill.background()

        # Title
        title_text = "التسلسل الزمني للأحداث المأساوية"
        title_box = slide.shapes.add_textbox(
            Inches(1), Inches(0.5), Inches(14), Inches(1)
        )
        title_frame = title_box.text_frame
        title_frame.text = self.format_arabic_text(title_text)

        p = title_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(32)
        p.font.color.rgb = self.colors['gold']
        p.font.bold = True

        # Timeline content
        timeline_content = """
١٠ يونيو ٢٠١٤: سقوط الموصل بيد داعش
١١ يونيو ٢٠١٤: تقدم داعش نحو تكريت وسبايكر
١٢ يونيو ٢٠١٤: سقوط قاعدة سبايكر وأسر الجنود
١٢-١٥ يونيو ٢٠١٤: عمليات الإعدام الجماعي
١٦ يونيو ٢٠١٤: نشر داعش صور الجريمة
٢٠١٥-٢٠١٦: تحرير المنطقة واكتشاف المقابر الجماعية
٢٠١٨-٢٠٢١: محاكمات المتورطين في الجريمة
        """

        content_box = slide.shapes.add_textbox(
            Inches(1), Inches(2), Inches(14), Inches(4.5)
        )
        content_frame = content_box.text_frame
        content_frame.text = self.format_arabic_text(timeline_content.strip())

        for p in content_frame.paragraphs:
            p.alignment = PP_ALIGN.RIGHT
            p.font.name = 'Amiri'
            p.font.size = Pt(18)
            p.font.color.rgb = self.colors['white']
            p.space_after = Pt(12)

        # English timeline
        eng_timeline = """Timeline of Tragic Events:
June 10, 2014: Fall of Mosul to ISIS
June 11, 2014: ISIS advance toward Tikrit and Speicher
June 12, 2014: Fall of Camp Speicher and capture of soldiers
June 12-15, 2014: Mass execution operations
June 16, 2014: ISIS publishes images of the crime
2015-2016: Liberation of the area and discovery of mass graves
2018-2021: Trials of those involved in the crime"""

        eng_box = slide.shapes.add_textbox(
            Inches(1), Inches(6.8), Inches(14), Inches(1.8)
        )
        eng_frame = eng_box.text_frame
        eng_frame.text = eng_timeline

        for p in eng_frame.paragraphs:
            p.alignment = PP_ALIGN.LEFT
            p.font.name = 'Arial'
            p.font.size = Pt(14)
            p.font.color.rgb = self.colors['white']

        return slide

    def add_memorial_slide(self):
        """Memorial slide honoring the victims"""
        slide_layout = self.prs.slide_layouts[6]
        slide = self.prs.slides.add_slide(slide_layout)

        # Background
        background = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE, 0, 0,
            self.prs.slide_width, self.prs.slide_height
        )
        background.fill.solid()
        background.fill.fore_color.rgb = self.colors['black']
        background.line.fill.background()

        # Title
        title_text = "تكريم ذكرى الشهداء الأبرار"
        title_box = slide.shapes.add_textbox(
            Inches(1), Inches(0.5), Inches(14), Inches(1)
        )
        title_frame = title_box.text_frame
        title_frame.text = self.format_arabic_text(title_text)

        p = title_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(32)
        p.font.color.rgb = self.colors['gold']
        p.font.bold = True

        # Memorial content
        memorial_content = """
• أكثر من ١٧٠٠ شهيد من أبناء العراق الأبرار
• جنود شباب في مقتبل العمر ضحوا بأرواحهم للوطن
• من جميع محافظات العراق ومن مختلف الطوائف والقوميات
• استشهدوا دفاعاً عن كرامة العراق وأرضه المقدسة
• أرواحهم الطاهرة ترفرف في جنان الخلد
• ذكراهم العطرة محفورة في قلوب العراقيين
• دماؤهم الزكية لن تذهب هدراً
        """

        content_box = slide.shapes.add_textbox(
            Inches(1), Inches(2), Inches(14), Inches(4)
        )
        content_frame = content_box.text_frame
        content_frame.text = self.format_arabic_text(memorial_content.strip())

        for p in content_frame.paragraphs:
            p.alignment = PP_ALIGN.RIGHT
            p.font.name = 'Amiri'
            p.font.size = Pt(18)
            p.font.color.rgb = self.colors['white']
            p.space_after = Pt(12)

        # Memorial prayer
        prayer_text = "رحمهم الله وأسكنهم فسيح جناته"
        prayer_box = slide.shapes.add_textbox(
            Inches(1), Inches(6.5), Inches(14), Inches(1)
        )
        prayer_frame = prayer_box.text_frame
        prayer_frame.text = self.format_arabic_text(prayer_text)

        p = prayer_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(28)
        p.font.color.rgb = self.colors['gold']
        p.font.bold = True

        # English translation
        eng_memorial = """Honoring the Memory of the Righteous Martyrs:
• More than 1700 martyrs from the noble sons of Iraq
• Young soldiers in the prime of life who sacrificed their souls for the homeland
• From all provinces of Iraq and from different sects and ethnicities
• They were martyred defending the dignity of Iraq and its sacred land
• Their pure souls soar in the gardens of eternity
• Their fragrant memory is engraved in the hearts of Iraqis
• Their pure blood will not be shed in vain

May Allah have mercy on them and grant them spacious gardens"""

        eng_box = slide.shapes.add_textbox(
            Inches(1), Inches(7.8), Inches(14), Inches(1)
        )
        eng_frame = eng_box.text_frame
        eng_frame.text = eng_memorial

        for p in eng_frame.paragraphs:
            p.alignment = PP_ALIGN.LEFT
            p.font.name = 'Arial'
            p.font.size = Pt(12)
            p.font.color.rgb = self.colors['white']

        return slide

    def add_impact_slide(self):
        """Impact and significance of the tragedy"""
        slide_layout = self.prs.slide_layouts[6]
        slide = self.prs.slides.add_slide(slide_layout)

        # Background
        background = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE, 0, 0,
            self.prs.slide_width, self.prs.slide_height
        )
        background.fill.solid()
        background.fill.fore_color.rgb = self.colors['dark_navy']
        background.line.fill.background()

        # Title
        title_text = "الأثر والدلالات التاريخية للمجزرة"
        title_box = slide.shapes.add_textbox(
            Inches(1), Inches(0.5), Inches(14), Inches(1)
        )
        title_frame = title_box.text_frame
        title_frame.text = self.format_arabic_text(title_text)

        p = title_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(32)
        p.font.color.rgb = self.colors['gold']
        p.font.bold = True

        # Impact content
        impact_content = """
• نقطة تحول في تاريخ العراق المعاصر
• كشفت وحشية التنظيمات الإرهابية
• عززت الوحدة الوطنية العراقية ضد الإرهاب
• أدت إلى تشكيل الحشد الشعبي للدفاع عن الوطن
• حركت الضمير العالمي ضد جرائم داعش
• أصبحت رمزاً للمقاومة ضد الظلم والطغيان
• ألهمت الشعب العراقي للوقوف ضد الإرهاب
• تذكير دائم بأهمية الوحدة والتماسك الوطني
        """

        content_box = slide.shapes.add_textbox(
            Inches(1), Inches(2), Inches(14), Inches(4.5)
        )
        content_frame = content_box.text_frame
        content_frame.text = self.format_arabic_text(impact_content.strip())

        for p in content_frame.paragraphs:
            p.alignment = PP_ALIGN.RIGHT
            p.font.name = 'Amiri'
            p.font.size = Pt(18)
            p.font.color.rgb = self.colors['white']
            p.space_after = Pt(12)

        # English translation
        eng_impact = """Historical Impact and Significance of the Massacre:
• Turning point in contemporary Iraqi history
• Revealed the brutality of terrorist organizations
• Strengthened Iraqi national unity against terrorism
• Led to the formation of Popular Mobilization Forces to defend the homeland
• Moved the global conscience against ISIS crimes
• Became a symbol of resistance against injustice and tyranny
• Inspired the Iraqi people to stand against terrorism
• Permanent reminder of the importance of national unity and cohesion"""

        eng_box = slide.shapes.add_textbox(
            Inches(1), Inches(6.8), Inches(14), Inches(1.8)
        )
        eng_frame = eng_box.text_frame
        eng_frame.text = eng_impact

        for p in eng_frame.paragraphs:
            p.alignment = PP_ALIGN.LEFT
            p.font.name = 'Arial'
            p.font.size = Pt(14)
            p.font.color.rgb = self.colors['white']

        return slide

    def add_closing_slide(self):
        """Concluding memorial slide"""
        slide_layout = self.prs.slide_layouts[6]
        slide = self.prs.slides.add_slide(slide_layout)

        # Background
        background = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE, 0, 0,
            self.prs.slide_width, self.prs.slide_height
        )
        background.fill.solid()
        background.fill.fore_color.rgb = self.colors['black']
        background.line.fill.background()

        # Main memorial text
        memorial_text = "لن ننسى شهداء سبايكر الأبرار"
        memorial_box = slide.shapes.add_textbox(
            Inches(1), Inches(2), Inches(14), Inches(2)
        )
        memorial_frame = memorial_box.text_frame
        memorial_frame.text = self.format_arabic_text(memorial_text)

        p = memorial_frame.paragraphs[0]
        p.alignment = PP_ALIGN.CENTER
        p.font.name = 'Amiri'
        p.font.size = Pt(48)
        p.font.color.rgb = self.colors['gold']
        p.font.bold = True

        # Additional memorial phrases
        additional_text = """
خلدت أسماؤهم في سجل الخالدين
وبقيت ذكراهم منارة للأجيال
رحمهم الله وأسكنهم فسيح جناته
        """

        additional_box = slide.shapes.add_textbox(
            Inches(1), Inches(4.5), Inches(14), Inches(2)
        )
        additional_frame = additional_box.text_frame
        additional_frame.text = self.format_arabic_text(additional_text.strip())

        for p in additional_frame.paragraphs:
            p.alignment = PP_ALIGN.CENTER
            p.font.name = 'Amiri'
            p.font.size = Pt(24)
            p.font.color.rgb = self.colors['white']
            p.space_after = Pt(12)

        # English translation
        eng_memorial = """We Will Never Forget the Righteous Martyrs of Speicher

Their names are immortalized in the register of the eternal
Their memory remains a beacon for generations
May Allah have mercy on them and grant them spacious gardens"""

        eng_box = slide.shapes.add_textbox(
            Inches(1), Inches(7), Inches(14), Inches(1.5)
        )
        eng_frame = eng_box.text_frame
        eng_frame.text = eng_memorial

        for p in eng_frame.paragraphs:
            p.alignment = PP_ALIGN.CENTER
            p.font.name = 'Arial'
            p.font.size = Pt(18)
            p.font.color.rgb = self.colors['white']
            p.font.italic = True

        return slide

    def generate_presentation(self):
        """Generate the complete presentation"""
        print("Creating Speicher Massacre Memorial Presentation...")

        # Add all slides
        self.add_title_slide()
        print("✓ Title slide added")

        self.add_background_slide()
        print("✓ Historical background slide added")

        self.add_timeline_slide()
        print("✓ Timeline of events slide added")

        self.add_memorial_slide()
        print("✓ Memorial slide added")

        self.add_impact_slide()
        print("✓ Impact and significance slide added")

        self.add_closing_slide()
        print("✓ Closing memorial slide added")

        # Save the presentation
        filename = "Speicher_Massacre_Memorial_Presentation.pptx"
        self.prs.save(filename)
        print(f"\n✅ Presentation saved as: {filename}")
        print(f"📊 Total slides: {len(self.prs.slides)}")
        print("\n📋 Presentation Contents:")
        print("1. Title Slide - Speicher Massacre Memorial")
        print("2. Historical Background - Context of the Massacre")
        print("3. Timeline of Events - Chronological Sequence")
        print("4. Memorial Slide - Honoring the Victims")
        print("5. Impact and Significance - Historical Importance")
        print("6. Closing Memorial - Final Remembrance")

        return filename

def main():
    """Main function to create the presentation"""
    try:
        # Create presentation instance
        presentation = SpecherMassacrePresentation()

        # Generate the presentation
        filename = presentation.generate_presentation()

        print(f"\n🎉 Successfully created PowerPoint presentation: {filename}")
        print("\n📝 Features included:")
        print("• Professional Arabic typography with Amiri font")
        print("• Proper Arabic RTL text formatting")
        print("• Memorial mourning colors (black, dark navy, dark green)")
        print("• Gold highlighting for important text")
        print("• English translations for accessibility")
        print("• Historical accuracy and cultural sensitivity")
        print("• 16:9 widescreen format")
        print("• Ready for memorial gatherings and presentations")

        print(f"\n📍 File location: {os.path.abspath(filename)}")
        print("\n🕌 This presentation is ready for use in memorial services")
        print("and educational events commemorating the Speicher massacre.")

    except Exception as e:
        print(f"❌ Error creating presentation: {str(e)}")
        print("Please ensure all required packages are installed:")
        print("pip install python-pptx pillow python-bidi arabic-reshaper")

if __name__ == "__main__":
    main()
