# Professional Image Editor

A comprehensive, professional-grade image editing application built with Python, featuring advanced editing tools, Arabic text support, and modern UI design.

## 🌟 Features

### Core Editing Tools
- **Image Loading & Saving**: Support for JPEG, PNG, GIF, BMP, TIFF formats
- **Basic Operations**: Crop, resize, rotate, flip with precision controls
- **Color Adjustments**: Brightness, contrast, saturation, hue with real-time preview
- **Advanced Filters**: Vintage, sepia, black & white, dramatic, soft effects
- **Drawing Tools**: Brush, pencil, eraser, shapes (rectangle, circle, line, arrow)
- **Text Overlay**: Full Arabic RTL text support with quality fonts

### Advanced Features
- **Layer Management**: Multiple layers with blending modes and opacity control
- **Undo/Redo System**: Comprehensive history management (up to 50 steps)
- **Zoom & Pan**: Smooth navigation with fit-to-window and actual size options
- **Batch Processing**: Process multiple images with automated operations
- **Social Media Export**: Preset dimensions for Instagram, Facebook, Twitter, etc.
- **Photo Restoration**: Noise reduction and basic repair tools

### Arabic Language Support
- **RTL Text Rendering**: Proper right-to-left text direction
- **Quality Fonts**: <PERSON><PERSON>, Scheherazade, Noto Sans Arabic support
- **Bidirectional Text**: Mixed Arabic and English text handling
- **Cultural Sensitivity**: Appropriate for Arab and Iraqi audiences

### User Interface
- **Modern Design**: Clean, intuitive interface with professional layout
- **Dark/Light Themes**: Customizable appearance
- **Responsive Panels**: Resizable tool panels and preview area
- **Keyboard Shortcuts**: Efficient workflow with standard shortcuts
- **Status Information**: Real-time image info and operation feedback

## 📋 Requirements

### System Requirements
- Python 3.7 or higher
- Windows, macOS, or Linux
- Minimum 4GB RAM (8GB recommended)
- 100MB free disk space

### Python Dependencies
```
Pillow==10.0.0
opencv-python==********
numpy==1.24.3
arabic-reshaper==3.0.0
python-bidi==0.4.2
tkinter-tooltip==2.0.0
matplotlib==3.7.2
```

## 🚀 Installation

1. **Clone or download the project files**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
3. **Run the application:**
   ```bash
   python main.py
   ```

## 📖 Usage Guide

### Getting Started
1. **Launch the application** by running `python main.py`
2. **Open an image** using File → Open or drag and drop
3. **Select tools** from the left panel
4. **Adjust settings** using the right panel controls
5. **Save your work** using File → Save or Save As

### Basic Editing Workflow
1. **Load Image**: File → Open to select your image
2. **Basic Adjustments**: Use brightness, contrast, saturation sliders
3. **Apply Filters**: Choose from vintage, dramatic, soft effects
4. **Crop/Resize**: Image menu for size adjustments
5. **Add Text**: Select text tool and click on image
6. **Save Result**: File → Save or Export with options

### Layer Management
- **Add Layer**: Click "Add" in layers panel
- **Select Layer**: Click on layer name to activate
- **Adjust Opacity**: Use opacity slider for transparency
- **Delete Layer**: Select layer and click "Delete"
- **Duplicate Layer**: Create copies for non-destructive editing

### Drawing Tools
- **Brush**: Smooth painting with adjustable size and opacity
- **Pencil**: Textured drawing for artistic effects
- **Eraser**: Remove parts of the image
- **Shapes**: Rectangle, circle, line, arrow tools
- **Text**: Add Arabic or English text with font options

### Batch Processing
1. **Open Batch Dialog**: File → Batch Process
2. **Add Files**: Select multiple images or entire folders
3. **Choose Preset**: Web optimization, print, or social media
4. **Set Output**: Choose destination folder
5. **Process**: Start automated processing

### Arabic Text Features
- **Enable Arabic**: Check "Arabic text (RTL)" when adding text
- **Font Selection**: Automatic selection of appropriate Arabic fonts
- **Text Direction**: Proper right-to-left rendering
- **Mixed Text**: Support for Arabic and English in same text

## ⌨️ Keyboard Shortcuts

| Action | Shortcut |
|--------|----------|
| New File | Ctrl+N |
| Open File | Ctrl+O |
| Save | Ctrl+S |
| Save As | Ctrl+Shift+S |
| Undo | Ctrl+Z |
| Redo | Ctrl+Y |
| Copy | Ctrl+C |
| Paste | Ctrl+V |
| Zoom In | Ctrl++ |
| Zoom Out | Ctrl+- |
| Fit to Window | Ctrl+0 |
| Crop | Ctrl+R |
| Resize | Ctrl+T |
| Rotate Left | Ctrl+Left |
| Rotate Right | Ctrl+Right |
| Flip Horizontal | Ctrl+H |
| Flip Vertical | Ctrl+V |
| Quit | Ctrl+Q |

## 🎨 Supported Formats

### Input Formats
- JPEG/JPG (Joint Photographic Experts Group)
- PNG (Portable Network Graphics)
- GIF (Graphics Interchange Format)
- BMP (Bitmap)
- TIFF (Tagged Image File Format)
- WEBP (Web Picture format)

### Output Formats
- JPEG (with quality control)
- PNG (with transparency support)
- GIF (with animation support)
- BMP (uncompressed)
- TIFF (high quality)
- PDF (document format)

## 🔧 Configuration

### Settings File
The application creates a `settings.json` file to store:
- Theme preference (light/dark)
- Language settings
- Default quality settings
- UI layout preferences
- Recent files list

### Font Configuration
Arabic fonts are automatically detected from:
- Application fonts folder
- System font directories
- Fallback to default fonts if needed

## 🐛 Troubleshooting

### Common Issues

**Application won't start:**
- Check Python version (3.7+ required)
- Install missing dependencies: `pip install -r requirements.txt`
- Verify all files are present

**Arabic text not displaying correctly:**
- Ensure Arabic fonts are installed
- Check font files in the fonts/ directory
- Try different Arabic font options

**Images not loading:**
- Verify file format is supported
- Check file permissions
- Try smaller image sizes

**Performance issues:**
- Reduce image size for large files
- Close unnecessary applications
- Increase available RAM

### Error Messages
- **"Missing Dependencies"**: Install required Python packages
- **"Failed to load image"**: Check file format and corruption
- **"Memory error"**: Reduce image size or close other applications

## 🤝 Contributing

We welcome contributions! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License. See LICENSE file for details.

## 👨‍💻 Author

**AliToucan**
- Professional Image Editor v1.0.0
- Specialized in Arabic language support and cultural sensitivity

## 🙏 Acknowledgments

- **Pillow (PIL)**: Core image processing library
- **OpenCV**: Advanced computer vision operations
- **Arabic-Reshaper**: Arabic text processing
- **Python-BIDI**: Bidirectional text algorithm
- **tkinter**: GUI framework

## 📞 Support

For support, bug reports, or feature requests:
- Create an issue in the project repository
- Check the troubleshooting section above
- Review the documentation and examples

---

**Professional Image Editor** - Bringing professional-grade image editing with Arabic support to everyone.
