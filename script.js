// Arabic E-commerce Showcase JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

function initializeWebsite() {
    loadProducts();
    setupEventListeners();
    addMarketingAnimations();
}

function loadProducts() {
    const productsGrid = document.getElementById('productsGrid');
    
    products.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.setAttribute('data-product-id', product.id);
    
    card.innerHTML = `
        <div class="product-image" style="background: ${product.gradient}">
            <span style="font-size: 4rem;">${product.icon}</span>
        </div>
        <div class="product-content">
            <h3 class="product-title">${product.name}</h3>
            <p class="product-description">${product.description}</p>
            <div class="product-price">${product.price}</div>
            <div class="marketing-text" style="text-align: center; color: #27ae60; font-weight: 600; margin-bottom: 15px; font-size: 0.9rem;">
                ${product.marketingText}
            </div>
            <div class="product-actions">
                <button class="btn btn-primary" onclick="addToCart(${product.id})">
                    <i class="fas fa-shopping-cart"></i>
                    أضف للسلة
                </button>
                <button class="btn btn-secondary" onclick="downloadProductImage(${product.id})">
                    <i class="fas fa-download"></i>
                    تحميل الصورة
                </button>
            </div>
        </div>
    `;
    
    return card;
}

function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        // Simulate adding to cart
        showNotification(`تم إضافة "${product.name}" إلى السلة بنجاح! 🛒`, 'success');
        
        // Add cart animation
        animateAddToCart(productId);
    }
}

function downloadProductImage(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    showLoading(true);
    
    // Create a canvas for the product image
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size for high quality (Instagram format)
    canvas.width = 1080;
    canvas.height = 1080;
    
    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    
    // Parse gradient colors from product.gradient
    const gradientMatch = product.gradient.match(/linear-gradient\(45deg,\s*(#[a-fA-F0-9]{6}),\s*(#[a-fA-F0-9]{6})\)/);
    if (gradientMatch) {
        gradient.addColorStop(0, gradientMatch[1]);
        gradient.addColorStop(1, gradientMatch[2]);
    } else {
        gradient.addColorStop(0, '#3498db');
        gradient.addColorStop(1, '#2980b9');
    }
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add Islamic pattern overlay
    addIslamicPattern(ctx, canvas.width, canvas.height);
    
    // Add product icon
    ctx.font = '200px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fillText(product.icon, canvas.width / 2, canvas.height / 2 - 100);
    
    // Add product name
    ctx.font = 'bold 48px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.fillText(product.name, canvas.width / 2, canvas.height / 2 + 100);
    
    // Add price
    ctx.font = 'bold 36px Arial';
    ctx.fillStyle = '#f1c40f';
    ctx.fillText(product.price, canvas.width / 2, canvas.height / 2 + 160);
    
    // Add marketing text
    ctx.font = '28px Arial';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fillText(product.marketingText, canvas.width / 2, canvas.height / 2 + 220);
    
    // Add website branding
    ctx.font = '24px Arial';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText('متجر التراث العربي', canvas.width / 2, canvas.height - 50);
    
    // Convert canvas to blob and download
    canvas.toBlob(function(blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${product.name.replace(/\s+/g, '_')}_منتج.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showLoading(false);
        showNotification(`تم تحميل صورة "${product.name}" بنجاح! 📸`, 'success');
    }, 'image/png', 1.0);
}

function addIslamicPattern(ctx, width, height) {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 2;
    
    const patternSize = 40;
    
    for (let x = 0; x < width; x += patternSize) {
        for (let y = 0; y < height; y += patternSize) {
            ctx.beginPath();
            ctx.moveTo(x + patternSize/2, y);
            ctx.lineTo(x + patternSize, y + patternSize/2);
            ctx.lineTo(x + patternSize/2, y + patternSize);
            ctx.lineTo(x, y + patternSize/2);
            ctx.closePath();
            ctx.stroke();
        }
    }
}

function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#27ae60' : '#3498db'};
        color: white;
        padding: 15px 25px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        font-family: 'Noto Sans Arabic', sans-serif;
        font-size: 1rem;
        max-width: 300px;
        animation: slideInRight 0.3s ease;
        direction: rtl;
        text-align: right;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    loadingIndicator.style.display = show ? 'block' : 'none';
}

function animateAddToCart(productId) {
    const productCard = document.querySelector(`[data-product-id="${productId}"]`);
    if (productCard) {
        productCard.style.transform = 'scale(0.95)';
        productCard.style.transition = 'transform 0.2s ease';
        
        setTimeout(() => {
            productCard.style.transform = 'scale(1)';
        }, 200);
    }
}

function setupEventListeners() {
    // Add smooth scrolling for better UX
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            showLoading(false);
        }
    });
}

function addMarketingAnimations() {
    // Add floating animation to marketing banner
    const marketingBanner = document.querySelector('.marketing-banner');
    if (marketingBanner) {
        marketingBanner.style.animation = 'float 3s ease-in-out infinite';
    }
    
    // Add staggered animation to product cards
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }
`;
document.head.appendChild(style);
