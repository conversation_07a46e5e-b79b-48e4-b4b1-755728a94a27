#!/usr/bin/env python3
"""
Build script for creating standalone Professional Image Editor executable
Creates a distributable application using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        print("✓ PyInstaller is available")
        return True
    except ImportError:
        print("✗ PyInstaller not found")
        return False

def install_pyinstaller():
    """Install PyInstaller"""
    try:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("✓ PyInstaller installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("✗ Failed to install PyInstaller")
        return False

def create_spec_file():
    """Create PyInstaller spec file for advanced configuration"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Define the main script
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('fonts', 'fonts'),
        ('*.py', '.'),
        ('requirements.txt', '.'),
        ('README_ImageEditor.md', '.'),
    ],
    hiddenimports=[
        'PIL._tkinter_finder',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.colorchooser',
        'arabic_reshaper',
        'bidi.algorithm',
        'cv2',
        'numpy',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'PIL.ImageEnhance',
        'PIL.ImageFilter',
        'PIL.ImageOps',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib.tests',
        'numpy.tests',
        'PIL.tests',
        'cv2.tests',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Professional_Image_Editor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
'''
    
    with open('image_editor.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ Created PyInstaller spec file")

def create_app_icon():
    """Create a simple application icon"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create icon image
        size = 256
        icon = Image.new('RGBA', (size, size), (70, 130, 180, 255))  # Steel blue background
        draw = ImageDraw.Draw(icon)
        
        # Draw a simple camera/image icon
        # Outer rectangle (camera body)
        margin = 40
        draw.rectangle([margin, margin + 20, size - margin, size - margin], 
                      outline='white', width=8, fill=(50, 50, 50, 255))
        
        # Lens circle
        center = size // 2
        lens_radius = 60
        draw.ellipse([center - lens_radius, center - lens_radius + 10, 
                     center + lens_radius, center + lens_radius + 10], 
                    outline='white', width=6, fill=(30, 30, 30, 255))
        
        # Inner lens
        inner_radius = 35
        draw.ellipse([center - inner_radius, center - inner_radius + 10, 
                     center + inner_radius, center + inner_radius + 10], 
                    outline='lightblue', width=4)
        
        # Flash
        flash_size = 15
        draw.rectangle([size - margin - 30, margin + 30, 
                       size - margin - 30 + flash_size, margin + 30 + flash_size], 
                      fill='yellow')
        
        # Save as ICO for Windows
        icon.save('app_icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        
        # Save as PNG for other platforms
        icon.save('app_icon.png', format='PNG')
        
        print("✓ Created application icon")
        return True
        
    except Exception as e:
        print(f"⚠ Could not create icon: {e}")
        return False

def build_executable():
    """Build the standalone executable"""
    try:
        print("Building standalone executable...")
        print("This may take several minutes...")
        
        # Use the spec file for building
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'image_editor.spec']
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Build completed successfully!")
            return True
        else:
            print("✗ Build failed!")
            print("Error output:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Build error: {e}")
        return False

def create_installer_script():
    """Create a simple installer script"""
    installer_content = '''@echo off
echo Professional Image Editor Installer
echo =====================================
echo.

REM Check if the executable exists
if not exist "Professional_Image_Editor.exe" (
    echo Error: Professional_Image_Editor.exe not found!
    echo Please make sure you're running this from the correct directory.
    pause
    exit /b 1
)

REM Create installation directory
set INSTALL_DIR=%PROGRAMFILES%\\Professional Image Editor
echo Creating installation directory: %INSTALL_DIR%
mkdir "%INSTALL_DIR%" 2>nul

REM Copy files
echo Copying application files...
copy "Professional_Image_Editor.exe" "%INSTALL_DIR%\\" >nul
if exist "README_ImageEditor.md" copy "README_ImageEditor.md" "%INSTALL_DIR%\\" >nul
if exist "app_icon.ico" copy "app_icon.ico" "%INSTALL_DIR%\\" >nul

REM Create desktop shortcut
echo Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\\shortcut.vbs"
echo sLinkFile = "%DESKTOP%\\Professional Image Editor.lnk" >> "%TEMP%\\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\\shortcut.vbs"
echo oLink.TargetPath = "%INSTALL_DIR%\\Professional_Image_Editor.exe" >> "%TEMP%\\shortcut.vbs"
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> "%TEMP%\\shortcut.vbs"
echo oLink.Description = "Professional Image Editor with Arabic Support" >> "%TEMP%\\shortcut.vbs"
if exist "%INSTALL_DIR%\\app_icon.ico" echo oLink.IconLocation = "%INSTALL_DIR%\\app_icon.ico" >> "%TEMP%\\shortcut.vbs"
echo oLink.Save >> "%TEMP%\\shortcut.vbs"
cscript "%TEMP%\\shortcut.vbs" >nul
del "%TEMP%\\shortcut.vbs"

REM Create start menu entry
echo Creating start menu entry...
set STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs
mkdir "%STARTMENU%\\Professional Image Editor" 2>nul
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\\startmenu.vbs"
echo sLinkFile = "%STARTMENU%\\Professional Image Editor\\Professional Image Editor.lnk" >> "%TEMP%\\startmenu.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\\startmenu.vbs"
echo oLink.TargetPath = "%INSTALL_DIR%\\Professional_Image_Editor.exe" >> "%TEMP%\\startmenu.vbs"
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> "%TEMP%\\startmenu.vbs"
echo oLink.Description = "Professional Image Editor with Arabic Support" >> "%TEMP%\\startmenu.vbs"
if exist "%INSTALL_DIR%\\app_icon.ico" echo oLink.IconLocation = "%INSTALL_DIR%\\app_icon.ico" >> "%TEMP%\\startmenu.vbs"
echo oLink.Save >> "%TEMP%\\startmenu.vbs"
cscript "%TEMP%\\startmenu.vbs" >nul
del "%TEMP%\\startmenu.vbs"

echo.
echo Installation completed successfully!
echo.
echo The application has been installed to: %INSTALL_DIR%
echo Desktop shortcut created: %DESKTOP%\\Professional Image Editor.lnk
echo Start menu entry created in: Professional Image Editor
echo.
echo You can now run the application from the desktop shortcut or start menu.
echo.
pause
'''
    
    with open('install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✓ Created installer script (install.bat)")

def create_portable_launcher():
    """Create a portable launcher script"""
    launcher_content = '''@echo off
title Professional Image Editor - Portable
echo Professional Image Editor - Portable Version
echo ============================================
echo.

REM Check if executable exists
if not exist "Professional_Image_Editor.exe" (
    echo Error: Professional_Image_Editor.exe not found!
    echo Please make sure you're running this from the correct directory.
    pause
    exit /b 1
)

REM Set portable mode environment
set PORTABLE_MODE=1
set APP_DATA_DIR=%~dp0data

REM Create data directory for portable settings
if not exist "%APP_DATA_DIR%" mkdir "%APP_DATA_DIR%"

echo Starting Professional Image Editor...
echo Data directory: %APP_DATA_DIR%
echo.

REM Launch the application
start "" "Professional_Image_Editor.exe"

REM Optional: Wait for application to close
REM "Professional_Image_Editor.exe"

echo Application closed.
pause
'''
    
    with open('run_portable.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✓ Created portable launcher (run_portable.bat)")

def create_distribution_package():
    """Create distribution package with all necessary files"""
    try:
        # Create distribution directory
        dist_dir = Path('distribution')
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        dist_dir.mkdir()
        
        # Copy executable
        exe_path = Path('dist/Professional_Image_Editor.exe')
        if exe_path.exists():
            shutil.copy2(exe_path, dist_dir / 'Professional_Image_Editor.exe')
            print("✓ Copied executable")
        else:
            print("✗ Executable not found!")
            return False
        
        # Copy documentation
        docs = ['README_ImageEditor.md', 'requirements.txt']
        for doc in docs:
            if Path(doc).exists():
                shutil.copy2(doc, dist_dir / doc)
        
        # Copy icon
        if Path('app_icon.ico').exists():
            shutil.copy2('app_icon.ico', dist_dir / 'app_icon.ico')
        
        # Copy installer and launcher scripts
        if Path('install.bat').exists():
            shutil.copy2('install.bat', dist_dir / 'install.bat')
        if Path('run_portable.bat').exists():
            shutil.copy2('run_portable.bat', dist_dir / 'run_portable.bat')
        
        # Create README for distribution
        dist_readme = '''# Professional Image Editor - Standalone Distribution

## Installation Options

### Option 1: Install to System
1. Run `install.bat` as Administrator
2. Follow the installation prompts
3. Use desktop shortcut or start menu to launch

### Option 2: Portable Mode
1. Run `run_portable.bat` to start in portable mode
2. All settings will be saved in the `data` folder
3. You can move the entire folder to any location

### Option 3: Direct Launch
1. Double-click `Professional_Image_Editor.exe`
2. Application will start immediately

## System Requirements
- Windows 10 or later (64-bit recommended)
- Minimum 4GB RAM
- 200MB free disk space

## Features
- Professional image editing tools
- Arabic text support (RTL)
- Layer management
- Batch processing
- Social media export presets
- Multiple file format support

## Support
For help and documentation, see README_ImageEditor.md

---
Professional Image Editor v1.0.0
Developed by AliToucan
'''
        
        with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
            f.write(dist_readme)
        
        print(f"✓ Created distribution package in: {dist_dir.absolute()}")
        return True
        
    except Exception as e:
        print(f"✗ Failed to create distribution package: {e}")
        return False

def main():
    """Main build process"""
    print("Professional Image Editor - Standalone Build Tool")
    print("=" * 55)
    
    # Check and install PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("Please install PyInstaller manually: pip install pyinstaller")
            return False
    
    # Create necessary files
    create_app_icon()
    create_spec_file()
    create_installer_script()
    create_portable_launcher()
    
    # Build executable
    if not build_executable():
        return False
    
    # Create distribution package
    if not create_distribution_package():
        return False
    
    print("\n" + "=" * 55)
    print("🎉 Standalone application build completed successfully!")
    print("\nDistribution files created in 'distribution' folder:")
    print("- Professional_Image_Editor.exe (Main application)")
    print("- install.bat (System installer)")
    print("- run_portable.bat (Portable launcher)")
    print("- README.txt (Distribution guide)")
    print("- Documentation files")
    
    print("\nTo distribute:")
    print("1. Zip the 'distribution' folder")
    print("2. Share the zip file with users")
    print("3. Users can choose installation or portable mode")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\nPress Enter to exit...")
    except KeyboardInterrupt:
        print("\nBuild cancelled by user.")
    except Exception as e:
        print(f"\nBuild error: {e}")
        input("Press Enter to exit...")
