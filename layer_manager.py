"""
Layer management system for the Professional Image Editor
Handles multiple layers, blending modes, and layer operations
"""

from PIL import Image, ImageDraw
import numpy as np
from typing import List, Optional, Tuple, Dict, Any
from enum import Enum
import uuid

class BlendMode(Enum):
    """Available blending modes for layers"""
    NORMAL = "normal"
    MULTIPLY = "multiply"
    SCREEN = "screen"
    OVERLAY = "overlay"
    SOFT_LIGHT = "soft_light"
    HARD_LIGHT = "hard_light"
    COLOR_DODGE = "color_dodge"
    COLOR_BURN = "color_burn"
    DARKEN = "darken"
    LIGHTEN = "lighten"
    DIFFERENCE = "difference"
    EXCLUSION = "exclusion"

class Layer:
    """Individual layer class"""
    
    def __init__(self, name: str, image: Image.Image, layer_id: str = None):
        self.id = layer_id or str(uuid.uuid4())
        self.name = name
        self.image = image.copy()
        self.visible = True
        self.opacity = 1.0  # 0.0 to 1.0
        self.blend_mode = BlendMode.NORMAL
        self.position = (0, 0)  # x, y offset
        self.locked = False
        self.mask = None  # Optional layer mask
        
    def set_opacity(self, opacity: float):
        """Set layer opacity (0.0 to 1.0)"""
        self.opacity = max(0.0, min(1.0, opacity))
    
    def set_blend_mode(self, blend_mode: BlendMode):
        """Set layer blending mode"""
        self.blend_mode = blend_mode
    
    def set_position(self, x: int, y: int):
        """Set layer position offset"""
        self.position = (x, y)
    
    def resize(self, new_size: Tuple[int, int]):
        """Resize layer image"""
        self.image = self.image.resize(new_size, Image.Resampling.LANCZOS)
    
    def rotate(self, angle: float):
        """Rotate layer image"""
        self.image = self.image.rotate(angle, expand=True, fillcolor=(0, 0, 0, 0))
    
    def flip(self, direction: str):
        """Flip layer horizontally or vertically"""
        if direction.lower() == 'horizontal':
            self.image = self.image.transpose(Image.Transpose.FLIP_LEFT_RIGHT)
        elif direction.lower() == 'vertical':
            self.image = self.image.transpose(Image.Transpose.FLIP_TOP_BOTTOM)
    
    def add_mask(self, mask: Image.Image):
        """Add layer mask"""
        if mask.mode != 'L':
            mask = mask.convert('L')
        self.mask = mask.resize(self.image.size, Image.Resampling.LANCZOS)
    
    def remove_mask(self):
        """Remove layer mask"""
        self.mask = None
    
    def get_effective_image(self) -> Image.Image:
        """Get layer image with mask applied"""
        if self.mask is None:
            return self.image
        
        # Apply mask to image
        if self.image.mode != 'RGBA':
            image_with_alpha = self.image.convert('RGBA')
        else:
            image_with_alpha = self.image.copy()
        
        # Use mask as alpha channel
        r, g, b, a = image_with_alpha.split()
        masked_alpha = Image.composite(a, Image.new('L', a.size, 0), self.mask)
        
        return Image.merge('RGBA', (r, g, b, masked_alpha))
    
    def duplicate(self) -> 'Layer':
        """Create a duplicate of this layer"""
        new_layer = Layer(f"{self.name} copy", self.image)
        new_layer.visible = self.visible
        new_layer.opacity = self.opacity
        new_layer.blend_mode = self.blend_mode
        new_layer.position = self.position
        new_layer.locked = self.locked
        if self.mask:
            new_layer.mask = self.mask.copy()
        return new_layer

class LayerManager:
    """Manage multiple layers and compositing"""
    
    def __init__(self, canvas_size: Tuple[int, int]):
        self.canvas_size = canvas_size
        self.layers: List[Layer] = []
        self.active_layer_index = -1
        self.background_color = (255, 255, 255, 255)  # White background
    
    def add_layer(self, layer: Layer, index: Optional[int] = None) -> str:
        """Add layer to the stack"""
        if index is None:
            self.layers.append(layer)
            self.active_layer_index = len(self.layers) - 1
        else:
            self.layers.insert(index, layer)
            if index <= self.active_layer_index:
                self.active_layer_index += 1
        
        return layer.id
    
    def remove_layer(self, layer_id: str) -> bool:
        """Remove layer by ID"""
        for i, layer in enumerate(self.layers):
            if layer.id == layer_id:
                self.layers.pop(i)
                if i <= self.active_layer_index and self.active_layer_index > 0:
                    self.active_layer_index -= 1
                elif len(self.layers) == 0:
                    self.active_layer_index = -1
                return True
        return False
    
    def get_layer(self, layer_id: str) -> Optional[Layer]:
        """Get layer by ID"""
        for layer in self.layers:
            if layer.id == layer_id:
                return layer
        return None
    
    def get_active_layer(self) -> Optional[Layer]:
        """Get currently active layer"""
        if 0 <= self.active_layer_index < len(self.layers):
            return self.layers[self.active_layer_index]
        return None
    
    def set_active_layer(self, layer_id: str) -> bool:
        """Set active layer by ID"""
        for i, layer in enumerate(self.layers):
            if layer.id == layer_id:
                self.active_layer_index = i
                return True
        return False
    
    def move_layer(self, layer_id: str, new_index: int) -> bool:
        """Move layer to new position in stack"""
        layer = None
        old_index = -1
        
        # Find layer
        for i, l in enumerate(self.layers):
            if l.id == layer_id:
                layer = l
                old_index = i
                break
        
        if layer is None:
            return False
        
        # Remove from old position
        self.layers.pop(old_index)
        
        # Insert at new position
        new_index = max(0, min(new_index, len(self.layers)))
        self.layers.insert(new_index, layer)
        
        # Update active layer index
        if old_index == self.active_layer_index:
            self.active_layer_index = new_index
        elif old_index < self.active_layer_index <= new_index:
            self.active_layer_index -= 1
        elif new_index <= self.active_layer_index < old_index:
            self.active_layer_index += 1
        
        return True
    
    def duplicate_layer(self, layer_id: str) -> Optional[str]:
        """Duplicate layer"""
        layer = self.get_layer(layer_id)
        if layer is None:
            return None
        
        new_layer = layer.duplicate()
        
        # Insert after original layer
        for i, l in enumerate(self.layers):
            if l.id == layer_id:
                self.add_layer(new_layer, i + 1)
                return new_layer.id
        
        return None
    
    def merge_down(self, layer_id: str) -> bool:
        """Merge layer with the layer below it"""
        layer_index = -1
        for i, layer in enumerate(self.layers):
            if layer.id == layer_id:
                layer_index = i
                break
        
        if layer_index <= 0:  # Can't merge bottom layer or layer not found
            return False
        
        upper_layer = self.layers[layer_index]
        lower_layer = self.layers[layer_index - 1]
        
        # Composite the layers
        merged_image = self._composite_layers([lower_layer, upper_layer])
        
        # Replace lower layer with merged result
        lower_layer.image = merged_image
        
        # Remove upper layer
        self.layers.pop(layer_index)
        if layer_index <= self.active_layer_index:
            self.active_layer_index -= 1
        
        return True
    
    def flatten_image(self) -> Image.Image:
        """Flatten all layers into a single image"""
        return self._composite_layers(self.layers)
    
    def _composite_layers(self, layers: List[Layer]) -> Image.Image:
        """Composite multiple layers into a single image"""
        # Create base canvas
        result = Image.new('RGBA', self.canvas_size, self.background_color)
        
        for layer in layers:
            if not layer.visible or layer.opacity == 0:
                continue
            
            # Get layer image with mask applied
            layer_image = layer.get_effective_image()
            
            # Apply opacity
            if layer.opacity < 1.0:
                layer_image = self._apply_opacity(layer_image, layer.opacity)
            
            # Position the layer
            x, y = layer.position
            
            # Create a canvas-sized image for this layer
            layer_canvas = Image.new('RGBA', self.canvas_size, (0, 0, 0, 0))
            
            # Paste layer image at position
            if x < self.canvas_size[0] and y < self.canvas_size[1]:
                # Calculate paste area
                paste_x = max(0, x)
                paste_y = max(0, y)
                
                # Calculate crop area if layer extends beyond canvas
                crop_left = max(0, -x)
                crop_top = max(0, -y)
                crop_right = min(layer_image.width, self.canvas_size[0] - x)
                crop_bottom = min(layer_image.height, self.canvas_size[1] - y)
                
                if crop_right > crop_left and crop_bottom > crop_top:
                    cropped_layer = layer_image.crop((crop_left, crop_top, crop_right, crop_bottom))
                    layer_canvas.paste(cropped_layer, (paste_x, paste_y))
            
            # Apply blending mode
            result = self._apply_blend_mode(result, layer_canvas, layer.blend_mode)
        
        return result
    
    def _apply_opacity(self, image: Image.Image, opacity: float) -> Image.Image:
        """Apply opacity to image"""
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        # Multiply alpha channel by opacity
        r, g, b, a = image.split()
        a = a.point(lambda x: int(x * opacity))
        
        return Image.merge('RGBA', (r, g, b, a))
    
    def _apply_blend_mode(self, base: Image.Image, overlay: Image.Image, 
                         blend_mode: BlendMode) -> Image.Image:
        """Apply blending mode between two images"""
        if blend_mode == BlendMode.NORMAL:
            return Image.alpha_composite(base, overlay)
        
        # Convert to numpy arrays for advanced blending
        base_array = np.array(base, dtype=np.float32) / 255.0
        overlay_array = np.array(overlay, dtype=np.float32) / 255.0
        
        # Extract alpha channels
        base_alpha = base_array[:, :, 3:4]
        overlay_alpha = overlay_array[:, :, 3:4]
        
        # Extract RGB channels
        base_rgb = base_array[:, :, :3]
        overlay_rgb = overlay_array[:, :, :3]
        
        # Apply blending mode
        if blend_mode == BlendMode.MULTIPLY:
            result_rgb = base_rgb * overlay_rgb
        elif blend_mode == BlendMode.SCREEN:
            result_rgb = 1 - (1 - base_rgb) * (1 - overlay_rgb)
        elif blend_mode == BlendMode.OVERLAY:
            mask = base_rgb < 0.5
            result_rgb = np.where(mask, 2 * base_rgb * overlay_rgb,
                                 1 - 2 * (1 - base_rgb) * (1 - overlay_rgb))
        elif blend_mode == BlendMode.SOFT_LIGHT:
            result_rgb = (1 - 2 * overlay_rgb) * base_rgb**2 + 2 * overlay_rgb * base_rgb
        elif blend_mode == BlendMode.HARD_LIGHT:
            mask = overlay_rgb < 0.5
            result_rgb = np.where(mask, 2 * base_rgb * overlay_rgb,
                                 1 - 2 * (1 - base_rgb) * (1 - overlay_rgb))
        elif blend_mode == BlendMode.DARKEN:
            result_rgb = np.minimum(base_rgb, overlay_rgb)
        elif blend_mode == BlendMode.LIGHTEN:
            result_rgb = np.maximum(base_rgb, overlay_rgb)
        elif blend_mode == BlendMode.DIFFERENCE:
            result_rgb = np.abs(base_rgb - overlay_rgb)
        else:
            result_rgb = overlay_rgb  # Default to normal
        
        # Composite with alpha
        result_alpha = base_alpha + overlay_alpha * (1 - base_alpha)
        result_rgb = (base_rgb * base_alpha * (1 - overlay_alpha) + 
                     result_rgb * overlay_alpha) / np.maximum(result_alpha, 1e-6)
        
        # Combine RGB and alpha
        result = np.concatenate([result_rgb, result_alpha], axis=2)
        result = np.clip(result * 255, 0, 255).astype(np.uint8)
        
        return Image.fromarray(result)
    
    def get_layer_info(self) -> List[Dict[str, Any]]:
        """Get information about all layers"""
        info = []
        for i, layer in enumerate(self.layers):
            info.append({
                'id': layer.id,
                'name': layer.name,
                'index': i,
                'visible': layer.visible,
                'opacity': layer.opacity,
                'blend_mode': layer.blend_mode.value,
                'position': layer.position,
                'size': layer.image.size,
                'locked': layer.locked,
                'has_mask': layer.mask is not None,
                'is_active': i == self.active_layer_index
            })
        return info
