#!/usr/bin/env python3
"""
Create release package for Professional Image Editor
Creates a compressed distribution package ready for sharing
"""

import os
import sys
import zipfile
import shutil
from pathlib import Path
from datetime import datetime

def create_zip_package():
    """Create ZIP package for distribution"""
    try:
        # Get current date for version
        date_str = datetime.now().strftime("%Y%m%d")
        
        # Create package name
        package_name = f"Professional_Image_Editor_v1.0.0_{date_str}"
        zip_filename = f"{package_name}.zip"
        
        # Remove existing zip if it exists
        if Path(zip_filename).exists():
            os.remove(zip_filename)
        
        print(f"Creating release package: {zip_filename}")
        
        # Create ZIP file
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
            # Add all files from distribution folder
            dist_path = Path('distribution')
            if not dist_path.exists():
                print("Error: distribution folder not found!")
                print("Please run build_standalone.py first")
                return False
            
            for file_path in dist_path.rglob('*'):
                if file_path.is_file():
                    # Calculate relative path for ZIP
                    arcname = file_path.relative_to(dist_path.parent)
                    zipf.write(file_path, arcname)
                    print(f"  Added: {arcname}")
        
        # Get file size
        file_size = Path(zip_filename).stat().st_size
        size_mb = file_size / (1024 * 1024)
        
        print(f"\n✓ Release package created successfully!")
        print(f"  File: {zip_filename}")
        print(f"  Size: {size_mb:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"Error creating release package: {e}")
        return False

def test_executable():
    """Test the standalone executable"""
    try:
        exe_path = Path('distribution/Professional_Image_Editor.exe')
        if not exe_path.exists():
            print("Error: Executable not found!")
            return False
        
        print("Testing executable...")
        
        # Get file info
        file_size = exe_path.stat().st_size
        size_mb = file_size / (1024 * 1024)
        
        print(f"✓ Executable found")
        print(f"  Path: {exe_path.absolute()}")
        print(f"  Size: {size_mb:.1f} MB")
        
        # Try to run with --help or version flag (if implemented)
        # For now, just check if file exists and is executable
        if os.access(exe_path, os.X_OK):
            print("✓ Executable has proper permissions")
        else:
            print("⚠ Executable may not have proper permissions")
        
        return True
        
    except Exception as e:
        print(f"Error testing executable: {e}")
        return False

def create_installer_package():
    """Create installer package with NSIS (if available)"""
    try:
        # Check if NSIS is available (Windows only)
        if sys.platform != 'win32':
            print("Installer creation is only available on Windows")
            return False
        
        # Create NSIS script
        nsis_script = '''
; Professional Image Editor Installer Script
; Generated automatically

!define APP_NAME "Professional Image Editor"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "AliToucan"
!define APP_EXE "Professional_Image_Editor.exe"

; Include Modern UI
!include "MUI2.nsh"

; General
Name "${APP_NAME}"
OutFile "Professional_Image_Editor_Setup.exe"
InstallDir "$PROGRAMFILES\\${APP_NAME}"
InstallDirRegKey HKCU "Software\\${APP_NAME}" ""
RequestExecutionLevel admin

; Interface Settings
!define MUI_ABORTWARNING

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "README_ImageEditor.md"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "English"

; Installer Sections
Section "Main Application" SecMain
    SetOutPath "$INSTDIR"
    File "distribution\\${APP_EXE}"
    File "distribution\\README_ImageEditor.md"
    File "distribution\\app_icon.ico"
    
    ; Create shortcuts
    CreateDirectory "$SMPROGRAMS\\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXE}"
    CreateShortCut "$DESKTOP\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXE}"
    
    ; Write registry keys
    WriteRegStr HKCU "Software\\${APP_NAME}" "" $INSTDIR
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
SectionEnd

; Uninstaller Section
Section "Uninstall"
    Delete "$INSTDIR\\${APP_EXE}"
    Delete "$INSTDIR\\README_ImageEditor.md"
    Delete "$INSTDIR\\app_icon.ico"
    Delete "$INSTDIR\\Uninstall.exe"
    
    Delete "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk"
    Delete "$DESKTOP\\${APP_NAME}.lnk"
    RMDir "$SMPROGRAMS\\${APP_NAME}"
    RMDir "$INSTDIR"
    
    DeleteRegKey HKCU "Software\\${APP_NAME}"
SectionEnd
'''
        
        with open('installer.nsi', 'w', encoding='utf-8') as f:
            f.write(nsis_script)
        
        print("✓ Created NSIS installer script")
        print("  To build installer: makensis installer.nsi")
        
        return True
        
    except Exception as e:
        print(f"Error creating installer package: {e}")
        return False

def create_documentation():
    """Create comprehensive documentation for the release"""
    doc_content = f'''# Professional Image Editor v1.0.0 - Release Package

## 📦 Package Contents

This package contains the standalone version of Professional Image Editor with Arabic support.

### Files Included:
- `Professional_Image_Editor.exe` - Main application (standalone executable)
- `install.bat` - System installer script
- `run_portable.bat` - Portable mode launcher
- `README_ImageEditor.md` - Complete documentation
- `app_icon.ico` - Application icon
- `README.txt` - Quick start guide

## 🚀 Installation Options

### Option 1: System Installation (Recommended)
1. Right-click on `install.bat` and select "Run as administrator"
2. Follow the installation prompts
3. Use the desktop shortcut or start menu to launch the application

### Option 2: Portable Mode
1. Double-click `run_portable.bat` to start in portable mode
2. All settings will be saved in a local `data` folder
3. You can move the entire folder to any location (USB drive, etc.)

### Option 3: Direct Launch
1. Double-click `Professional_Image_Editor.exe` to start immediately
2. No installation required

## 💻 System Requirements

- **Operating System**: Windows 10 or later (64-bit recommended)
- **Memory**: Minimum 4GB RAM (8GB recommended for large images)
- **Storage**: 200MB free disk space
- **Display**: 1024x768 minimum resolution (1920x1080 recommended)

## ✨ Key Features

### Professional Editing Tools
- Load and save multiple image formats (JPEG, PNG, GIF, BMP, TIFF)
- Crop, resize, rotate, and flip with precision controls
- Brightness, contrast, saturation, and hue adjustments
- Professional filters: vintage, sepia, black & white, dramatic effects

### Arabic Language Support ⭐
- Full right-to-left (RTL) text rendering
- Support for Arabic fonts (Amiri, Scheherazade, Noto Sans Arabic)
- Bidirectional text handling for mixed Arabic/English content
- Cultural sensitivity for Arab and Iraqi audiences

### Advanced Features
- Multi-layer editing with blending modes
- Drawing tools: brush, pencil, shapes, text overlay
- Batch processing for multiple images
- Social media export presets (Instagram, Facebook, Twitter, TikTok)
- Undo/redo system (up to 50 steps)
- Dark and light themes

## 🎯 Quick Start Guide

1. **Launch the application** using any of the installation options above
2. **Open an image**: Click "Open" button or use File → Open
3. **Basic editing**: Use the sliders on the right panel for quick adjustments
4. **Add Arabic text**: Select the Text tool, click on image, check "Arabic text (RTL)"
5. **Apply filters**: Choose from the filter buttons on the right panel
6. **Save your work**: Use File → Save or File → Export for different formats

## 🔧 Troubleshooting

### Common Issues:

**Application won't start:**
- Ensure you're running Windows 10 or later
- Try running as administrator
- Check antivirus software (may need to whitelist the application)

**Arabic text not displaying correctly:**
- The application includes Arabic fonts automatically
- If issues persist, try different Arabic font options in the text dialog

**Performance issues with large images:**
- Close other applications to free up memory
- Consider resizing very large images before editing
- Use the "Fit to Window" option for better performance

**Antivirus warnings:**
- This is common with PyInstaller-built applications
- The application is safe - you can whitelist it in your antivirus
- Source code is available for verification

## 📞 Support and Updates

- **Documentation**: See README_ImageEditor.md for complete documentation
- **Version**: 1.0.0
- **Build Date**: {datetime.now().strftime("%Y-%m-%d")}
- **Developer**: AliToucan

## 📄 License

This software is provided as-is for educational and professional use.

---

**Professional Image Editor** - Bringing professional-grade image editing with Arabic support to everyone.

Enjoy creating and editing your images! 🎨
'''
    
    with open('distribution/RELEASE_NOTES.txt', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("✓ Created release documentation")

def main():
    """Main release creation process"""
    print("Professional Image Editor - Release Package Creator")
    print("=" * 55)
    
    # Test executable
    if not test_executable():
        print("Please build the application first using build_standalone.py")
        return False
    
    # Create documentation
    create_documentation()
    
    # Create installer script
    create_installer_package()
    
    # Create ZIP package
    if not create_zip_package():
        return False
    
    print("\n" + "=" * 55)
    print("🎉 Release package created successfully!")
    print("\nNext steps:")
    print("1. Test the executable in the distribution folder")
    print("2. Share the ZIP file with users")
    print("3. Users can extract and choose their preferred installation method")
    print("\nThe application is now ready for distribution! 📦")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\nPress Enter to exit...")
    except KeyboardInterrupt:
        print("\nRelease creation cancelled by user.")
    except Exception as e:
        print(f"\nRelease creation error: {e}")
        input("Press Enter to exit...")
