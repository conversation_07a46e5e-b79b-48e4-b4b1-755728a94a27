// Arabic Image Template Generator
class ArabicTemplateGenerator {
    constructor() {
        this.uploadedImage = null;
        this.canvas = null;
        this.ctx = null;
        this.currentFormat = 'square';
        this.currentPosition = 'center';
        
        this.init();
    }

    init() {
        this.setupCanvas();
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.updateFontSizeDisplay();
        this.updateOpacityDisplay();
    }

    setupCanvas() {
        this.canvas = document.getElementById('previewCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Set initial canvas size for square format
        this.setCanvasSize('square');
    }

    setCanvasSize(format) {
        if (format === 'square') {
            this.canvas.width = 1080;
            this.canvas.height = 1080;
        } else if (format === 'story') {
            this.canvas.width = 1080;
            this.canvas.height = 1920;
        }
        
        // Scale canvas display for preview
        const maxDisplaySize = 400;
        const scale = Math.min(maxDisplaySize / this.canvas.width, maxDisplaySize / this.canvas.height);
        this.canvas.style.width = (this.canvas.width * scale) + 'px';
        this.canvas.style.height = (this.canvas.height * scale) + 'px';
    }

    setupEventListeners() {
        // Image upload
        const imageInput = document.getElementById('imageInput');
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('click', () => imageInput.click());
        imageInput.addEventListener('change', (e) => this.handleImageUpload(e));

        // Text input
        const arabicText = document.getElementById('arabicText');
        arabicText.addEventListener('input', () => this.updatePreview());

        // Font controls
        const fontFamily = document.getElementById('fontFamily');
        fontFamily.addEventListener('change', () => this.updatePreview());

        const fontSize = document.getElementById('fontSize');
        fontSize.addEventListener('input', () => {
            this.updateFontSizeDisplay();
            this.updatePreview();
        });

        const textColor = document.getElementById('textColor');
        textColor.addEventListener('change', () => this.updatePreview());

        const backgroundColor = document.getElementById('backgroundColor');
        backgroundColor.addEventListener('change', () => this.updatePreview());

        const backgroundOpacity = document.getElementById('backgroundOpacity');
        backgroundOpacity.addEventListener('input', () => {
            this.updateOpacityDisplay();
            this.updatePreview();
        });

        // Position controls
        const positionBtns = document.querySelectorAll('.position-btn');
        positionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                positionBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentPosition = btn.dataset.position;
                this.updatePreview();
            });
        });

        // Format controls
        const formatBtns = document.querySelectorAll('.format-btn');
        formatBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                formatBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentFormat = btn.dataset.format;
                this.setCanvasSize(this.currentFormat);
                this.updatePreview();
            });
        });

        // Action buttons
        document.getElementById('generateBtn').addEventListener('click', () => this.generateTemplate());
        document.getElementById('downloadBtn').addEventListener('click', () => this.downloadImage());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetAll());
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                this.processImageFile(files[0]);
            }
        });
    }

    handleImageUpload(event) {
        const file = event.target.files[0];
        if (file && file.type.startsWith('image/')) {
            this.processImageFile(file);
        }
    }

    processImageFile(file) {
        // Check file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            this.showNotification('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت.', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.uploadedImage = img;
                this.showUploadedImage(e.target.result);
                this.enableControls();
                this.updatePreview();
                this.showNotification('تم رفع الصورة بنجاح! 📸', 'success');
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    showUploadedImage(src) {
        const uploadedImage = document.getElementById('uploadedImage');
        uploadedImage.src = src;
        uploadedImage.style.display = 'block';
    }

    enableControls() {
        document.getElementById('generateBtn').disabled = false;
    }

    updateFontSizeDisplay() {
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        fontSizeValue.textContent = fontSize.value + 'px';
    }

    updateOpacityDisplay() {
        const backgroundOpacity = document.getElementById('backgroundOpacity');
        const backgroundOpacityValue = document.getElementById('backgroundOpacityValue');
        backgroundOpacityValue.textContent = backgroundOpacity.value + '%';
    }

    updatePreview() {
        if (!this.uploadedImage) return;

        this.generateTemplate();
    }

    generateTemplate() {
        if (!this.uploadedImage) {
            this.showNotification('يرجى رفع صورة أولاً', 'error');
            return;
        }

        const text = document.getElementById('arabicText').value.trim();
        if (!text) {
            this.showNotification('يرجى إدخال النص العربي', 'error');
            return;
        }

        this.showLoading(true);

        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background image
        this.drawBackgroundImage();

        // Draw text overlay
        this.drawTextOverlay();

        // Show canvas and enable download
        document.getElementById('previewPlaceholder').style.display = 'none';
        this.canvas.style.display = 'block';
        document.getElementById('downloadBtn').disabled = false;

        this.showLoading(false);
        this.showNotification('تم إنشاء التصميم بنجاح! ✨', 'success');
    }

    drawBackgroundImage() {
        const img = this.uploadedImage;
        const canvasAspect = this.canvas.width / this.canvas.height;
        const imgAspect = img.width / img.height;

        let drawWidth, drawHeight, drawX, drawY;

        if (imgAspect > canvasAspect) {
            // Image is wider than canvas
            drawHeight = this.canvas.height;
            drawWidth = drawHeight * imgAspect;
            drawX = (this.canvas.width - drawWidth) / 2;
            drawY = 0;
        } else {
            // Image is taller than canvas
            drawWidth = this.canvas.width;
            drawHeight = drawWidth / imgAspect;
            drawX = 0;
            drawY = (this.canvas.height - drawHeight) / 2;
        }

        this.ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);
    }

    drawTextOverlay() {
        const text = document.getElementById('arabicText').value;
        const fontFamily = document.getElementById('fontFamily').value;
        const fontSize = parseInt(document.getElementById('fontSize').value);
        const textColor = document.getElementById('textColor').value;
        const backgroundColor = document.getElementById('backgroundColor').value;
        const backgroundOpacity = parseInt(document.getElementById('backgroundOpacity').value) / 100;

        // Set font
        this.ctx.font = `${fontSize}px "${fontFamily}", Arial, sans-serif`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // Calculate text position
        let textY;
        switch (this.currentPosition) {
            case 'top':
                textY = this.canvas.height * 0.2;
                break;
            case 'bottom':
                textY = this.canvas.height * 0.8;
                break;
            default: // center
                textY = this.canvas.height * 0.5;
        }

        const textX = this.canvas.width / 2;

        // Split text into lines if too long
        const lines = this.wrapText(text, this.canvas.width * 0.8);
        const lineHeight = fontSize * 1.2;
        const totalHeight = lines.length * lineHeight;
        const startY = textY - (totalHeight / 2) + (lineHeight / 2);

        // Draw background for text
        if (backgroundOpacity > 0) {
            const padding = 20;
            const bgHeight = totalHeight + (padding * 2);
            const bgY = startY - (lineHeight / 2) - padding;

            this.ctx.fillStyle = this.hexToRgba(backgroundColor, backgroundOpacity);
            this.ctx.fillRect(0, bgY, this.canvas.width, bgHeight);
        }

        // Draw text with shadow for better readability
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        this.ctx.shadowBlur = 4;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;

        this.ctx.fillStyle = textColor;
        lines.forEach((line, index) => {
            const y = startY + (index * lineHeight);
            this.ctx.fillText(line, textX, y);
        });

        // Reset shadow
        this.ctx.shadowColor = 'transparent';
        this.ctx.shadowBlur = 0;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;
    }

    wrapText(text, maxWidth) {
        const words = text.split(' ');
        const lines = [];
        let currentLine = '';

        for (let word of words) {
            const testLine = currentLine + (currentLine ? ' ' : '') + word;
            const metrics = this.ctx.measureText(testLine);
            
            if (metrics.width > maxWidth && currentLine) {
                lines.push(currentLine);
                currentLine = word;
            } else {
                currentLine = testLine;
            }
        }
        
        if (currentLine) {
            lines.push(currentLine);
        }

        return lines.length > 0 ? lines : [text];
    }

    hexToRgba(hex, alpha) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    downloadImage() {
        if (!this.canvas) return;

        const text = document.getElementById('arabicText').value.trim();
        const format = this.currentFormat === 'square' ? 'مربع' : 'ستوري';
        const filename = `تصميم_عربي_${format}_${Date.now()}.png`;

        this.canvas.toBlob((blob) => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showNotification('تم تحميل التصميم بنجاح! 🎉', 'success');
        }, 'image/png', 1.0);
    }

    resetAll() {
        // Reset form
        document.getElementById('arabicText').value = '';
        document.getElementById('imageInput').value = '';
        document.getElementById('uploadedImage').style.display = 'none';
        
        // Reset canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.canvas.style.display = 'none';
        document.getElementById('previewPlaceholder').style.display = 'block';
        
        // Reset controls
        document.getElementById('generateBtn').disabled = true;
        document.getElementById('downloadBtn').disabled = true;
        
        // Reset uploaded image
        this.uploadedImage = null;
        
        this.showNotification('تم إعادة تعيين جميع الإعدادات', 'info');
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db'
        };
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1rem;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
            direction: rtl;
            text-align: right;
        `;
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    showLoading(show) {
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = show ? 'block' : 'none';
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new ArabicTemplateGenerator();
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});
