#!/bin/bash

# Professional Image Editor - Build Script for Linux/macOS
echo "Professional Image Editor - Standalone Build"
echo "============================================="
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3.7+ and try again"
    exit 1
fi

echo "Python found:"
python3 --version

# Check if pip is available
if ! python3 -m pip --version &> /dev/null; then
    echo "Error: pip is not available"
    exit 1
fi

echo
echo "Installing/Updating required packages..."
python3 -m pip install --upgrade pip
python3 -m pip install pyinstaller
python3 -m pip install -r requirements.txt

echo
echo "Starting build process..."
python3 build_standalone.py

echo
echo "Build process completed!"
echo "Check the 'distribution' folder for the final application."
echo

read -p "Press Enter to continue..."
