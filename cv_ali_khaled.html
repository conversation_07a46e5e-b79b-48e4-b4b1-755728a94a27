<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السيرة الذاتية | علي خالد عويد خالد</title>
    <style>
        /* استيراد الخطوط العربية */
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;500;700&display=swap');
        
        :root {
            --primary-color: #16213e;
            --secondary-color: #0f3460;
            --accent-color: #4361ee;
            --text-color: #333;
            --light-color: #f5f5f5;
            --border-color: #ddd;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', 'Amiri', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f9f9f9;
            direction: rtl;
        }
        
        .cv-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin-bottom: 5px;
            font-weight: 700;
        }
        
        .header h2 {
            font-size: 1.3rem;
            font-weight: 400;
            opacity: 0.9;
        }
        
        .contact-info {
            background-color: var(--light-color);
            padding: 15px 30px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }
        
        .contact-item {
            margin: 5px 0;
            display: flex;
            align-items: center;
        }
        
        .contact-item i {
            margin-left: 10px;
            color: var(--accent-color);
        }
        
        .section {
            padding: 25px 30px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .section:last-child {
            border-bottom: none;
        }
        
        .section-title {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid var(--accent-color);
            display: inline-block;
        }
        
        .education-item, .skill-item {
            margin-bottom: 15px;
        }
        
        .education-title {
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--secondary-color);
        }
        
        .education-details {
            margin-top: 5px;
        }
        
        .skills-container {
            display: flex;
            flex-wrap: wrap;
        }
        
        .skill-category {
            flex: 1;
            min-width: 250px;
            margin-bottom: 20px;
        }
        
        .skill-name {
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .skill-bar {
            height: 10px;
            background-color: var(--light-color);
            border-radius: 5px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .skill-level {
            height: 100%;
            background-color: var(--accent-color);
            border-radius: 5px;
        }
        
        .languages-list {
            list-style-type: none;
        }
        
        .languages-list li {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
        }
        
        .language-level {
            color: var(--accent-color);
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .cv-container {
                margin: 10px;
                border-radius: 5px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.8rem;
            }
            
            .section {
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <div class="header">
            <h1>علي خالد عويد خالد</h1>
            <h2>طالب هندسة</h2>
        </div>
        
        <div class="contact-info">
            <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>بصرة/أبو الخصيب-محيلة، العراق</span>
            </div>
            <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>07730411335</span>
            </div>
            <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span><EMAIL></span>
            </div>
            <div class="contact-item">
                <i class="fas fa-calendar"></i>
                <span>14/11/2005</span>
            </div>
        </div>
        
        <div class="section">
            <h3 class="section-title">المؤهلات التعليمية</h3>
            <div class="education-item">
                <div class="education-title">شهادة الإعدادية</div>
                <div class="education-details">
                    <p>حاصل على شهادة الإعدادية</p>
                </div>
            </div>
            <div class="education-item">
                <div class="education-title">الدراسة الحالية</div>
                <div class="education-details">
                    <p>طالب في كلية الهندسة</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3 class="section-title">المهارات</h3>
            <div class="skills-container">
                <div class="skill-category">
                    <div class="skill-item">
                        <div class="skill-name">تصميم مواقع</div>
                        <div class="skill-bar">
                            <div class="skill-level" style="width: 85%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Microsoft Excel</div>
                        <div class="skill-bar">
                            <div class="skill-level" style="width: 80%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Microsoft Word</div>
                        <div class="skill-bar">
                            <div class="skill-level" style="width: 90%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3 class="section-title">اللغات</h3>
            <ul class="languages-list">
                <li>
                    <span class="language-name">العربية</span>
                    <span class="language-level">اللغة الأم</span>
                </li>
                <li>
                    <span class="language-name">الإنجليزية</span>
                    <span class="language-level">متوسط</span>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- Font Awesome للأيقونات -->
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
</body>
</html>