<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رحلة مهندس عراقي - إلهام وحكمة من ألمانيا</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gold-gradient: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
            --emerald-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --sunset-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --ocean-gradient: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-light: #718096;
            --white: #ffffff;
            --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-medium: 0 10px 25px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.2);
            --border-radius: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', 'Amiri', Arial, sans-serif;
            line-height: 1.8;
            color: var(--text-primary);
            background: var(--primary-gradient);
            direction: rtl;
            overflow-x: hidden;
        }

        /* Loading Animation */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .page-loader.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loader-content {
            text-align: center;
            color: white;
        }

        .loader-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Scroll Progress Bar */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: var(--sunset-gradient);
            z-index: 1001;
            transition: width 0.1s ease;
        }

        /* Islamic Geometric Patterns */
        .islamic-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.05;
            background-image:
                radial-gradient(circle at 25% 25%, #ffd700 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, #ffd700 2px, transparent 2px);
            background-size: 50px 50px;
            background-position: 0 0, 25px 25px;
            pointer-events: none;
        }

        .geometric-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            pointer-events: none;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Enhanced Header Styles */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-heavy);
            transform: translateY(0);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.2rem 0;
            transition: var(--transition);
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            background: var(--gold-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Amiri', serif;
            position: relative;
            transition: var(--transition);
        }

        .logo::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 3px;
            background: var(--gold-gradient);
            transition: width 0.3s ease;
            border-radius: 2px;
        }

        .logo:hover::after {
            width: 100%;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2.5rem;
            align-items: center;
        }

        .nav-links li {
            position: relative;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 1.1rem;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .nav-links a:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .nav-links a:hover::before {
            left: 0;
        }

        .nav-links a.active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-light);
        }

        /* Enhanced Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
            background: var(--primary-gradient);
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.2);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 900px;
            padding: 2rem;
        }

        .engineer-photo {
            width: 220px;
            height: 220px;
            border-radius: 50%;
            margin: 0 auto 3rem;
            background: var(--gold-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4.5rem;
            color: white;
            box-shadow: var(--shadow-heavy);
            position: relative;
            transition: var(--transition);
            animation: float 6s ease-in-out infinite;
        }

        .engineer-photo::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border-radius: 50%;
            background: var(--sunset-gradient);
            z-index: -1;
            animation: rotate 20s linear infinite;
        }

        .engineer-photo::after {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            border-radius: 50%;
            background: var(--ocean-gradient);
            z-index: -2;
            animation: rotate 30s linear infinite reverse;
            opacity: 0.7;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            font-family: 'Amiri', serif;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #ffffff, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5)); }
            to { filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
        }

        .hero p {
            font-size: 1.4rem;
            margin-bottom: 2rem;
            opacity: 0.95;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
            line-height: 1.8;
        }

        .hero-subtitle {
            font-size: 1.6rem;
            font-weight: 600;
            margin-bottom: 3rem;
            background: var(--sunset-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Scheherazade New', serif;
        }

        .cta-button {
            display: inline-block;
            padding: 18px 40px;
            background: var(--sunset-gradient);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.2rem;
            transition: var(--transition);
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .cta-button:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: var(--shadow-heavy);
        }

        .cta-button:hover::before {
            left: 100%;
        }

        /* Floating Elements */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            color: rgba(255, 255, 255, 0.1);
            font-size: 2rem;
            animation: floatRandom 15s ease-in-out infinite;
        }

        .floating-element:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 60%; left: 85%; animation-delay: 2s; }
        .floating-element:nth-child(3) { top: 80%; left: 15%; animation-delay: 4s; }
        .floating-element:nth-child(4) { top: 30%; left: 80%; animation-delay: 6s; }
        .floating-element:nth-child(5) { top: 70%; left: 70%; animation-delay: 8s; }

        @keyframes floatRandom {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-30px) rotate(90deg); }
            50% { transform: translateY(-60px) rotate(180deg); }
            75% { transform: translateY(-30px) rotate(270deg); }
        }

        /* Enhanced Section Styles */
        .section {
            padding: 100px 0;
            background: var(--white);
            position: relative;
            overflow: hidden;
        }

        .section:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--geometric-overlay);
            pointer-events: none;
            z-index: 0;
        }

        .section .container {
            position: relative;
            z-index: 1;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 4rem;
            color: var(--text-primary);
            font-family: 'Amiri', serif;
            position: relative;
            font-weight: 700;
        }

        .section-title::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 6px;
            background: var(--gold-gradient);
            border-radius: 3px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2.5rem;
            margin-top: 4rem;
        }

        .content-card {
            background: var(--white);
            padding: 2.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .content-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--gold-gradient);
            transform: scaleY(0);
            transition: transform 0.3s ease 0.1s;
        }

        .content-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .content-card:hover::before {
            transform: scaleX(1);
        }

        .content-card:hover::after {
            transform: scaleY(1);
        }

        .card-icon {
            font-size: 3rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            transition: var(--transition);
        }

        .content-card:hover .card-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .card-title {
            font-size: 1.6rem;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
            font-weight: 700;
            font-family: 'Amiri', serif;
        }

        .card-content {
            color: var(--text-secondary);
            line-height: 1.9;
            font-size: 1.1rem;
        }

        .card-content ul {
            margin-top: 1rem;
            padding-right: 1.5rem;
        }

        .card-content li {
            margin-bottom: 0.5rem;
            position: relative;
        }

        .card-content li::before {
            content: '✦';
            color: var(--primary-gradient);
            font-weight: bold;
            position: absolute;
            right: -1.2rem;
        }

        /* Enhanced Quote Section */
        .quote-section {
            background: var(--primary-gradient);
            color: white;
            text-align: center;
            padding: 120px 0;
            position: relative;
            overflow: hidden;
        }

        .quote-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--islamic-pattern);
            pointer-events: none;
        }

        .quote-section .container {
            position: relative;
            z-index: 2;
        }

        .quote {
            font-size: 2.5rem;
            font-style: italic;
            margin-bottom: 3rem;
            font-family: 'Amiri', serif;
            max-width: 900px;
            margin: 0 auto 3rem;
            line-height: 1.6;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .quote::before {
            content: '"';
            font-size: 6rem;
            position: absolute;
            top: -2rem;
            right: -1rem;
            color: rgba(255, 255, 255, 0.3);
            font-family: serif;
        }

        .quote::after {
            content: '"';
            font-size: 6rem;
            position: absolute;
            bottom: -4rem;
            left: -1rem;
            color: rgba(255, 255, 255, 0.3);
            font-family: serif;
        }

        .quote-author {
            font-size: 1.4rem;
            opacity: 0.9;
            font-weight: 600;
            position: relative;
        }

        .quote-author::before {
            content: '';
            position: absolute;
            top: 50%;
            right: -50px;
            width: 30px;
            height: 2px;
            background: rgba(255, 255, 255, 0.7);
        }

        .quote-author::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -50px;
            width: 30px;
            height: 2px;
            background: rgba(255, 255, 255, 0.7);
        }

        /* Enhanced Timeline Styles */
        .timeline {
            position: relative;
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem 0;
        }

        .timeline::after {
            content: '';
            position: absolute;
            width: 6px;
            background: var(--primary-gradient);
            top: 0;
            bottom: 0;
            right: 50%;
            margin-right: -3px;
            border-radius: 3px;
            box-shadow: var(--shadow-light);
        }

        .timeline-item {
            padding: 15px 50px;
            position: relative;
            background: inherit;
            width: 50%;
            animation: slideInFromRight 0.6s ease-out;
        }

        .timeline-item:nth-child(even) {
            left: 50%;
            animation: slideInFromLeft 0.6s ease-out;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            right: -12.5px;
            background: var(--gold-gradient);
            border: 5px solid white;
            top: 20px;
            border-radius: 50%;
            z-index: 2;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
        }

        .timeline-item:hover::after {
            transform: scale(1.2);
            box-shadow: var(--shadow-heavy);
        }

        .timeline-content {
            padding: 2.5rem;
            background: var(--white);
            position: relative;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .timeline-content h3 {
            color: var(--text-primary);
            font-size: 1.4rem;
            margin-bottom: 1rem;
            font-family: 'Amiri', serif;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .timeline-content p {
            color: var(--text-secondary);
            line-height: 1.8;
            font-size: 1.1rem;
        }

        @keyframes slideInFromRight {
            0% {
                opacity: 0;
                transform: translateX(50px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInFromLeft {
            0% {
                opacity: 0;
                transform: translateX(-50px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Enhanced Footer */
        footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: white;
            text-align: center;
            padding: 60px 0 40px;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--geometric-overlay);
            opacity: 0.1;
            pointer-events: none;
        }

        footer .container {
            position: relative;
            z-index: 1;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            color: white;
            text-decoration: none;
            border-radius: 50%;
            font-size: 1.4rem;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .social-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gold-gradient);
            transform: scale(0);
            transition: transform 0.3s ease;
            border-radius: 50%;
        }

        .social-links a:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: var(--shadow-heavy);
        }

        .social-links a:hover::before {
            transform: scale(1);
        }

        .social-links a i {
            position: relative;
            z-index: 2;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
            }

            .section-title {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.2rem;
            }

            .hero-subtitle {
                font-size: 1.3rem;
            }

            .engineer-photo {
                width: 180px;
                height: 180px;
                font-size: 3.5rem;
            }

            .section {
                padding: 60px 0;
            }

            .section-title {
                font-size: 2.2rem;
                margin-bottom: 3rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .content-card {
                padding: 2rem;
            }

            .timeline::after {
                right: 31px;
            }

            .timeline-item {
                width: 100%;
                padding-right: 70px;
                padding-left: 25px;
            }

            .timeline-item::after {
                right: 22px;
            }

            .timeline-item:nth-child(even) {
                left: 0%;
            }

            .quote {
                font-size: 1.8rem;
            }

            .quote::before,
            .quote::after {
                font-size: 4rem;
            }

            .social-links {
                gap: 1rem;
            }

            .social-links a {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .quote {
                font-size: 1.4rem;
            }

            .cta-button {
                padding: 15px 30px;
                font-size: 1rem;
            }
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Enhanced Animation Classes */
        .fade-in {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .fade-in-left {
            opacity: 0;
            transform: translateX(-50px);
            transition: opacity 0.8s ease, transform 0.8s ease;
        }

        .fade-in-left.visible {
            opacity: 1;
            transform: translateX(0);
        }

        .fade-in-right {
            opacity: 0;
            transform: translateX(50px);
            transition: opacity 0.8s ease, transform 0.8s ease;
        }

        .fade-in-right.visible {
            opacity: 1;
            transform: translateX(0);
        }

        .scale-in {
            opacity: 0;
            transform: scale(0.8);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .scale-in.visible {
            opacity: 1;
            transform: scale(1);
        }

        /* Pulse Animation for Important Elements */
        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Bounce Animation */
        .bounce {
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* Gradient Text Animation */
        .gradient-text {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* Particle Effect */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            animation: particleFloat 15s linear infinite;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Page Loader -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <h3>جاري التحميل...</h3>
            <p>مرحباً بك في رحلة الإلهام</p>
        </div>
    </div>

    <!-- Scroll Progress Bar -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- Header -->
    <header id="header">
        <nav class="container">
            <div class="logo gradient-text">مهندس عراقي في ألمانيا</div>
            <ul class="nav-links">
                <li><a href="#home" class="nav-link">الرئيسية</a></li>
                <li><a href="#journey" class="nav-link">الرحلة</a></li>
                <li><a href="#advice" class="nav-link">النصائح</a></li>
                <li><a href="#culture" class="nav-link">الثقافة</a></li>
                <li><a href="#contact" class="nav-link">التواصل</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="geometric-overlay"></div>
        <div class="floating-elements">
            <div class="floating-element"><i class="fas fa-cog"></i></div>
            <div class="floating-element"><i class="fas fa-lightbulb"></i></div>
            <div class="floating-element"><i class="fas fa-rocket"></i></div>
            <div class="floating-element"><i class="fas fa-star"></i></div>
            <div class="floating-element"><i class="fas fa-heart"></i></div>
        </div>
        <div class="particles" id="particles"></div>
        <div class="hero-content">
            <div class="engineer-photo pulse">
                <i class="fas fa-user-tie"></i>
            </div>
            <h1 class="fade-in-right">أحمد محمد الخالدي</h1>
            <p class="fade-in-left">مهندس ميكانيكي عراقي - عشر سنوات من الخبرة في ألمانيا</p>
            <p class="hero-subtitle fade-in-right">"من بغداد إلى برلين: رحلة إلهام وتحدي"</p>
            <a href="#journey" class="cta-button bounce">اكتشف رحلتي</a>
        </div>
    </section>

    <!-- Journey Section -->
    <section id="journey" class="section">
        <div class="container">
            <h2 class="section-title">رحلتي من العراق إلى ألمانيا</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h3>2014 - البداية في بغداد</h3>
                        <p>تخرجت من كلية الهندسة الميكانيكية في جامعة بغداد بتقدير امتياز. كان حلمي دائماً أن أطور مهاراتي في بيئة تقنية متقدمة، وكانت ألمانيا وجهتي المثالية بسبب تقدمها الصناعي والتقني.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h3>2015 - الوصول إلى ألمانيا</h3>
                        <p>وصلت إلى برلين بحلم كبير وحقيبة صغيرة. التحدي الأول كان تعلم اللغة الألمانية والتكيف مع الثقافة الجديدة. بدأت بدورة مكثفة في اللغة الألمانية وعملت في وظائف بسيطة لتغطية نفقات المعيشة.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h3>2016-2018 - التعليم والتطوير</h3>
                        <p>التحقت ببرنامج الماجستير في الهندسة الميكانيكية في جامعة برلين التقنية. كانت فترة صعبة لكنها مثمرة، حيث تعلمت أحدث التقنيات في مجال الهندسة وطورت مهاراتي في البحث والتطوير.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h3>2019-2024 - النجاح المهني</h3>
                        <p>حصلت على وظيفة في شركة BMW كمهندس تطوير. خلال هذه السنوات، عملت على مشاريع مبتكرة في مجال السيارات الكهربائية والتقنيات المستدامة. أصبحت قائد فريق وحصلت على عدة براءات اختراع.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quote Section -->
    <section class="quote-section">
        <div class="container">
            <div class="quote">
                "النجاح ليس وجهة، بل رحلة مستمرة من التعلم والتطوير. كل تحدٍ واجهته في ألمانيا جعلني أقوى وأكثر حكمة."
            </div>
            <div class="quote-author">- أحمد محمد الخالدي</div>
        </div>
    </section>

    <!-- Life Lessons Section -->
    <section id="advice" class="section">
        <div class="container">
            <h2 class="section-title">دروس الحياة والحكمة المكتسبة</h2>
            <div class="content-grid">
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="card-title">التعلم المستمر</h3>
                    <div class="card-content">
                        <p>في ألمانيا تعلمت أن التعلم لا يتوقف عند التخرج. الاستثمار في تطوير المهارات والمعرفة هو مفتاح النجاح في أي مجال. احرص على تعلم شيء جديد كل يوم، سواء كان تقنياً أو ثقافياً.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="card-title">الصبر والمثابرة</h3>
                    <div class="card-content">
                        <p>الطريق إلى النجاح ليس سهلاً، خاصة في بلد جديد. واجهت صعوبات كثيرة في البداية، لكن الصبر والمثابرة كانا سلاحي الأقوى. لا تستسلم أمام التحديات، فهي جزء من رحلة النمو.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="card-title">بناء العلاقات</h3>
                    <div class="card-content">
                        <p>العلاقات الإنسانية هي أساس النجاح في أي مكان. تعلمت أهمية بناء شبكة علاقات قوية مع الزملاء والأصدقاء. كن منفتحاً ومتعاوناً، وستجد أن الناس مستعدون لمساعدتك.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3 class="card-title">التوازن في الحياة</h3>
                    <div class="card-content">
                        <p>تعلمت في ألمانيا أهمية التوازن بين العمل والحياة الشخصية. النجاح المهني مهم، لكن الصحة النفسية والجسدية والعلاقات الأسرية لا تقل أهمية. خصص وقتاً لنفسك ولأحبائك.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="card-title">الانفتاح الثقافي</h3>
                    <div class="card-content">
                        <p>العيش في ثقافة مختلفة علمني قيمة الانفتاح والتسامح. كل ثقافة لها ما تقدمه من حكمة وخبرة. كن متقبلاً للاختلاف ومستعداً لتعلم وجهات نظر جديدة.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="card-title">الثقة بالنفس</h3>
                    <div class="card-content">
                        <p>أهم درس تعلمته هو الثقة بالنفس والقدرات. نحن كعرب وعراقيين لدينا إمكانيات هائلة وعقول مبدعة. لا تدع أحداً يقلل من شأنك أو يحد من طموحاتك. آمن بنفسك وبقدرتك على تحقيق أحلامك.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Career Advice Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">نصائح مهنية للمهندسين الشباب</h2>
            <div class="content-grid">
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="card-title">التخصص والتميز</h3>
                    <div class="card-content">
                        <p>اختر مجالاً تحبه وتميز فيه. في ألمانيا، يقدرون الخبرة العميقة أكثر من المعرفة السطحية. ركز على تطوير مهاراتك في مجال محدد وكن الأفضل فيه.</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>احرص على الحصول على شهادات مهنية معترف بها</li>
                            <li>شارك في المؤتمرات والورش التدريبية</li>
                            <li>اقرأ أحدث الأبحاث في مجالك</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3 class="card-title">إتقان اللغة</h3>
                    <div class="card-content">
                        <p>اللغة الألمانية مفتاح النجاح في ألمانيا. لا تكتفِ بالمستوى الأساسي، بل اسعَ لإتقان اللغة التقنية في مجالك.</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>احصل على شهادة C1 على الأقل</li>
                            <li>تعلم المصطلحات التقنية في مجالك</li>
                            <li>مارس اللغة يومياً مع الزملاء</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <h3 class="card-title">بناء الشبكة المهنية</h3>
                    <div class="card-content">
                        <p>الشبكة المهنية في ألمانيا مهمة جداً. انضم للجمعيات المهنية وشارك في الفعاليات.</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>انضم لـ VDI (جمعية المهندسين الألمان)</li>
                            <li>استخدم LinkedIn بفعالية</li>
                            <li>احضر معارض التوظيف والفعاليات المهنية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cultural Insights Section -->
    <section id="culture" class="section">
        <div class="container">
            <h2 class="section-title">نظرة على الثقافة الألمانية في العمل</h2>
            <div class="content-grid">
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="card-title">الدقة والانضباط</h3>
                    <div class="card-content">
                        <p>الألمان يقدرون الدقة في المواعيد والالتزام بالخطط. كن دائماً في الوقت المحدد، وإذا كان لديك موعد في الساعة 9:00، كن هناك في 8:55.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="card-title">التواصل المباشر</h3>
                    <div class="card-content">
                        <p>الألمان يفضلون التواصل المباشر والصريح. لا تخف من التعبير عن رأيك بوضوح، لكن بطريقة محترمة ومهنية.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3 class="card-title">التوازن بين العمل والحياة</h3>
                    <div class="card-content">
                        <p>في ألمانيا، يحترمون الحدود بين العمل والحياة الشخصية. لا تتوقع رداً على الإيميلات بعد ساعات العمل أو في عطلة نهاية الأسبوع.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-recycle"></i>
                    </div>
                    <h3 class="card-title">الاستدامة والبيئة</h3>
                    <div class="card-content">
                        <p>الألمان يهتمون كثيراً بالبيئة والاستدامة. تعلم عن أنظمة إعادة التدوير واحترم القوانين البيئية.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Practical Tips Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">نصائح عملية للدراسة والعمل في ألمانيا</h2>
            <div class="content-grid">
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="card-title">اختيار الجامعة</h3>
                    <div class="card-content">
                        <p>ابحث جيداً عن الجامعات والبرامج المناسبة لك:</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>جامعة برلين التقنية (TU Berlin)</li>
                            <li>جامعة ميونخ التقنية (TUM)</li>
                            <li>جامعة آخن (RWTH Aachen)</li>
                            <li>جامعة شتوتغارت</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-euro-sign"></i>
                    </div>
                    <h3 class="card-title">التمويل والمنح</h3>
                    <div class="card-content">
                        <p>هناك عدة خيارات للتمويل:</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>منح DAAD</li>
                            <li>منح الحكومة الألمانية</li>
                            <li>العمل الجزئي (450 يورو شهرياً)</li>
                            <li>المساعدة البحثية في الجامعة</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="card-title">السكن والمعيشة</h3>
                    <div class="card-content">
                        <p>نصائح للسكن في ألمانيا:</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>ابدأ البحث عن السكن مبكراً</li>
                            <li>فكر في السكن الطلابي (Studentenwohnheim)</li>
                            <li>تعلم عن نظام التأمين الصحي</li>
                            <li>افتح حساباً بنكياً ألمانياً</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h3 class="card-title">البحث عن عمل</h3>
                    <div class="card-content">
                        <p>استراتيجيات فعالة للحصول على وظيفة:</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>اكتب سيرة ذاتية ألمانية احترافية</li>
                            <li>استخدم مواقع مثل Xing و StepStone</li>
                            <li>قدم على التدريب العملي (Praktikum)</li>
                            <li>احضر معارض التوظيف</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final Inspirational Quote -->
    <section class="quote-section">
        <div class="container">
            <div class="quote">
                "إلى كل شاب عراقي يحلم بمستقبل أفضل: الطريق صعب لكنه ممكن. ثق بنفسك، اعمل بجد، ولا تنسَ جذورك. أنت قادر على تحقيق المعجزات."
            </div>
            <div class="quote-author">- أحمد محمد الخالدي</div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section">
        <div class="container">
            <h2 class="section-title">تواصل معي</h2>
            <div style="text-align: center; max-width: 600px; margin: 0 auto;">
                <p style="font-size: 1.2rem; margin-bottom: 2rem; color: #4a5568;">
                    أسعد دائماً بالتواصل مع الشباب العراقي والعربي الطموح. لا تتردد في التواصل معي للاستشارة أو المساعدة في رحلتك المهنية.
                </p>
                <div class="content-grid" style="margin-top: 3rem;">
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h3 class="card-title">البريد الإلكتروني</h3>
                        <div class="card-content">
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fab fa-linkedin"></i>
                        </div>
                        <h3 class="card-title">لينكد إن</h3>
                        <div class="card-content">
                            <p>linkedin.com/in/ahmed-alkhalidi</p>
                        </div>
                    </div>
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h3 class="card-title">واتساب</h3>
                        <div class="card-content">
                            <p>+49 176 1234 5678</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="social-links">
                <a href="#" title="فيسبوك"><i class="fab fa-facebook-f"></i></a>
                <a href="#" title="تويتر"><i class="fab fa-twitter"></i></a>
                <a href="#" title="لينكد إن"><i class="fab fa-linkedin-in"></i></a>
                <a href="#" title="إنستغرام"><i class="fab fa-instagram"></i></a>
                <a href="#" title="يوتيوب"><i class="fab fa-youtube"></i></a>
            </div>
            <p style="margin-bottom: 1rem; font-size: 1.1rem;">
                "كن التغيير الذي تريد أن تراه في العالم"
            </p>
            <p style="opacity: 0.8;">
                © 2024 أحمد محمد الخالدي - مهندس ميكانيكي عراقي في ألمانيا
            </p>
            <p style="opacity: 0.6; margin-top: 1rem; font-size: 0.9rem;">
                تم تصميم هذا الموقع لإلهام الشباب العربي والعراقي لتحقيق أحلامهم
            </p>
        </div>
    </footer>

    <!-- Enhanced JavaScript for Animations and Interactions -->
    <script>
        // Page Loader
        window.addEventListener('load', () => {
            const loader = document.getElementById('pageLoader');
            setTimeout(() => {
                loader.classList.add('hidden');
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 500);
            }, 1500);
        });

        // Scroll Progress Bar
        window.addEventListener('scroll', () => {
            const scrollProgress = document.getElementById('scrollProgress');
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            scrollProgress.style.width = scrollPercent + '%';
        });

        // Particle System
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Enhanced smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    // Update active nav link
                    document.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('active');
                    });
                    this.classList.add('active');

                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced animation observer
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');

                    // Add staggered animation for cards
                    if (entry.target.classList.contains('content-card')) {
                        const cards = entry.target.parentElement.querySelectorAll('.content-card');
                        cards.forEach((card, index) => {
                            setTimeout(() => {
                                card.classList.add('visible');
                            }, index * 200);
                        });
                    }
                }
            });
        }, observerOptions);

        // Observe all animation elements
        document.querySelectorAll('.fade-in, .fade-in-left, .fade-in-right, .scale-in').forEach(el => {
            observer.observe(el);
        });

        // Add mobile menu toggle functionality
        const createMobileMenu = () => {
            const nav = document.querySelector('nav');
            const navLinks = document.querySelector('.nav-links');

            // Create mobile menu button
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            mobileMenuBtn.style.cssText = `
                display: none;
                background: none;
                border: none;
                font-size: 1.5rem;
                color: #4a5568;
                cursor: pointer;
            `;

            // Add mobile menu functionality
            mobileMenuBtn.addEventListener('click', () => {
                navLinks.classList.toggle('mobile-active');
            });

            nav.appendChild(mobileMenuBtn);

            // Show mobile menu button on small screens
            const checkScreenSize = () => {
                if (window.innerWidth <= 768) {
                    mobileMenuBtn.style.display = 'block';
                    navLinks.style.cssText = `
                        position: fixed;
                        top: 70px;
                        right: -100%;
                        width: 100%;
                        height: calc(100vh - 70px);
                        background: white;
                        flex-direction: column;
                        justify-content: flex-start;
                        align-items: center;
                        padding-top: 2rem;
                        transition: right 0.3s ease;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    `;
                } else {
                    mobileMenuBtn.style.display = 'none';
                    navLinks.style.cssText = '';
                    navLinks.classList.remove('mobile-active');
                }
            };

            // Add CSS for mobile menu active state
            const style = document.createElement('style');
            style.textContent = `
                .nav-links.mobile-active {
                    right: 0 !important;
                }
                .nav-links.mobile-active a {
                    padding: 1rem 0;
                    border-bottom: 1px solid #eee;
                    width: 80%;
                    text-align: center;
                }
            `;
            document.head.appendChild(style);

            window.addEventListener('resize', checkScreenSize);
            checkScreenSize();
        };

        // Initialize mobile menu
        createMobileMenu();

        // Enhanced scroll effects
        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            const scrolled = window.pageYOffset;

            // Header scroll effect
            if (scrolled > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            // Parallax effect for hero section
            const hero = document.querySelector('.hero');
            const rate = scrolled * -0.3;
            hero.style.transform = `translateY(${rate}px)`;

            // Update active navigation based on scroll position
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrolled >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Enhanced typing effect for hero title
        const heroTitle = document.querySelector('.hero h1');
        const originalText = heroTitle.textContent;
        heroTitle.textContent = '';

        let i = 0;
        const typeWriter = () => {
            if (i < originalText.length) {
                heroTitle.textContent += originalText.charAt(i);
                i++;
                setTimeout(typeWriter, 80);
            } else {
                // Add cursor blink effect
                heroTitle.style.borderLeft = '3px solid #ffd700';
                heroTitle.style.animation = 'blink 1s infinite';
            }
        };

        // Enhanced card interactions
        document.querySelectorAll('.content-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
                this.style.boxShadow = 'var(--shadow-heavy)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });

            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Animated counters (if you want to add statistics)
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);

            const timer = setInterval(() => {
                start += increment;
                element.textContent = Math.floor(start);

                if (start >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                }
            }, 16);
        }

        // Initialize all features
        function initializeWebsite() {
            // Create particles
            createParticles();

            // Start typing effect after loader
            setTimeout(() => {
                typeWriter();
            }, 2000);

            // Add CSS for cursor blink
            const style = document.createElement('style');
            style.textContent = `
                @keyframes blink {
                    0%, 50% { border-color: transparent; }
                    51%, 100% { border-color: #ffd700; }
                }
            `;
            document.head.appendChild(style);

            // Add smooth reveal for timeline items
            const timelineItems = document.querySelectorAll('.timeline-item');
            timelineItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.3}s`;
            });

            // Add hover effects to social links
            document.querySelectorAll('.social-links a').forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.1) rotate(5deg)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });
            });
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', initializeWebsite);

        // Performance optimization: Throttle scroll events
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }

        // Apply throttling to scroll events
        window.addEventListener('scroll', throttle(() => {
            // Scroll-based animations here
        }, 16));

        console.log('🚀 موقع المهندس العراقي المحسن تم تحميله بنجاح!');
        console.log('✨ "التميز ليس مجرد هدف، بل أسلوب حياة"');
        console.log('🎯 "كل حلم يبدأ بخطوة، وكل خطوة تقربك من النجاح"');
    </script>
</body>
</html>