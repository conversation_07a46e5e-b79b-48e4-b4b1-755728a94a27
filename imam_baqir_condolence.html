<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ذكرى الإمام محمد الباقر عليه السلام - AliToucan</title>
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'Scheherazade New', 'Noto Sans Arabic', serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #d4af37;
        }

        .container {
            max-width: 600px;
            width: 100%;
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            font-family: 'Amiri', serif;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            color: #b8860b;
            font-family: 'Scheherazade New', serif;
        }

        /* Condolence Design Canvas with 3D Effects */
        .design-container {
            position: relative;
            width: 500px;
            height: 500px;
            margin: 0 auto 30px;
            background:
                linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
                linear-gradient(225deg, rgba(212, 175, 55, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #0f3460 100%);
            border: 3px solid #d4af37;
            border-radius: 15px;
            box-shadow:
                0 0 0 1px rgba(212, 175, 55, 0.3),
                0 5px 15px rgba(0,0,0,0.6),
                0 15px 35px rgba(0,0,0,0.4),
                0 25px 50px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(212, 175, 55, 0.2),
                inset 0 -1px 0 rgba(0,0,0,0.5);
            overflow: hidden;
            transform-style: preserve-3d;
            perspective: 1000px;
            transform: rotateX(2deg) rotateY(-1deg);
        }

        .design-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(ellipse at top left, rgba(212, 175, 55, 0.15) 0%, transparent 50%),
                radial-gradient(ellipse at bottom right, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        .islamic-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.2) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(212, 175, 55, 0.15) 2px, transparent 2px),
                radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.1) 1px, transparent 1px);
            background-size: 50px 50px, 50px 50px, 25px 25px;
            opacity: 0.4;
            z-index: 2;
            transform: translateZ(5px);
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .islamic-pattern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(45deg, transparent 40%, rgba(212, 175, 55, 0.05) 50%, transparent 60%),
                linear-gradient(-45deg, transparent 40%, rgba(212, 175, 55, 0.05) 50%, transparent 60%);
            background-size: 30px 30px;
            transform: translateZ(2px);
        }

        .geometric-border {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 2px solid #d4af37;
            border-radius: 10px;
            background:
                linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(212, 175, 55, 0.05) 50%, rgba(212, 175, 55, 0.02) 100%);
            box-shadow:
                inset 0 1px 0 rgba(212, 175, 55, 0.3),
                inset 0 -1px 0 rgba(0,0,0,0.3),
                inset 1px 0 0 rgba(212, 175, 55, 0.2),
                inset -1px 0 0 rgba(0,0,0,0.2),
                0 2px 8px rgba(0,0,0,0.2);
            transform: translateZ(3px);
            z-index: 3;
        }

        .geometric-border::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 1px solid rgba(212, 175, 55, 0.4);
            border-radius: 8px;
            box-shadow:
                inset 0 1px 0 rgba(212, 175, 55, 0.2),
                inset 0 -1px 0 rgba(0,0,0,0.2);
        }

        .content-area {
            position: relative;
            z-index: 10;
            padding: 40px 30px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            text-align: center;
            transform-style: preserve-3d;
        }

        .imam-name {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            font-weight: 700;
            color: #d4af37;
            text-shadow:
                0 1px 0 #b8860b,
                0 2px 0 #9a7209,
                0 3px 0 #7d5e07,
                0 4px 0 #604a05,
                0 5px 0 #433603,
                0 6px 0 #262201,
                0 7px 10px rgba(0,0,0,0.6),
                0 10px 20px rgba(0,0,0,0.4),
                0 15px 30px rgba(0,0,0,0.2);
            margin-bottom: 15px;
            line-height: 1.3;
            transform: translateZ(15px);
            filter: drop-shadow(0 5px 10px rgba(0,0,0,0.5));
            position: relative;
        }

        .imam-name::before {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.8), rgba(184, 134, 11, 0.6));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            transform: translateZ(-1px);
        }

        .peace-phrase {
            font-family: 'Scheherazade New', serif;
            font-size: 1.1rem;
            color: #b8860b;
            margin-bottom: 20px;
            text-shadow:
                0 1px 0 #9a7209,
                0 2px 0 #7d5e07,
                0 3px 5px rgba(0,0,0,0.5),
                0 5px 10px rgba(0,0,0,0.3);
            transform: translateZ(10px);
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.4));
        }

        .condolence-text {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1.3rem;
            color: #ffffff;
            text-shadow:
                0 1px 0 #cccccc,
                0 2px 0 #999999,
                0 3px 0 #666666,
                0 4px 0 #333333,
                0 5px 0 #000000,
                0 6px 8px rgba(0,0,0,0.6),
                0 8px 15px rgba(0,0,0,0.4),
                0 12px 25px rgba(0,0,0,0.2);
            margin-bottom: 15px;
            line-height: 1.5;
            transform: translateZ(12px);
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.5));
        }

        .prayer-text {
            font-family: 'Amiri', serif;
            font-size: 1rem;
            color: #d4af37;
            font-style: italic;
            margin-bottom: 20px;
            line-height: 1.4;
            text-shadow:
                0 1px 0 #b8860b,
                0 2px 0 #9a7209,
                0 3px 0 #7d5e07,
                0 4px 6px rgba(0,0,0,0.5),
                0 6px 12px rgba(0,0,0,0.3);
            transform: translateZ(8px);
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.4));
        }



        /* Control Panel */
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #d4af37, #b8860b);
            color: #000;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #b8860b, #d4af37);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #d4af37;
            border: 2px solid #d4af37;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #d4af37, #b8860b);
            color: #000;
            transform: translateY(-2px);
        }

        /* Loading Animation */
        .loading {
            display: none;
            text-align: center;
            color: #d4af37;
            font-size: 1.1rem;
            margin-top: 10px;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            border-top-color: #d4af37;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .design-container {
                width: 90vw;
                height: 90vw;
                max-width: 400px;
                max-height: 400px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .imam-name {
                font-size: 1.8rem;
            }
            
            .condolence-text {
                font-size: 1.1rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }

        @media (max-width: 480px) {
            .design-container {
                width: 95vw;
                height: 95vw;
            }
            
            .content-area {
                padding: 25px 20px;
            }
            
            .imam-name {
                font-size: 1.5rem;
            }
            
            .condolence-text {
                font-size: 1rem;
            }
        }

        /* Enhanced 3D Islamic Decorative Elements */
        .decorative-corner {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #d4af37;
            z-index: 15;
            transform-style: preserve-3d;
            transform: translateZ(20px);
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.6));
        }

        .decorative-corner::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px solid rgba(212, 175, 55, 0.5);
            transform: translateZ(-2px);
        }

        .decorative-corner::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            transform: translateZ(2px);
        }

        .corner-top-left {
            top: 25px;
            left: 25px;
            border-right: none;
            border-bottom: none;
            box-shadow:
                inset 1px 1px 0 rgba(212, 175, 55, 0.4),
                -2px -2px 8px rgba(0,0,0,0.3),
                -4px -4px 15px rgba(0,0,0,0.2);
        }

        .corner-top-left::before {
            border-right: none;
            border-bottom: none;
        }

        .corner-top-left::after {
            border-right: none;
            border-bottom: none;
        }

        .corner-top-right {
            top: 25px;
            right: 25px;
            border-left: none;
            border-bottom: none;
            box-shadow:
                inset -1px 1px 0 rgba(212, 175, 55, 0.4),
                2px -2px 8px rgba(0,0,0,0.3),
                4px -4px 15px rgba(0,0,0,0.2);
        }

        .corner-top-right::before {
            border-left: none;
            border-bottom: none;
        }

        .corner-top-right::after {
            border-left: none;
            border-bottom: none;
        }

        .corner-bottom-left {
            bottom: 25px;
            left: 25px;
            border-right: none;
            border-top: none;
            box-shadow:
                inset 1px -1px 0 rgba(212, 175, 55, 0.4),
                -2px 2px 8px rgba(0,0,0,0.3),
                -4px 4px 15px rgba(0,0,0,0.2);
        }

        .corner-bottom-left::before {
            border-right: none;
            border-top: none;
        }

        .corner-bottom-left::after {
            border-right: none;
            border-top: none;
        }

        .corner-bottom-right {
            bottom: 25px;
            right: 25px;
            border-left: none;
            border-top: none;
            box-shadow:
                inset -1px -1px 0 rgba(212, 175, 55, 0.4),
                2px 2px 8px rgba(0,0,0,0.3),
                4px 4px 15px rgba(0,0,0,0.2);
        }

        .corner-bottom-right::before {
            border-left: none;
            border-top: none;
        }

        .corner-bottom-right::after {
            border-left: none;
            border-top: none;
        }

        /* 3D Hover Effects */
        .design-container:hover {
            transform: rotateX(3deg) rotateY(-2deg) scale(1.02);
            transition: transform 0.6s ease;
        }

        .design-container:hover .imam-name {
            transform: translateZ(25px);
            transition: transform 0.4s ease;
        }

        .design-container:hover .condolence-text {
            transform: translateZ(18px);
            transition: transform 0.4s ease;
        }

        .design-container:hover .decorative-corner {
            transform: translateZ(30px);
            transition: transform 0.4s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">ذكرى استشهاد الإمام محمد الباقر</h1>
        <p class="subtitle">عليه أفضل الصلاة والسلام</p>
    </div>

    <!-- Condolence Design -->
    <div class="design-container" id="condolenceDesign">
        <div class="islamic-pattern"></div>
        <div class="geometric-border"></div>

        <!-- Decorative Corners -->
        <div class="decorative-corner corner-top-left"></div>
        <div class="decorative-corner corner-top-right"></div>
        <div class="decorative-corner corner-bottom-left"></div>
        <div class="decorative-corner corner-bottom-right"></div>

        <div class="content-area">
            <div>
                <div class="imam-name" data-text="الإمام محمد الباقر">الإمام محمد الباقر</div>
                <div class="peace-phrase">عليه السلام</div>
                <div class="condolence-text">عظم الله أجوركم بذكرى استشهاد</div>
                <div class="condolence-text">باقر علوم الأولين والآخرين</div>
            </div>

            <div>
                <div class="prayer-text">اللهم صل على محمد وآل محمد</div>
                <div class="prayer-text">وعجل فرجهم الشريف</div>
            </div>
        </div>


    </div>

    <!-- Control Panel -->
    <div class="controls">
        <button class="btn btn-primary" onclick="downloadImage()">تحميل عالي الجودة</button>
        <button class="btn btn-secondary" onclick="downloadImageFallback()">تحميل بديل</button>
        <button class="btn btn-secondary" onclick="downloadImageAlternative()">تحميل يدوي مثالي</button>
        <button class="btn btn-secondary" onclick="shareDesign()">مشاركة التصميم</button>
        <button class="btn btn-secondary" onclick="previewDesign()">معاينة</button>
        <button class="btn btn-secondary" onclick="verifyDownloadQuality()" style="font-size: 0.9rem;">فحص الجودة</button>
        <button class="btn btn-secondary" onclick="debugCanvasCapture()" style="font-size: 0.8rem;">مقارنة مرئية</button>
    </div>

    <div class="loading" id="loadingIndicator">
        جاري تحضير الصورة...
        <div class="spinner"></div>
    </div>

    <!-- Hidden Canvas for Image Generation -->
    <canvas id="hiddenCanvas" style="display: none;"></canvas>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // Enhanced image download with perfect visual fidelity
        async function downloadImage() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const designContainer = document.getElementById('condolenceDesign');

            try {
                // Show loading indicator
                loadingIndicator.style.display = 'block';
                showNotification('جاري تحضير الصورة بجودة عالية...', 'info');

                // Ensure all fonts are loaded and wait for complete rendering
                await document.fonts.ready;
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Create a dedicated capture container with exact styling
                const captureContainer = document.createElement('div');
                captureContainer.id = 'captureContainer';
                captureContainer.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 500px;
                    height: 500px;
                    z-index: 10000;
                    visibility: visible;
                    opacity: 1;
                    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
                               linear-gradient(225deg, rgba(212, 175, 55, 0.05) 0%, transparent 50%),
                               linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #0f3460 100%);
                    border: 3px solid #d4af37;
                    border-radius: 15px;
                    box-shadow: 0 0 0 1px rgba(212, 175, 55, 0.3),
                               0 5px 15px rgba(0,0,0,0.6),
                               0 15px 35px rgba(0,0,0,0.4),
                               0 25px 50px rgba(0,0,0,0.2),
                               inset 0 1px 0 rgba(212, 175, 55, 0.2),
                               inset 0 -1px 0 rgba(0,0,0,0.5);
                    overflow: hidden;
                `;

                // Clone the content with all styling preserved
                const contentClone = designContainer.innerHTML;
                captureContainer.innerHTML = contentClone;

                // Add to document for rendering
                document.body.appendChild(captureContainer);

                // Wait for rendering
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Configure html2canvas with maximum quality settings
                const options = {
                    scale: 3, // High resolution
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null,
                    width: 500,
                    height: 500,
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: 1920,
                    windowHeight: 1080,
                    foreignObjectRendering: true,
                    removeContainer: false,
                    imageTimeout: 60000,
                    logging: false,
                    ignoreElements: function(element) {
                        // Ignore elements that might interfere with capture
                        return element.classList && (
                            element.classList.contains('controls') ||
                            element.classList.contains('loading') ||
                            element.id === 'loadingIndicator'
                        );
                    },
                    onclone: function(clonedDoc, element) {
                        // Ensure all styles are properly applied
                        const clonedContainer = clonedDoc.getElementById('captureContainer');
                        if (clonedContainer) {
                            // Force all text elements to be visible and properly styled
                            const textElements = clonedContainer.querySelectorAll('.imam-name, .peace-phrase, .condolence-text, .prayer-text');
                            textElements.forEach(el => {
                                el.style.visibility = 'visible';
                                el.style.opacity = '1';
                                el.style.display = 'block';

                                // Preserve text shadows and 3D effects
                                if (el.classList.contains('imam-name')) {
                                    el.style.textShadow = `
                                        0 1px 0 #b8860b,
                                        0 2px 0 #9a7209,
                                        0 3px 0 #7d5e07,
                                        0 4px 0 #604a05,
                                        0 5px 0 #433603,
                                        0 6px 0 #262201,
                                        0 7px 10px rgba(0,0,0,0.6),
                                        0 10px 20px rgba(0,0,0,0.4),
                                        0 15px 30px rgba(0,0,0,0.2)
                                    `;
                                }

                                if (el.classList.contains('condolence-text')) {
                                    el.style.textShadow = `
                                        0 1px 0 #cccccc,
                                        0 2px 0 #999999,
                                        0 3px 0 #666666,
                                        0 4px 0 #333333,
                                        0 5px 0 #000000,
                                        0 6px 8px rgba(0,0,0,0.6),
                                        0 8px 15px rgba(0,0,0,0.4),
                                        0 12px 25px rgba(0,0,0,0.2)
                                    `;
                                }

                                if (el.classList.contains('prayer-text')) {
                                    el.style.textShadow = `
                                        0 1px 0 #b8860b,
                                        0 2px 0 #9a7209,
                                        0 3px 0 #7d5e07,
                                        0 4px 6px rgba(0,0,0,0.5),
                                        0 6px 12px rgba(0,0,0,0.3)
                                    `;
                                }

                                if (el.classList.contains('peace-phrase')) {
                                    el.style.textShadow = `
                                        0 1px 0 #9a7209,
                                        0 2px 0 #7d5e07,
                                        0 3px 5px rgba(0,0,0,0.5),
                                        0 5px 10px rgba(0,0,0,0.3)
                                    `;
                                }
                            });

                            // Ensure decorative corners are visible
                            const corners = clonedContainer.querySelectorAll('.decorative-corner');
                            corners.forEach(corner => {
                                corner.style.visibility = 'visible';
                                corner.style.opacity = '1';
                                corner.style.display = 'block';
                            });
                        }

                        // Add font loading to cloned document
                        const fontLink = clonedDoc.createElement('link');
                        fontLink.href = 'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@400;700&display=swap';
                        fontLink.rel = 'stylesheet';
                        clonedDoc.head.appendChild(fontLink);

                        return clonedDoc;
                    }
                };

                console.log('Starting high-quality capture...');

                // Generate canvas from the capture container
                const canvas = await html2canvas(captureContainer, options);

                // Remove capture container
                document.body.removeChild(captureContainer);

                console.log('High-quality canvas generated:', canvas.width, 'x', canvas.height);

                // Verify canvas has content
                if (canvas.width === 0 || canvas.height === 0) {
                    throw new Error('Canvas is empty - dimensions: ' + canvas.width + 'x' + canvas.height);
                }

                // Create download link with maximum quality
                const link = document.createElement('a');
                link.download = 'الإمام_محمد_الباقر_عليه_السلام_تعزية_عالية_الجودة.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message
                showNotification(`تم تحميل الصورة بجودة عالية! (${canvas.width}x${canvas.height})`, 'success');

            } catch (error) {
                console.error('High-quality capture failed:', error);
                showNotification('فشل التحميل عالي الجودة. جاري المحاولة بطريقة بديلة...', 'error');

                // Try fallback method
                setTimeout(() => {
                    downloadImageFallback();
                }, 1000);

            } finally {
                // Hide loading indicator
                loadingIndicator.style.display = 'none';
            }
        }

        // Improved fallback download method
        async function downloadImageFallback() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const designContainer = document.getElementById('condolenceDesign');

            try {
                loadingIndicator.style.display = 'block';
                showNotification('جاري المحاولة بالطريقة البديلة...', 'info');

                // Wait for fonts
                await document.fonts.ready;
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Create a completely isolated container for capture
                const captureContainer = document.createElement('div');
                captureContainer.innerHTML = designContainer.outerHTML;
                captureContainer.style.position = 'absolute';
                captureContainer.style.top = '0';
                captureContainer.style.left = '0';
                captureContainer.style.width = '500px';
                captureContainer.style.height = '500px';
                captureContainer.style.zIndex = '9999';
                captureContainer.style.background = 'linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #0f3460 100%)';
                captureContainer.style.visibility = 'visible';
                captureContainer.style.opacity = '1';

                // Add to body temporarily
                document.body.appendChild(captureContainer);

                // Get the inner design container
                const innerDesign = captureContainer.querySelector('#condolenceDesign');
                if (innerDesign) {
                    innerDesign.id = 'tempDesignContainer';
                    innerDesign.style.position = 'relative';
                    innerDesign.style.transform = 'none';
                    innerDesign.style.transition = 'none';
                }

                // Wait for rendering
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Simple but effective options
                const options = {
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#000000',
                    width: 500,
                    height: 500,
                    logging: false,
                    foreignObjectRendering: false
                };

                const canvas = await html2canvas(innerDesign || captureContainer, options);

                // Remove temporary container
                document.body.removeChild(captureContainer);

                if (canvas.width === 0 || canvas.height === 0) {
                    throw new Error('Fallback canvas is also empty');
                }

                const link = document.createElement('a');
                link.download = 'الإمام_محمد_الباقر_عليه_السلام_تعزية_بديل.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showNotification(`تم تحميل الصورة بالطريقة البديلة! (${canvas.width}x${canvas.height})`, 'success');

            } catch (error) {
                console.error('Fallback method failed:', error);
                showNotification('فشلت جميع طرق التحميل. يرجى تجربة متصفح آخر.', 'error');

                // Show the alternative download button
                if (window.showAlternativeDownload) {
                    window.showAlternativeDownload();
                }
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }

        // Share functionality
        async function shareDesign() {
            if (navigator.share) {
                try {
                    await navigator.share({
                        title: 'ذكرى استشهاد الإمام محمد الباقر عليه السلام',
                        text: 'عظم الله أجوركم بذكرى استشهاد باقر علوم الأولين والآخرين',
                        url: window.location.href
                    });
                } catch (error) {
                    console.log('Error sharing:', error);
                    copyToClipboard();
                }
            } else {
                copyToClipboard();
            }
        }

        // Copy URL to clipboard
        function copyToClipboard() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('تم نسخ الرابط إلى الحافظة!', 'success');
            }).catch(() => {
                showNotification('لم يتم نسخ الرابط. يرجى النسخ يدوياً.', 'error');
            });
        }

        // Preview functionality
        function previewDesign() {
            const designContainer = document.getElementById('condolenceDesign');

            // Create modal for preview
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                cursor: pointer;
            `;

            const previewImage = designContainer.cloneNode(true);
            previewImage.style.cssText = `
                transform: scale(1.2);
                box-shadow: 0 20px 60px rgba(212, 175, 55, 0.3);
            `;

            modal.appendChild(previewImage);
            document.body.appendChild(modal);

            // Close modal on click
            modal.addEventListener('click', () => {
                document.body.removeChild(modal);
            });

            // Close modal on escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(modal);
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-family: 'Noto Sans Arabic', sans-serif;
                font-weight: 600;
                z-index: 1001;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                text-align: right;
                direction: rtl;
            `;

            // Set background color based on type
            switch (type) {
                case 'success':
                    notification.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                    break;
                case 'error':
                    notification.style.background = 'linear-gradient(135deg, #dc3545, #e74c3c)';
                    break;
                default:
                    notification.style.background = 'linear-gradient(135deg, #d4af37, #b8860b)';
                    notification.style.color = '#000';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth animations
            const designContainer = document.getElementById('condolenceDesign');
            designContainer.style.opacity = '0';
            designContainer.style.transform = 'translateY(20px)';

            setTimeout(() => {
                designContainer.style.transition = 'all 0.8s ease';
                designContainer.style.opacity = '1';
                designContainer.style.transform = 'translateY(0)';
            }, 300);

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 's':
                            e.preventDefault();
                            downloadImage();
                            break;
                        case 'p':
                            e.preventDefault();
                            previewDesign();
                            break;
                    }
                }
            });

            // Ensure fonts are loaded before showing notification
            document.fonts.ready.then(() => {
                showNotification('مرحباً بكم في تصميم تعزية الإمام محمد الباقر عليه السلام', 'info');
            });

            // Add alternative download method button for testing
            addAlternativeDownloadButton();
        });

        // Add alternative download method for testing
        function addAlternativeDownloadButton() {
            const controls = document.querySelector('.controls');
            const altButton = document.createElement('button');
            altButton.className = 'btn btn-secondary';
            altButton.textContent = 'تحميل بديل';
            altButton.onclick = downloadImageAlternative;
            altButton.style.display = 'none'; // Hidden by default
            controls.appendChild(altButton);

            // Show alternative button if main download fails
            window.showAlternativeDownload = function() {
                altButton.style.display = 'inline-block';
            };
        }

        // Perfect manual canvas recreation with exact 3D effects
        async function downloadImageAlternative() {
            const loadingIndicator = document.getElementById('loadingIndicator');

            try {
                loadingIndicator.style.display = 'block';
                showNotification('جاري إنشاء صورة مثالية بالطريقة اليدوية...', 'info');

                // Create high-resolution canvas
                const canvas = document.createElement('canvas');
                canvas.width = 1500; // 3x resolution for ultra-high quality
                canvas.height = 1500;
                const ctx = canvas.getContext('2d');

                // Enable high-quality rendering
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';
                ctx.textRenderingOptimization = 'optimizeQuality';

                // Draw complex background with multiple gradients
                const bgGradient1 = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                bgGradient1.addColorStop(0, 'rgba(212, 175, 55, 0.1)');
                bgGradient1.addColorStop(0.5, 'transparent');
                bgGradient1.addColorStop(1, 'transparent');

                const bgGradient2 = ctx.createLinearGradient(canvas.width, 0, 0, canvas.height);
                bgGradient2.addColorStop(0, 'rgba(212, 175, 55, 0.05)');
                bgGradient2.addColorStop(0.5, 'transparent');
                bgGradient2.addColorStop(1, 'transparent');

                const mainGradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                mainGradient.addColorStop(0, '#000000');
                mainGradient.addColorStop(0.5, '#1a1a2e');
                mainGradient.addColorStop(1, '#0f3460');

                // Apply background layers
                ctx.fillStyle = mainGradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = bgGradient1;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = bgGradient2;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw Islamic geometric patterns
                ctx.save();
                ctx.globalAlpha = 0.4;

                // Pattern layer 1
                for (let x = 0; x < canvas.width; x += 75) {
                    for (let y = 0; y < canvas.height; y += 75) {
                        ctx.fillStyle = 'rgba(212, 175, 55, 0.2)';
                        ctx.beginPath();
                        ctx.arc(x + 18.75, y + 18.75, 3, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }

                // Pattern layer 2
                for (let x = 37.5; x < canvas.width; x += 75) {
                    for (let y = 37.5; y < canvas.height; y += 75) {
                        ctx.fillStyle = 'rgba(212, 175, 55, 0.15)';
                        ctx.beginPath();
                        ctx.arc(x + 18.75, y + 18.75, 3, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }

                ctx.restore();

                // Draw 3D beveled border with multiple layers
                const borderLayers = [
                    { offset: 0, width: 9, color: '#d4af37', alpha: 1 },
                    { offset: 3, width: 6, color: 'rgba(212, 175, 55, 0.3)', alpha: 0.8 },
                    { offset: -3, width: 3, color: 'rgba(0, 0, 0, 0.5)', alpha: 0.6 }
                ];

                borderLayers.forEach(layer => {
                    ctx.save();
                    ctx.globalAlpha = layer.alpha;
                    ctx.strokeStyle = layer.color;
                    ctx.lineWidth = layer.width;
                    ctx.strokeRect(22.5 + layer.offset, 22.5 + layer.offset,
                                 canvas.width - 45 - (layer.offset * 2),
                                 canvas.height - 45 - (layer.offset * 2));
                    ctx.restore();
                });

                // Draw inner geometric border with 3D effect
                ctx.save();
                ctx.strokeStyle = '#d4af37';
                ctx.lineWidth = 6;
                ctx.strokeRect(67.5, 67.5, canvas.width - 135, canvas.height - 135);

                // Inner border shadow
                ctx.strokeStyle = 'rgba(212, 175, 55, 0.4)';
                ctx.lineWidth = 3;
                ctx.strokeRect(75, 75, canvas.width - 150, canvas.height - 150);
                ctx.restore();

                // Draw enhanced 3D decorative corners
                const cornerSize = 120;
                const cornerOffset = 112.5;

                const drawCorner = (x1, y1, x2, y2, x3, y3) => {
                    // Main corner
                    ctx.strokeStyle = '#d4af37';
                    ctx.lineWidth = 6;
                    ctx.beginPath();
                    ctx.moveTo(x1, y1);
                    ctx.lineTo(x2, y2);
                    ctx.lineTo(x3, y3);
                    ctx.stroke();

                    // Corner shadow/depth
                    ctx.strokeStyle = 'rgba(212, 175, 55, 0.5)';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x1 - 3, y1 - 3);
                    ctx.lineTo(x2 - 3, y2 - 3);
                    ctx.lineTo(x3 - 3, y3 - 3);
                    ctx.stroke();

                    // Corner highlight
                    ctx.strokeStyle = 'rgba(212, 175, 55, 0.3)';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(x1 + 3, y1 + 3);
                    ctx.lineTo(x2 + 3, y2 + 3);
                    ctx.lineTo(x3 + 3, y3 + 3);
                    ctx.stroke();
                };

                // Draw all four corners with 3D effect
                drawCorner(cornerOffset, cornerOffset + cornerSize, cornerOffset, cornerOffset, cornerOffset + cornerSize, cornerOffset);
                drawCorner(canvas.width - cornerOffset - cornerSize, cornerOffset, canvas.width - cornerOffset, cornerOffset, canvas.width - cornerOffset, cornerOffset + cornerSize);
                drawCorner(cornerOffset, canvas.height - cornerOffset - cornerSize, cornerOffset, canvas.height - cornerOffset, cornerOffset + cornerSize, canvas.height - cornerOffset);
                drawCorner(canvas.width - cornerOffset - cornerSize, canvas.height - cornerOffset, canvas.width - cornerOffset, canvas.height - cornerOffset, canvas.width - cornerOffset, canvas.height - cornerOffset - cornerSize);

                // Set text properties for Arabic
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.direction = 'rtl';

                // Enhanced 3D text drawing with perfect shadows
                const drawText3D = (text, x, y, font, mainColor, shadowLayers) => {
                    ctx.font = font;

                    // Draw shadow layers from back to front
                    shadowLayers.forEach((layer, index) => {
                        ctx.fillStyle = layer.color;
                        ctx.fillText(text, x + layer.offsetX, y + layer.offsetY);
                    });

                    // Draw main text
                    ctx.fillStyle = mainColor;
                    ctx.fillText(text, x, y);
                };

                // Imam's name with dramatic 3D effect
                const titleText = 'الإمام محمد الباقر';
                const titleY = 450; // Scaled for 1500px canvas
                const titleShadows = [
                    { offsetX: 9, offsetY: 9, color: 'rgba(38, 34, 1, 0.8)' },
                    { offsetX: 8, offsetY: 8, color: 'rgba(67, 54, 3, 0.7)' },
                    { offsetX: 7, offsetY: 7, color: 'rgba(96, 74, 5, 0.6)' },
                    { offsetX: 6, offsetY: 6, color: 'rgba(125, 94, 7, 0.5)' },
                    { offsetX: 5, offsetY: 5, color: 'rgba(154, 114, 9, 0.4)' },
                    { offsetX: 4, offsetY: 4, color: 'rgba(184, 134, 11, 0.3)' },
                    { offsetX: 3, offsetY: 3, color: 'rgba(212, 175, 55, 0.2)' }
                ];

                drawText3D(titleText, canvas.width / 2, titleY, 'bold 96px Amiri, serif', '#d4af37', titleShadows);

                // Peace phrase with elegant 3D effect
                const peaceText = 'عليه السلام';
                const peaceY = 555;
                const peaceShadows = [
                    { offsetX: 5, offsetY: 5, color: 'rgba(125, 94, 7, 0.7)' },
                    { offsetX: 4, offsetY: 4, color: 'rgba(140, 109, 7, 0.6)' },
                    { offsetX: 3, offsetY: 3, color: 'rgba(154, 114, 9, 0.5)' },
                    { offsetX: 2, offsetY: 2, color: 'rgba(169, 129, 10, 0.4)' },
                    { offsetX: 1, offsetY: 1, color: 'rgba(184, 134, 11, 0.3)' }
                ];

                drawText3D(peaceText, canvas.width / 2, peaceY, '48px Scheherazade New, serif', '#b8860b', peaceShadows);

                // Condolence text with embossed 3D effect
                const condolenceText1 = 'عظم الله أجوركم بذكرى استشهاد';
                const condolenceText2 = 'باقر علوم الأولين والآخرين';
                const condolenceY1 = 750;
                const condolenceY2 = 840;
                const condolenceShadows = [
                    { offsetX: 7, offsetY: 7, color: 'rgba(51, 51, 51, 0.6)' },
                    { offsetX: 6, offsetY: 6, color: 'rgba(102, 102, 102, 0.5)' },
                    { offsetX: 5, offsetY: 5, color: 'rgba(153, 153, 153, 0.4)' },
                    { offsetX: 4, offsetY: 4, color: 'rgba(204, 204, 204, 0.3)' },
                    { offsetX: 3, offsetY: 3, color: 'rgba(230, 230, 230, 0.2)' },
                    { offsetX: 2, offsetY: 2, color: 'rgba(245, 245, 245, 0.1)' }
                ];

                drawText3D(condolenceText1, canvas.width / 2, condolenceY1, '57px Noto Sans Arabic, sans-serif', '#ffffff', condolenceShadows);
                drawText3D(condolenceText2, canvas.width / 2, condolenceY2, '57px Noto Sans Arabic, sans-serif', '#ffffff', condolenceShadows);

                // Prayer text with golden 3D effect
                const prayerText1 = 'اللهم صل على محمد وآل محمد';
                const prayerText2 = 'وعجل فرجهم الشريف';
                const prayerY1 = 1050;
                const prayerY2 = 1125;
                const prayerShadows = [
                    { offsetX: 6, offsetY: 6, color: 'rgba(125, 94, 7, 0.7)' },
                    { offsetX: 5, offsetY: 5, color: 'rgba(140, 109, 7, 0.6)' },
                    { offsetX: 4, offsetY: 4, color: 'rgba(154, 114, 9, 0.5)' },
                    { offsetX: 3, offsetY: 3, color: 'rgba(169, 129, 10, 0.4)' },
                    { offsetX: 2, offsetY: 2, color: 'rgba(184, 134, 11, 0.3)' }
                ];

                drawText3D(prayerText1, canvas.width / 2, prayerY1, 'italic 42px Amiri, serif', '#d4af37', prayerShadows);
                drawText3D(prayerText2, canvas.width / 2, prayerY2, 'italic 42px Amiri, serif', '#d4af37', prayerShadows);

                // Add subtle lighting effects
                const lightGradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 3, 0, canvas.width / 2, canvas.height / 3, canvas.width / 2);
                lightGradient.addColorStop(0, 'rgba(212, 175, 55, 0.1)');
                lightGradient.addColorStop(0.5, 'rgba(212, 175, 55, 0.05)');
                lightGradient.addColorStop(1, 'transparent');

                ctx.save();
                ctx.globalCompositeOperation = 'overlay';
                ctx.fillStyle = lightGradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.restore();

                // Create download link
                const link = document.createElement('a');
                link.download = 'الإمام_محمد_الباقر_عليه_السلام_تعزية_يدوي.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showNotification('تم تحميل الصورة بالطريقة اليدوية بنجاح!', 'success');

            } catch (error) {
                console.error('Manual canvas method failed:', error);
                showNotification('فشل التحميل اليدوي أيضاً', 'error');
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }

        // Enhanced debug function with visual quality comparison
        function debugCanvasCapture() {
            const designContainer = document.getElementById('condolenceDesign');
            console.log('=== VISUAL QUALITY DEBUG INFO ===');
            console.log('Container:', designContainer);
            console.log('Container dimensions:', designContainer.getBoundingClientRect());
            console.log('Container computed style:', window.getComputedStyle(designContainer));
            console.log('Fonts loaded:', document.fonts.status);
            console.log('Available fonts:', Array.from(document.fonts).map(f => f.family));

            // Test canvas capabilities
            const testCanvas = document.createElement('canvas');
            testCanvas.width = 500;
            testCanvas.height = 500;
            const testCtx = testCanvas.getContext('2d');

            // Test gradient support
            const gradient = testCtx.createLinearGradient(0, 0, 500, 500);
            gradient.addColorStop(0, '#000000');
            gradient.addColorStop(1, '#1a1a2e');
            testCtx.fillStyle = gradient;
            testCtx.fillRect(0, 0, 500, 500);

            // Test text rendering
            testCtx.font = 'bold 48px Amiri, serif';
            testCtx.fillStyle = '#d4af37';
            testCtx.textAlign = 'center';
            testCtx.fillText('الإمام محمد الباقر', 250, 250);

            console.log('Test canvas data URL length:', testCanvas.toDataURL().length);
            console.log('Canvas context properties:', {
                imageSmoothingEnabled: testCtx.imageSmoothingEnabled,
                imageSmoothingQuality: testCtx.imageSmoothingQuality,
                textAlign: testCtx.textAlign,
                textBaseline: testCtx.textBaseline
            });

            // Create visual comparison modal
            createVisualComparisonModal(testCanvas);

            showNotification('معلومات التشخيص متوفرة في وحدة التحكم والمقارنة المرئية', 'info');
        }

        // Create visual comparison modal
        function createVisualComparisonModal(testCanvas) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                color: white;
                font-family: 'Noto Sans Arabic', sans-serif;
                direction: rtl;
                text-align: center;
                padding: 20px;
                box-sizing: border-box;
            `;

            const title = document.createElement('h2');
            title.textContent = 'مقارنة جودة الصورة';
            title.style.cssText = 'margin-bottom: 20px; color: #d4af37;';

            const container = document.createElement('div');
            container.style.cssText = `
                display: flex;
                gap: 20px;
                align-items: center;
                justify-content: center;
                flex-wrap: wrap;
            `;

            // Original design
            const originalDiv = document.createElement('div');
            originalDiv.style.cssText = 'text-align: center;';
            const originalLabel = document.createElement('p');
            originalLabel.textContent = 'التصميم الأصلي';
            originalLabel.style.cssText = 'margin-bottom: 10px; color: #d4af37;';
            const originalDesign = document.getElementById('condolenceDesign').cloneNode(true);
            originalDesign.style.cssText = `
                transform: scale(0.8);
                margin: 0;
                box-shadow: 0 5px 20px rgba(212, 175, 55, 0.3);
            `;
            originalDiv.appendChild(originalLabel);
            originalDiv.appendChild(originalDesign);

            // Test canvas
            const canvasDiv = document.createElement('div');
            canvasDiv.style.cssText = 'text-align: center;';
            const canvasLabel = document.createElement('p');
            canvasLabel.textContent = 'اختبار Canvas';
            canvasLabel.style.cssText = 'margin-bottom: 10px; color: #d4af37;';
            testCanvas.style.cssText = `
                transform: scale(0.8);
                border: 2px solid #d4af37;
                border-radius: 10px;
                box-shadow: 0 5px 20px rgba(212, 175, 55, 0.3);
            `;
            canvasDiv.appendChild(canvasLabel);
            canvasDiv.appendChild(testCanvas);

            container.appendChild(originalDiv);
            container.appendChild(canvasDiv);

            const instructions = document.createElement('p');
            instructions.textContent = 'اضغط في أي مكان للإغلاق';
            instructions.style.cssText = 'margin-top: 20px; color: #b8860b;';

            modal.appendChild(title);
            modal.appendChild(container);
            modal.appendChild(instructions);

            // Close modal on click
            modal.addEventListener('click', () => {
                document.body.removeChild(modal);
            });

            document.body.appendChild(modal);
        }

        // Quality verification function
        async function verifyDownloadQuality() {
            showNotification('جاري التحقق من جودة التحميل...', 'info');

            try {
                // Test all three download methods
                const results = {
                    primary: await testDownloadMethod('primary'),
                    fallback: await testDownloadMethod('fallback'),
                    manual: await testDownloadMethod('manual')
                };

                console.log('Download quality test results:', results);

                const bestMethod = Object.keys(results).reduce((a, b) =>
                    results[a].score > results[b].score ? a : b
                );

                showNotification(`أفضل طريقة تحميل: ${bestMethod} (نقاط: ${results[bestMethod].score})`, 'success');

            } catch (error) {
                console.error('Quality verification failed:', error);
                showNotification('فشل في التحقق من الجودة', 'error');
            }
        }

        // Test download method quality
        async function testDownloadMethod(method) {
            const startTime = performance.now();

            try {
                let canvas;

                switch (method) {
                    case 'primary':
                        canvas = await html2canvas(document.getElementById('condolenceDesign'), {
                            scale: 2,
                            useCORS: true,
                            allowTaint: true,
                            backgroundColor: '#000000'
                        });
                        break;
                    case 'fallback':
                        // Simplified test
                        canvas = await html2canvas(document.getElementById('condolenceDesign'), {
                            scale: 1,
                            useCORS: true,
                            allowTaint: true
                        });
                        break;
                    case 'manual':
                        // Test manual canvas creation
                        canvas = document.createElement('canvas');
                        canvas.width = 500;
                        canvas.height = 500;
                        const ctx = canvas.getContext('2d');
                        ctx.fillStyle = '#000000';
                        ctx.fillRect(0, 0, 500, 500);
                        ctx.fillStyle = '#d4af37';
                        ctx.font = 'bold 32px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('Test', 250, 250);
                        break;
                }

                const endTime = performance.now();
                const duration = endTime - startTime;

                // Calculate quality score
                const dataURL = canvas.toDataURL('image/png', 1.0);
                const score = Math.min(100, Math.max(0,
                    (canvas.width * canvas.height / 10000) +
                    (dataURL.length / 10000) -
                    (duration / 100)
                ));

                return {
                    width: canvas.width,
                    height: canvas.height,
                    dataSize: dataURL.length,
                    duration: duration,
                    score: Math.round(score)
                };

            } catch (error) {
                return {
                    width: 0,
                    height: 0,
                    dataSize: 0,
                    duration: 9999,
                    score: 0,
                    error: error.message
                };
            }
        }

        // Enhanced responsive handling
        function handleResize() {
            const designContainer = document.getElementById('condolenceDesign');
            const containerWidth = Math.min(window.innerWidth - 40, 500);

            if (window.innerWidth <= 768) {
                designContainer.style.width = containerWidth + 'px';
                designContainer.style.height = containerWidth + 'px';
            }
        }

        window.addEventListener('resize', handleResize);
        window.addEventListener('load', handleResize);
    </script>
</body>
</html>
