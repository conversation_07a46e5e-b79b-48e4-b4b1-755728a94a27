"""
Arabic text support for the Professional Image Editor
Handles RTL text rendering, Arabic fonts, and bidirectional text processing
"""

import os
from pathlib import Path
from typing import Optional, <PERSON>ple
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
from config import ARABIC_FONTS, FONTS_DIR

class ArabicTextRenderer:
    """Handle Arabic text rendering with proper RTL support"""
    
    def __init__(self):
        self.available_fonts = self._check_available_fonts()
        self.default_font = self._get_default_font()
    
    def _check_available_fonts(self) -> dict:
        """Check which Arabic fonts are available"""
        available = {}
        for font_name, font_path in ARABIC_FONTS.items():
            if os.path.exists(font_path):
                available[font_name] = font_path
            else:
                # Try to find font in system
                system_path = self._find_system_font(font_name)
                if system_path:
                    available[font_name] = system_path
        return available
    
    def _find_system_font(self, font_name: str) -> Optional[str]:
        """Try to find font in system directories"""
        system_font_dirs = [
            "C:/Windows/Fonts/",
            "/System/Library/Fonts/",
            "/usr/share/fonts/",
            "/usr/local/share/fonts/"
        ]
        
        font_files = {
            'amiri': ['Amiri-Regular.ttf', 'amiri-regular.ttf'],
            'scheherazade': ['Scheherazade-Regular.ttf', 'scheherazade-regular.ttf'],
            'noto_arabic': ['NotoSansArabic-Regular.ttf', 'noto-sans-arabic-regular.ttf']
        }
        
        for directory in system_font_dirs:
            if os.path.exists(directory):
                for filename in font_files.get(font_name, []):
                    font_path = os.path.join(directory, filename)
                    if os.path.exists(font_path):
                        return font_path
        return None
    
    def _get_default_font(self) -> str:
        """Get the best available Arabic font"""
        priority_order = ['amiri', 'noto_arabic', 'scheherazade']
        for font_name in priority_order:
            if font_name in self.available_fonts:
                return self.available_fonts[font_name]
        
        # Fallback to system default
        return "arial.ttf"
    
    def process_arabic_text(self, text: str) -> str:
        """Process Arabic text for proper RTL display"""
        try:
            # Reshape Arabic text (connect letters properly)
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except Exception as e:
            print(f"Error processing Arabic text: {e}")
            return text
    
    def get_font(self, font_name: str = None, size: int = 20) -> ImageFont.FreeTypeFont:
        """Get PIL font object for Arabic text"""
        if font_name and font_name in self.available_fonts:
            font_path = self.available_fonts[font_name]
        else:
            font_path = self.default_font
        
        try:
            return ImageFont.truetype(font_path, size)
        except Exception:
            # Fallback to default font
            try:
                return ImageFont.truetype("arial.ttf", size)
            except Exception:
                return ImageFont.load_default()
    
    def get_text_size(self, text: str, font: ImageFont.FreeTypeFont) -> Tuple[int, int]:
        """Get text dimensions"""
        processed_text = self.process_arabic_text(text)
        
        # Create temporary image to measure text
        temp_img = Image.new('RGB', (1, 1))
        draw = ImageDraw.Draw(temp_img)
        
        try:
            # Use textbbox for newer Pillow versions
            bbox = draw.textbbox((0, 0), processed_text, font=font)
            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]
            return (width, height)
        except AttributeError:
            # Fallback for older Pillow versions
            return draw.textsize(processed_text, font=font)
    
    def draw_text(self, image: Image.Image, position: Tuple[int, int], text: str, 
                  font: ImageFont.FreeTypeFont, fill: str = 'black', 
                  align: str = 'right') -> Image.Image:
        """Draw Arabic text on image with proper RTL alignment"""
        draw = ImageDraw.Draw(image)
        processed_text = self.process_arabic_text(text)
        
        # Adjust position for RTL alignment
        if align == 'right':
            text_width, _ = self.get_text_size(text, font)
            position = (position[0] - text_width, position[1])
        elif align == 'center':
            text_width, _ = self.get_text_size(text, font)
            position = (position[0] - text_width // 2, position[1])
        
        draw.text(position, processed_text, font=font, fill=fill)
        return image
    
    def create_text_image(self, text: str, font_size: int = 20, 
                         font_name: str = None, text_color: str = 'black',
                         bg_color: str = 'white', padding: int = 10) -> Image.Image:
        """Create an image with Arabic text"""
        font = self.get_font(font_name, font_size)
        text_width, text_height = self.get_text_size(text, font)
        
        # Create image with padding
        img_width = text_width + (padding * 2)
        img_height = text_height + (padding * 2)
        
        image = Image.new('RGB', (img_width, img_height), bg_color)
        
        # Draw text
        position = (img_width - padding, padding)  # RTL positioning
        self.draw_text(image, position, text, font, text_color, align='right')
        
        return image

class BilingualTextHandler:
    """Handle mixed Arabic and English text"""
    
    def __init__(self):
        self.arabic_renderer = ArabicTextRenderer()
    
    def detect_text_direction(self, text: str) -> str:
        """Detect if text is primarily Arabic (RTL) or English (LTR)"""
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        total_chars = len([char for char in text if char.isalpha()])
        
        if total_chars == 0:
            return 'ltr'
        
        arabic_ratio = arabic_chars / total_chars
        return 'rtl' if arabic_ratio > 0.5 else 'ltr'
    
    def process_mixed_text(self, text: str) -> str:
        """Process text that may contain both Arabic and English"""
        direction = self.detect_text_direction(text)
        
        if direction == 'rtl':
            return self.arabic_renderer.process_arabic_text(text)
        else:
            return text
    
    def get_appropriate_font(self, text: str, size: int = 20) -> ImageFont.FreeTypeFont:
        """Get appropriate font based on text content"""
        direction = self.detect_text_direction(text)
        
        if direction == 'rtl':
            return self.arabic_renderer.get_font(size=size)
        else:
            # Use system default for English text
            try:
                return ImageFont.truetype("arial.ttf", size)
            except Exception:
                return ImageFont.load_default()

class ArabicUIHelper:
    """Helper functions for Arabic UI elements"""
    
    @staticmethod
    def get_rtl_alignment() -> str:
        """Get appropriate text alignment for RTL languages"""
        return 'right'
    
    @staticmethod
    def reverse_layout_order(widgets: list) -> list:
        """Reverse widget order for RTL layout"""
        return widgets[::-1]
    
    @staticmethod
    def get_arabic_number(number: int) -> str:
        """Convert number to Arabic-Indic numerals"""
        arabic_digits = '٠١٢٣٤٥٦٧٨٩'
        english_digits = '0123456789'
        
        number_str = str(number)
        arabic_number = ''
        
        for digit in number_str:
            if digit in english_digits:
                arabic_number += arabic_digits[int(digit)]
            else:
                arabic_number += digit
        
        return arabic_number
    
    @staticmethod
    def format_arabic_date(date_obj) -> str:
        """Format date in Arabic style"""
        # This is a simplified version - you might want to use a proper Arabic calendar
        months_arabic = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]
        
        day = ArabicUIHelper.get_arabic_number(date_obj.day)
        month = months_arabic[date_obj.month - 1]
        year = ArabicUIHelper.get_arabic_number(date_obj.year)
        
        return f"{day} {month} {year}"

# Global instance for easy access
arabic_text_renderer = ArabicTextRenderer()
bilingual_handler = BilingualTextHandler()
arabic_ui_helper = ArabicUIHelper()
