#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول بيانات Excel شامل لقادة الأعمال الذين زاروا السعودية
خلال القمة العربية الأمريكية - مايو 2025

Creating comprehensive Excel spreadsheet for business leaders who visited Saudi Arabia
during the Arab-American Summit - May 2025
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime
import os

def create_business_leaders_spreadsheet():
    """إنشاء جدول بيانات Excel لقادة الأعمال في القمة العربية الأمريكية"""
    
    # بيانات قادة الأعمال الذين حضروا القمة
    business_leaders_data = [
        {
            'الاسم': 'إيلون ماسك',
            'الاسم_الإنجليزي': 'Elon Musk',
            'الشركة/المؤسسة': 'تيسلا وسبيس إكس',
            'الشركة_الإنجليزية': 'Tesla & SpaceX',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'التكنولوجيا والسيارات الكهربائية والفضاء',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي والذكاء الاصطناعي',
            'تفاصيل_اللقاءات': 'غداء رسمي مع ولي العهد محمد بن سلمان والرئيس ترامب',
            'المصدر': 'نيويورك تايمز، بيزنس إنسايدر'
        },
        {
            'الاسم': 'سام ألتمان',
            'الاسم_الإنجليزي': 'Sam Altman',
            'الشركة/المؤسسة': 'أوبن إيه آي',
            'الشركة_الإنجليزية': 'OpenAI',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'الذكاء الاصطناعي والتكنولوجيا',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي والذكاء الاصطناعي',
            'تفاصيل_اللقاءات': 'غداء رسمي مع القيادة السعودية والأمريكية',
            'المصدر': 'نيويورك تايمز، فوكس بيزنس'
        },
        {
            'الاسم': 'لاري فينك',
            'الاسم_الإنجليزي': 'Larry Fink',
            'الشركة/المؤسسة': 'بلاك روك',
            'الشركة_الإنجليزية': 'BlackRock',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'إدارة الأصول والاستثمار',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'متحدث في المنتدى وحضور الغداء الرسمي',
            'المصدر': 'بيزنس إنسايدر، رويترز'
        },
        {
            'الاسم': 'ستيفن شوارزمان',
            'الاسم_الإنجليزي': 'Stephen Schwarzman',
            'الشركة/المؤسسة': 'بلاك ستون',
            'الشركة_الإنجليزية': 'Blackstone Group',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'الاستثمار الخاص والعقارات',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'متحدث رئيسي في المنتدى وحضور الغداء الرسمي',
            'المصدر': 'نيويورك تايمز، بوليتيكو'
        },
        {
            'الاسم': 'جنسن هوانغ',
            'الاسم_الإنجليزي': 'Jensen Huang',
            'الشركة/المؤسسة': 'إنفيديا',
            'الشركة_الإنجليزية': 'Nvidia',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'أشباه الموصلات والذكاء الاصطناعي',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي والذكاء الاصطناعي',
            'تفاصيل_اللقاءات': 'متحدث في المنتدى ومناقشات حول رقائق الذكاء الاصطناعي',
            'المصدر': 'بيزنس إنسايدر، سي إن بي سي'
        },
        {
            'الاسم': 'آندي جاسي',
            'الاسم_الإنجليزي': 'Andy Jassy',
            'الشركة/المؤسسة': 'أمازون',
            'الشركة_الإنجليزية': 'Amazon',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'التجارة الإلكترونية والحوسبة السحابية',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي والذكاء الاصطناعي',
            'تفاصيل_اللقاءات': 'متحدث رئيسي ومناقشات حول استثمارات أمازون في السعودية',
            'المصدر': 'بيزنس إنسايدر، نيويورك تايمز'
        },
        {
            'الاسم': 'روث بورات',
            'الاسم_الإنجليزي': 'Ruth Porat',
            'الشركة/المؤسسة': 'ألفابت وجوجل',
            'الشركة_الإنجليزية': 'Alphabet & Google',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'التكنولوجيا والذكاء الاصطناعي',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي والذكاء الاصطناعي',
            'تفاصيل_اللقاءات': 'متحدثة في المنتدى ومناقشات حول مركز الذكاء الاصطناعي',
            'المصدر': 'بيزنس إنسايدر، نيويورك تايمز'
        },
        {
            'الاسم': 'جين فريزر',
            'الاسم_الإنجليزي': 'Jane Fraser',
            'الشركة/المؤسسة': 'سيتي جروب',
            'الشركة_الإنجليزية': 'Citigroup',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'الخدمات المصرفية والمالية',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'متحدثة في المنتدى وحضور الغداء الرسمي',
            'المصدر': 'بيزنس إنسايدر، نيويورك تايمز'
        },
        {
            'الاسم': 'أليكس كارب',
            'الاسم_الإنجليزي': 'Alex Karp',
            'الشركة/المؤسسة': 'بالانتير',
            'الشركة_الإنجليزية': 'Palantir',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'تحليل البيانات والأمن السيبراني',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'متحدث في المنتدى ومناقشات حول التكنولوجيا الدفاعية',
            'المصدر': 'بيزنس إنسايدر'
        },
        {
            'الاسم': 'أرفيند كريشنا',
            'الاسم_الإنجليزي': 'Arvind Krishna',
            'الشركة/المؤسسة': 'آي بي إم',
            'الشركة_الإنجليزية': 'IBM',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'التكنولوجيا والذكاء الاصطناعي',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي والذكاء الاصطناعي',
            'تفاصيل_اللقاءات': 'متحدث في المنتدى ومناقشات حول توسع آي بي إم في السعودية',
            'المصدر': 'بيزنس إنسايدر، نيويورك تايمز'
        },
        {
            'الاسم': 'كيلي أورتبرغ',
            'الاسم_الإنجليزي': 'Kelly Ortberg',
            'الشركة/المؤسسة': 'بوينغ',
            'الشركة_الإنجليزية': 'Boeing',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'الطيران والدفاع',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'متحدث في المنتدى ومناقشات حول صناعة الطيران',
            'المصدر': 'بيزنس إنسايدر، نيويورك تايمز'
        },
        {
            'الاسم': 'جيمس كوينسي',
            'الاسم_الإنجليزي': 'James Quincey',
            'الشركة/المؤسسة': 'كوكا كولا',
            'الشركة_الإنجليزية': 'Coca-Cola',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'المشروبات والسلع الاستهلاكية',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'حضور الغداء الرسمي ومناقشات تجارية',
            'المصدر': 'نيويورك تايمز'
        },
        {
            'الاسم': 'دارا خسروشاهي',
            'الاسم_الإنجليزي': 'Dara Khosrowshahi',
            'الشركة/المؤسسة': 'أوبر',
            'الشركة_الإنجليزية': 'Uber',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'النقل والتكنولوجيا',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'حضور الغداء الرسمي ومناقشات حول النقل الذكي',
            'المصدر': 'نيويورك تايمز'
        },
        {
            'الاسم': 'جيف ميلر',
            'الاسم_الإنجليزي': 'Jeff Miller',
            'الشركة/المؤسسة': 'هاليبرتون',
            'الشركة_الإنجليزية': 'Halliburton',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'خدمات النفط والغاز',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'حضور الغداء الرسمي ومناقشات حول قطاع الطاقة',
            'المصدر': 'نيويورك تايمز'
        },
        {
            'الاسم': 'كاثي واردن',
            'الاسم_الإنجليزي': 'Kathy Warden',
            'الشركة/المؤسسة': 'نورثروب جرومان',
            'الشركة_الإنجليزية': 'Northrop Grumman',
            'بلد_المنشأ': 'الولايات المتحدة الأمريكية',
            'القطاع': 'الدفاع والطيران',
            'تاريخ_الزيارة': '13 مايو 2025',
            'غرض_الزيارة': 'منتدى الاستثمار السعودي الأمريكي',
            'تفاصيل_اللقاءات': 'حضور الغداء الرسمي ومناقشات حول التكنولوجيا الدفاعية',
            'المصدر': 'نيويورك تايمز'
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(business_leaders_data)
    
    # إنشاء ملف Excel
    wb = Workbook()
    ws = wb.active
    ws.title = "قادة الأعمال - القمة العربية الأمريكية"
    
    # تعيين اتجاه النص من اليمين إلى اليسار
    ws.sheet_view.rightToLeft = True
    
    # إعداد الخطوط والألوان
    arabic_font = Font(name='Amiri', size=12, bold=False)
    header_font = Font(name='Amiri', size=14, bold=True, color='FFFFFF')
    title_font = Font(name='Amiri', size=18, bold=True, color='1F4E79')
    
    # ألوان التنسيق
    header_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
    alt_row_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
    
    # حدود الجدول
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # إضافة العنوان الرئيسي
    ws.merge_cells('A1:J3')
    title_cell = ws['A1']
    title_cell.value = 'قادة الأعمال الذين زاروا المملكة العربية السعودية\nخلال منتدى الاستثمار السعودي الأمريكي\n13 مايو 2025'
    title_cell.font = title_font
    title_cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # إضافة معلومات إضافية
    ws.merge_cells('A4:J4')
    info_cell = ws['A4']
    info_cell.value = f'تاريخ إعداد التقرير: {datetime.now().strftime("%d/%m/%Y")} - المصادر: نيويورك تايمز، بيزنس إنسايدر، رويترز، فوكس بيزنس'
    info_cell.font = Font(name='Amiri', size=10, italic=True)
    info_cell.alignment = Alignment(horizontal='center', vertical='center')
    
    # رؤوس الأعمدة
    headers = [
        'الاسم',
        'الشركة/المؤسسة', 
        'بلد المنشأ',
        'القطاع',
        'تاريخ الزيارة',
        'غرض الزيارة',
        'تفاصيل اللقاءات',
        'المصدر'
    ]
    
    # إضافة رؤوس الأعمدة
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=6, column=col)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.border = thin_border
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # إضافة البيانات
    for row_idx, leader in enumerate(business_leaders_data, 7):
        row_data = [
            leader['الاسم'],
            leader['الشركة/المؤسسة'],
            leader['بلد_المنشأ'],
            leader['القطاع'],
            leader['تاريخ_الزيارة'],
            leader['غرض_الزيارة'],
            leader['تفاصيل_اللقاءات'],
            leader['المصدر']
        ]
        
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx)
            cell.value = value
            cell.font = arabic_font
            cell.border = thin_border
            cell.alignment = Alignment(horizontal='right', vertical='top', wrap_text=True)
            
            # تلوين الصفوف المتناوبة
            if row_idx % 2 == 0:
                cell.fill = alt_row_fill
    
    # تعديل عرض الأعمدة
    column_widths = [25, 30, 20, 35, 15, 40, 50, 25]
    for col_idx, width in enumerate(column_widths, 1):
        ws.column_dimensions[ws.cell(row=1, column=col_idx).column_letter].width = width
    
    # تعديل ارتفاع الصفوف
    for row in range(6, len(business_leaders_data) + 7):
        ws.row_dimensions[row].height = 60
    
    # إضافة ملخص إحصائي
    summary_row = len(business_leaders_data) + 8
    ws.merge_cells(f'A{summary_row}:H{summary_row}')
    summary_cell = ws[f'A{summary_row}']
    summary_cell.value = f'إجمالي عدد قادة الأعمال: {len(business_leaders_data)} | القطاعات الرئيسية: التكنولوجيا والذكاء الاصطناعي، الخدمات المالية، الطيران والدفاع، الطاقة'
    summary_cell.font = Font(name='Amiri', size=12, bold=True, color='1F4E79')
    summary_cell.alignment = Alignment(horizontal='center', vertical='center')
    
    # حفظ الملف
    filename = 'قادة_الأعمال_القمة_العربية_الأمريكية_السعودية_2025.xlsx'
    wb.save(filename)
    
    print(f"✅ تم إنشاء ملف Excel بنجاح: {filename}")
    print(f"📊 عدد قادة الأعمال المدرجين: {len(business_leaders_data)}")
    print("🔍 الملف يتضمن:")
    print("   - تنسيق RTL للنصوص العربية")
    print("   - خطوط عربية عالية الجودة (Amiri)")
    print("   - تنسيق مهني مع حدود وألوان متناوبة")
    print("   - معلومات شاملة عن كل قائد أعمال")
    print("   - مصادر موثوقة للتحقق")
    
    return filename

if __name__ == "__main__":
    create_business_leaders_spreadsheet()
