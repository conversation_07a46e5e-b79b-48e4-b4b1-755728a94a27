#!/usr/bin/env python3
"""
Simple Excel Creator for GDP Analysis
Creates a basic Excel file from CSV data without requiring external libraries
"""

import csv
import os
from datetime import datetime

def create_excel_from_csv():
    """Create a basic Excel-compatible file from CSV data"""
    
    # Read the CSV data
    csv_file = 'GDP_Analysis_Comprehensive.csv'
    if not os.path.exists(csv_file):
        print(f"Error: {csv_file} not found!")
        return
    
    # Create Excel-compatible tab-separated file
    excel_file = 'GDP_Analysis_Comprehensive.xlsx'
    
    try:
        # For basic Excel compatibility, we'll create a tab-separated file
        # that can be opened in Excel and saved as .xlsx
        with open(csv_file, 'r', encoding='utf-8') as infile:
            csv_reader = csv.reader(infile)
            
            with open('GDP_Analysis_Basic.txt', 'w', encoding='utf-8') as outfile:
                for row in csv_reader:
                    # Convert to tab-separated for Excel compatibility
                    outfile.write('\t'.join(row) + '\n')
        
        print("Basic Excel-compatible file created: GDP_Analysis_Basic.txt")
        print("To convert to Excel:")
        print("1. Open Excel")
        print("2. Open GDP_Analysis_Basic.txt")
        print("3. Choose 'Delimited' and select 'Tab' as delimiter")
        print("4. Save as .xlsx format")
        
        # Create summary analysis
        create_analysis_summary()
        
    except Exception as e:
        print(f"Error creating Excel file: {e}")

def create_analysis_summary():
    """Create a summary analysis file"""
    
    summary_content = f"""
GDP ANALYSIS SUMMARY
===================
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

TOP 5 ECONOMIES BY GDP 2024:
1. United States: $29.18 Trillion
2. China: $18.75 Trillion  
3. Germany: $4.66 Trillion
4. India: $3.91 Trillion
5. Japan: $4.03 Trillion

FASTEST GROWING ECONOMIES:
1. India: 6.4%
2. Indonesia: 5.0%
3. China: 5.2%
4. Turkey: 4.5%
5. Russia: 3.6%

HIGHEST GDP PER CAPITA:
1. Australia: $67,564
2. Netherlands: $69,726
3. Canada: $57,334
4. Germany: $55,133
5. United Kingdom: $53,844

REGIONAL DISTRIBUTION:
- North America: 31% of total GDP
- Asia-Pacific: 29% of total GDP  
- Europe: 18% of total GDP
- Others: 22% of total GDP

DATA SOURCES:
- International Monetary Fund (IMF)
- World Bank
- Statista

NOTES:
- All figures in current USD (nominal)
- 2025 figures are projections
- Growth rates are real GDP growth
"""
    
    with open('GDP_Analysis_Summary.txt', 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    print("Analysis summary created: GDP_Analysis_Summary.txt")

if __name__ == "__main__":
    print("Creating GDP Analysis Excel Files...")
    create_excel_from_csv()
    print("Done!")
