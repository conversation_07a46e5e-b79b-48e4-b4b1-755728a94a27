<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعزية العالم الإسلامي - استشهاد الإمام محمد الجواد (عليه السلام)</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- html2canvas Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>', 'Noto Sans Arabic', serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #f5f5f5;
            direction: rtl;
            text-align: right;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .condolence-card {
            max-width: 600px;
            background: rgba(0, 0, 0, 0.9);
            border: 3px solid #d4af37;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow: hidden;
        }

        .brand-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .brand-logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #d4af37;
        }

        .brand-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #d4af37;
        }

        .opening-verse {
            font-size: 1.3rem;
            color: #90ee90;
            font-weight: 600;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(144, 238, 144, 0.3);
        }

        .main-title {
            font-size: 2.5rem;
            color: #d4af37;
            margin-bottom: 1rem;
        }

        .imam-name {
            font-size: 3rem;
            color: #d4af37;
            font-weight: 700;
            margin: 1rem 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
        }

        .honorific {
            display: block;
            font-size: 1.5rem;
            color: #90ee90;
            margin-top: 0.5rem;
        }

        .condolence-text {
            font-size: 1.2rem;
            line-height: 2;
            margin: 2rem 0;
            text-align: justify;
        }

        .date-martyrdom {
            font-size: 1.4rem;
            color: #d4af37;
            font-weight: 600;
            margin: 2rem 0;
            padding: 1rem;
            background: rgba(212, 175, 55, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(212, 175, 55, 0.3);
        }

        .closing-prayer {
            font-size: 1.1rem;
            color: #90ee90;
            font-weight: 600;
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(144, 238, 144, 0.3);
        }

        .footer-brand {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
            font-size: 0.9rem;
            color: #999;
        }

        .download-section {
            margin-top: 2rem;
            text-align: center;
        }

        .download-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .download-btn {
            background: linear-gradient(90deg, #d4af37, #b8941f);
            color: #000;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
        }

        .download-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 10px;
            display: none;
        }

        .success {
            background: rgba(144, 238, 144, 0.2);
            border: 2px solid rgba(144, 238, 144, 0.5);
            color: #90ee90;
        }

        .error {
            background: rgba(255, 100, 100, 0.2);
            border: 2px solid rgba(255, 100, 100, 0.5);
            color: #ff6464;
        }

        @media (max-width: 768px) {
            .condolence-card {
                padding: 2rem;
                margin: 1rem;
            }
            
            .main-title {
                font-size: 2rem;
            }
            
            .imam-name {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="condolence-card" id="condolenceCard">
        <!-- Brand Header -->
        <div class="brand-header">
            <div class="brand-logo"></div>
            <span class="brand-name">AliToucan</span>
        </div>

        <!-- Opening Quranic Verse -->
        <div class="opening-verse">
            "وَبَشِّرِ الصَّابِرِينَ * الَّذِينَ إِذَا أَصَابَتْهُم مُّصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ"
        </div>

        <!-- Main Title -->
        <h1 class="main-title">تعزية العالم الإسلامي</h1>

        <!-- Imam Name -->
        <h2 class="imam-name">
            الإمام محمد الجواد
            <span class="honorific">(عليه السلام)</span>
        </h2>

        <!-- Condolence Message -->
        <div class="condolence-text">
            بقلوب مؤمنة بقضاء الله وقدره، نتقدم إلى العالم الإسلامي وجميع المؤمنين في مشارق الأرض ومغاربها 
            بأحر التعازي وأصدق المواساة بمناسبة ذكرى استشهاد الإمام التاسع من أئمة أهل البيت الأطهار، 
            الإمام أبو جعفر محمد بن علي الجواد (عليه السلام)، الذي استشهد مسموماً وهو في ريعان شبابه، 
            تاركاً للأمة إرثاً عظيماً من العلم والحكمة والتقوى.
        </div>

        <!-- Martyrdom Date -->
        <div class="date-martyrdom">
            ٢٩ ذو القعدة الحرام - ذكرى الاستشهاد
        </div>

        <!-- Closing Prayer -->
        <div class="closing-prayer">
            اللهم صل على محمد وآل محمد، وعجل فرج وليك الحجة بن الحسن، 
            واجعلنا من أنصاره وأعوانه والذابين عنه
        </div>

        <!-- Footer -->
        <div class="footer-brand">
            © ٢٠٢٤ AliToucan - في ذكرى الإمام الجواد (عليه السلام)
        </div>

        <!-- Download Section -->
        <div class="download-section" id="downloadSection">
            <h3 style="color: #d4af37; margin-bottom: 1rem;">تحميل الصورة للمشاركة</h3>
            <div class="download-buttons">
                <button class="download-btn" onclick="downloadImageFixed('square')">
                    📱 مربع (1080x1080)
                </button>
                <button class="download-btn" onclick="downloadImageFixed('story')">
                    📖 ستوري (1080x1920)
                </button>
                <button class="download-btn" onclick="downloadImageFixed('simple')">
                    📷 تحميل بسيط
                </button>
            </div>
            
            <div class="status" id="status"></div>
        </div>
    </div>

    <script>
        let isDownloading = false;

        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (isError ? 'error' : 'success');
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        function toggleButtons(disabled) {
            const buttons = document.querySelectorAll('.download-btn');
            buttons.forEach(btn => {
                btn.disabled = disabled;
            });
        }

        async function downloadImageFixed(format) {
            if (isDownloading) return;
            
            isDownloading = true;
            toggleButtons(true);
            
            try {
                showStatus('جاري إنشاء الصورة...');
                
                const element = document.getElementById('condolenceCard');
                const downloadSection = document.getElementById('downloadSection');
                
                // Hide download section temporarily
                downloadSection.style.display = 'none';
                
                // Wait a moment for the DOM to update
                await new Promise(resolve => setTimeout(resolve, 100));
                
                let canvas;
                
                if (format === 'simple') {
                    // Simple download
                    canvas = await html2canvas(element, {
                        scale: 2,
                        backgroundColor: '#0a0a0a',
                        allowTaint: true,
                        useCORS: true,
                        logging: false
                    });
                    
                    downloadCanvas(canvas, 'تعزية_الإمام_الجواد_بسيط.png');
                    
                } else {
                    // Advanced download with resizing
                    canvas = await html2canvas(element, {
                        scale: 2,
                        backgroundColor: '#0a0a0a',
                        allowTaint: true,
                        useCORS: true,
                        logging: false
                    });
                    
                    const dimensions = format === 'square' ? [1080, 1080] : [1080, 1920];
                    const resizedCanvas = resizeCanvas(canvas, dimensions[0], dimensions[1]);
                    const filename = `تعزية_الإمام_الجواد_${format}.png`;
                    
                    downloadCanvas(resizedCanvas, filename);
                }
                
                // Show download section again
                downloadSection.style.display = 'block';
                showStatus('تم تحميل الصورة بنجاح! ✅');
                
            } catch (error) {
                console.error('Download error:', error);
                showStatus('حدث خطأ في تحميل الصورة: ' + error.message, true);
                
                // Show download section again
                document.getElementById('downloadSection').style.display = 'block';
            } finally {
                toggleButtons(false);
                isDownloading = false;
            }
        }

        function resizeCanvas(originalCanvas, width, height) {
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            // Fill background with gradient
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#0a0a0a');
            gradient.addColorStop(0.5, '#1a1a2e');
            gradient.addColorStop(1, '#16213e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // Calculate scaling
            const scale = Math.min(width / originalCanvas.width, height / originalCanvas.height) * 0.9;
            const scaledWidth = originalCanvas.width * scale;
            const scaledHeight = originalCanvas.height * scale;
            const x = (width - scaledWidth) / 2;
            const y = (height - scaledHeight) / 2;
            
            // Draw the original canvas
            ctx.drawImage(originalCanvas, x, y, scaledWidth, scaledHeight);
            
            return canvas;
        }

        function downloadCanvas(canvas, filename) {
            canvas.toBlob(function(blob) {
                if (!blob) {
                    throw new Error('فشل في إنشاء الصورة');
                }
                
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // Clean up
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 1000);
                
            }, 'image/png', 0.95);
        }
    </script>
</body>
</html>
