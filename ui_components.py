"""
Custom UI components for the Professional Image Editor
Contains reusable widgets, dialogs, and interface elements
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
from typing import Callable, Optional, List, Dict, Any, Tuple
from PIL import Image, ImageTk
import os
from config import COLORS, SOCIAL_MEDIA_PRESETS, SHORTCUTS
from utils import UIUtils, SettingsManager

class ColorPicker(tk.Frame):
    """Custom color picker widget"""
    
    def __init__(self, parent, initial_color: str = '#000000', callback: Callable = None):
        super().__init__(parent)
        self.callback = callback
        self.current_color = initial_color
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup color picker UI"""
        # Color display button
        self.color_button = tk.Button(
            self, 
            bg=self.current_color,
            width=4,
            height=2,
            command=self.choose_color,
            relief='raised',
            bd=2
        )
        self.color_button.pack(side=tk.LEFT, padx=2)
        
        # Color value label
        self.color_label = tk.Label(self, text=self.current_color, font=('Arial', 8))
        self.color_label.pack(side=tk.LEFT, padx=5)
    
    def choose_color(self):
        """Open color chooser dialog"""
        color = colorchooser.askcolor(initialcolor=self.current_color)
        if color[1]:  # If user didn't cancel
            self.set_color(color[1])
    
    def set_color(self, color: str):
        """Set the current color"""
        self.current_color = color
        self.color_button.config(bg=color)
        self.color_label.config(text=color)
        
        if self.callback:
            self.callback(color)
    
    def get_color(self) -> str:
        """Get current color"""
        return self.current_color

class SliderControl(tk.Frame):
    """Custom slider with label and value display"""
    
    def __init__(self, parent, label: str, min_val: float, max_val: float, 
                 initial_val: float, callback: Callable = None, precision: int = 2):
        super().__init__(parent)
        self.callback = callback
        self.precision = precision
        
        self.setup_ui(label, min_val, max_val, initial_val)
    
    def setup_ui(self, label: str, min_val: float, max_val: float, initial_val: float):
        """Setup slider UI"""
        # Label
        self.label = tk.Label(self, text=label, font=('Arial', 9))
        self.label.pack(anchor='w')
        
        # Frame for slider and value
        control_frame = tk.Frame(self)
        control_frame.pack(fill='x', pady=2)
        
        # Slider
        self.var = tk.DoubleVar(value=initial_val)
        self.slider = tk.Scale(
            control_frame,
            from_=min_val,
            to=max_val,
            orient='horizontal',
            variable=self.var,
            command=self.on_change,
            resolution=10**(-self.precision),
            length=200
        )
        self.slider.pack(side=tk.LEFT, fill='x', expand=True)
        
        # Value display
        self.value_label = tk.Label(control_frame, text=f"{initial_val:.{self.precision}f}", 
                                   width=8, font=('Arial', 8))
        self.value_label.pack(side=tk.RIGHT, padx=5)
    
    def on_change(self, value):
        """Handle slider value change"""
        val = float(value)
        self.value_label.config(text=f"{val:.{self.precision}f}")
        
        if self.callback:
            self.callback(val)
    
    def get_value(self) -> float:
        """Get current slider value"""
        return self.var.get()
    
    def set_value(self, value: float):
        """Set slider value"""
        self.var.set(value)

class ImagePreview(tk.Frame):
    """Image preview widget with zoom and pan"""
    
    def __init__(self, parent, width: int = 400, height: int = 300):
        super().__init__(parent)
        self.canvas_width = width
        self.canvas_height = height
        self.image = None
        self.photo_image = None
        self.zoom_factor = 1.0
        self.pan_x = 0
        self.pan_y = 0
        
        self.setup_ui()
        self.bind_events()
    
    def setup_ui(self):
        """Setup preview UI"""
        # Canvas with scrollbars
        self.canvas = tk.Canvas(
            self,
            width=self.canvas_width,
            height=self.canvas_height,
            bg='white',
            relief='sunken',
            bd=2
        )
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(self, orient='vertical', command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(self, orient='horizontal', command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack widgets
        self.canvas.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Configure grid weights
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
    
    def bind_events(self):
        """Bind mouse events for pan and zoom"""
        self.canvas.bind('<Button-1>', self.start_pan)
        self.canvas.bind('<B1-Motion>', self.pan_image)
        self.canvas.bind('<MouseWheel>', self.zoom_image)
        self.canvas.bind('<Button-4>', self.zoom_image)  # Linux
        self.canvas.bind('<Button-5>', self.zoom_image)  # Linux
    
    def set_image(self, image: Image.Image):
        """Set image to display"""
        self.image = image
        self.zoom_factor = 1.0
        self.pan_x = 0
        self.pan_y = 0
        self.update_display()
    
    def update_display(self):
        """Update image display"""
        if self.image is None:
            return
        
        # Calculate display size
        display_width = int(self.image.width * self.zoom_factor)
        display_height = int(self.image.height * self.zoom_factor)
        
        # Resize image for display
        if self.zoom_factor != 1.0:
            display_image = self.image.resize((display_width, display_height), Image.Resampling.LANCZOS)
        else:
            display_image = self.image
        
        # Convert to PhotoImage
        self.photo_image = ImageTk.PhotoImage(display_image)
        
        # Clear canvas and add image
        self.canvas.delete('all')
        self.canvas.create_image(
            self.pan_x + display_width // 2,
            self.pan_y + display_height // 2,
            image=self.photo_image
        )
        
        # Update scroll region
        self.canvas.configure(scrollregion=self.canvas.bbox('all'))
    
    def start_pan(self, event):
        """Start panning operation"""
        self.canvas.scan_mark(event.x, event.y)
    
    def pan_image(self, event):
        """Pan image"""
        self.canvas.scan_dragto(event.x, event.y, gain=1)
    
    def zoom_image(self, event):
        """Zoom image"""
        if self.image is None:
            return
        
        # Determine zoom direction
        if event.delta > 0 or event.num == 4:
            zoom_factor = 1.1
        else:
            zoom_factor = 0.9
        
        # Apply zoom
        new_zoom = self.zoom_factor * zoom_factor
        new_zoom = max(0.1, min(5.0, new_zoom))  # Limit zoom range
        
        if new_zoom != self.zoom_factor:
            self.zoom_factor = new_zoom
            self.update_display()
    
    def fit_to_window(self):
        """Fit image to window"""
        if self.image is None:
            return
        
        # Calculate zoom to fit
        zoom_x = self.canvas_width / self.image.width
        zoom_y = self.canvas_height / self.image.height
        self.zoom_factor = min(zoom_x, zoom_y, 1.0)  # Don't zoom in beyond 100%
        
        self.pan_x = 0
        self.pan_y = 0
        self.update_display()
    
    def zoom_to_actual_size(self):
        """Zoom to actual size (100%)"""
        self.zoom_factor = 1.0
        self.pan_x = 0
        self.pan_y = 0
        self.update_display()

class LayerPanel(tk.Frame):
    """Layer management panel"""
    
    def __init__(self, parent, layer_callback: Callable = None):
        super().__init__(parent)
        self.layer_callback = layer_callback
        self.layers_data = []
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup layer panel UI"""
        # Title
        title_label = tk.Label(self, text="Layers", font=('Arial', 10, 'bold'))
        title_label.pack(pady=5)
        
        # Layer list frame
        list_frame = tk.Frame(self)
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Listbox with scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side='right', fill='y')
        
        self.layer_listbox = tk.Listbox(
            list_frame,
            yscrollcommand=scrollbar.set,
            height=8,
            font=('Arial', 9)
        )
        self.layer_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.layer_listbox.yview)
        
        # Bind selection event
        self.layer_listbox.bind('<<ListboxSelect>>', self.on_layer_select)
        
        # Buttons frame
        buttons_frame = tk.Frame(self)
        buttons_frame.pack(fill='x', padx=5, pady=5)
        
        # Layer control buttons
        tk.Button(buttons_frame, text="Add", command=self.add_layer, width=8).pack(side='left', padx=2)
        tk.Button(buttons_frame, text="Delete", command=self.delete_layer, width=8).pack(side='left', padx=2)
        tk.Button(buttons_frame, text="Duplicate", command=self.duplicate_layer, width=8).pack(side='left', padx=2)
        
        # Opacity control
        opacity_frame = tk.Frame(self)
        opacity_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(opacity_frame, text="Opacity:", font=('Arial', 9)).pack(anchor='w')
        self.opacity_var = tk.DoubleVar(value=100)
        self.opacity_scale = tk.Scale(
            opacity_frame,
            from_=0,
            to=100,
            orient='horizontal',
            variable=self.opacity_var,
            command=self.on_opacity_change
        )
        self.opacity_scale.pack(fill='x')
    
    def update_layers(self, layers_info: List[Dict[str, Any]]):
        """Update layer list"""
        self.layers_data = layers_info
        
        # Clear listbox
        self.layer_listbox.delete(0, tk.END)
        
        # Add layers (reverse order for display)
        for layer_info in reversed(layers_info):
            name = layer_info['name']
            if not layer_info['visible']:
                name += " (hidden)"
            if layer_info['is_active']:
                name = f"► {name}"
            
            self.layer_listbox.insert(0, name)
    
    def on_layer_select(self, event):
        """Handle layer selection"""
        selection = self.layer_listbox.curselection()
        if selection and self.layer_callback:
            # Convert display index to actual layer index
            display_index = selection[0]
            actual_index = len(self.layers_data) - 1 - display_index
            
            if 0 <= actual_index < len(self.layers_data):
                layer_info = self.layers_data[actual_index]
                self.layer_callback('select', layer_info['id'])
                
                # Update opacity slider
                self.opacity_var.set(layer_info['opacity'] * 100)
    
    def on_opacity_change(self, value):
        """Handle opacity change"""
        if self.layer_callback:
            opacity = float(value) / 100.0
            self.layer_callback('opacity', opacity)
    
    def add_layer(self):
        """Add new layer"""
        if self.layer_callback:
            self.layer_callback('add', None)
    
    def delete_layer(self):
        """Delete selected layer"""
        if self.layer_callback:
            self.layer_callback('delete', None)
    
    def duplicate_layer(self):
        """Duplicate selected layer"""
        if self.layer_callback:
            self.layer_callback('duplicate', None)

class ProgressDialog(tk.Toplevel):
    """Progress dialog for long operations"""
    
    def __init__(self, parent, title: str = "Processing"):
        super().__init__(parent)
        self.title(title)
        self.transient(parent)
        self.grab_set()
        
        self.cancelled = False
        
        self.setup_ui()
        self.center_window()
    
    def setup_ui(self):
        """Setup progress dialog UI"""
        # Main frame
        main_frame = tk.Frame(self, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Status label
        self.status_label = tk.Label(main_frame, text="Initializing...", font=('Arial', 10))
        self.status_label.pack(pady=10)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            maximum=100,
            length=300
        )
        self.progress_bar.pack(pady=10)
        
        # Progress text
        self.progress_label = tk.Label(main_frame, text="0%", font=('Arial', 9))
        self.progress_label.pack()
        
        # Cancel button
        self.cancel_button = tk.Button(
            main_frame,
            text="Cancel",
            command=self.cancel,
            width=10
        )
        self.cancel_button.pack(pady=10)
    
    def update_progress(self, current: int, total: int, status: str = ""):
        """Update progress"""
        if total > 0:
            percentage = (current / total) * 100
            self.progress_var.set(percentage)
            self.progress_label.config(text=f"{current}/{total} ({percentage:.1f}%)")
        
        if status:
            self.status_label.config(text=status)
        
        self.update()
    
    def cancel(self):
        """Cancel operation"""
        self.cancelled = True
        self.destroy()
    
    def center_window(self):
        """Center dialog on parent"""
        self.geometry("400x200")
        self.update_idletasks()
        
        x = (self.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.winfo_screenheight() // 2) - (200 // 2)
        self.geometry(f"400x200+{x}+{y}")

class ToolPanel(tk.Frame):
    """Tool selection panel"""
    
    def __init__(self, parent, tool_callback: Callable = None):
        super().__init__(parent)
        self.tool_callback = tool_callback
        self.current_tool = None
        self.tool_buttons = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup tool panel UI"""
        # Title
        title_label = tk.Label(self, text="Tools", font=('Arial', 10, 'bold'))
        title_label.pack(pady=5)
        
        # Tool buttons
        tools = [
            ('Select', 'select'),
            ('Brush', 'brush'),
            ('Pencil', 'pencil'),
            ('Eraser', 'eraser'),
            ('Text', 'text'),
            ('Rectangle', 'rectangle'),
            ('Circle', 'circle'),
            ('Line', 'line'),
            ('Arrow', 'arrow')
        ]
        
        for i, (name, tool_id) in enumerate(tools):
            btn = tk.Button(
                self,
                text=name,
                command=lambda t=tool_id: self.select_tool(t),
                width=12,
                relief='raised'
            )
            btn.pack(pady=2, padx=5, fill='x')
            self.tool_buttons[tool_id] = btn
        
        # Select default tool
        self.select_tool('select')
    
    def select_tool(self, tool_id: str):
        """Select a tool"""
        # Update button states
        for tid, btn in self.tool_buttons.items():
            if tid == tool_id:
                btn.config(relief='sunken', bg='lightblue')
            else:
                btn.config(relief='raised', bg='SystemButtonFace')
        
        self.current_tool = tool_id
        
        if self.tool_callback:
            self.tool_callback(tool_id)
    
    def get_current_tool(self) -> str:
        """Get currently selected tool"""
        return self.current_tool
