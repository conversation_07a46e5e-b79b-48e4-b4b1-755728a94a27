<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بطاقة تعزية قابلة للطباعة - الإمام محمد الجواد (عليه السلام)</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON>i', 'Noto Sans Arabic', serif;
            background: #f5f5f5;
            direction: rtl;
            padding: 2rem;
            line-height: 1.6;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .card {
            width: 100%;
            min-height: 600px;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            position: relative;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .decorative-border {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 3px solid #d4af37;
            border-radius: 15px;
            pointer-events: none;
        }

        .decorative-border::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 20px;
        }

        .geometric-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.05;
            background-image: 
                radial-gradient(circle at 25% 25%, #d4af37 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, #90ee90 2px, transparent 2px);
            background-size: 60px 60px;
            background-position: 0 0, 30px 30px;
        }

        .header-section {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .brand-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .brand-logo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #d4af37;
        }

        .brand-name {
            font-size: 1.8rem;
            font-weight: 700;
            color: #d4af37;
        }

        .card-title {
            font-size: 2rem;
            color: #f5f5f5;
            margin-bottom: 1rem;
            font-weight: 400;
        }

        .main-content {
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .verse-section {
            background: rgba(144, 238, 144, 0.1);
            border: 2px solid rgba(144, 238, 144, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .verse-text {
            font-size: 1.4rem;
            color: #90ee90;
            font-weight: 600;
            line-height: 1.8;
        }

        .imam-section {
            margin: 2rem 0;
        }

        .imam-name {
            font-size: 3rem;
            color: #d4af37;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .honorific {
            font-size: 1.8rem;
            color: #90ee90;
            font-weight: 400;
        }

        .condolence-section {
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .condolence-text {
            font-size: 1.2rem;
            line-height: 2;
            text-align: justify;
            margin-bottom: 1.5rem;
        }

        .date-section {
            background: rgba(212, 175, 55, 0.2);
            border: 2px solid rgba(212, 175, 55, 0.5);
            border-radius: 25px;
            padding: 1rem 2rem;
            margin: 2rem 0;
            display: inline-block;
        }

        .date-text {
            font-size: 1.4rem;
            color: #d4af37;
            font-weight: 600;
        }

        .prayer-section {
            background: rgba(144, 238, 144, 0.1);
            border: 2px solid rgba(144, 238, 144, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .prayer-text {
            font-size: 1.1rem;
            color: #90ee90;
            font-weight: 600;
            line-height: 1.8;
        }

        .footer-section {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
            position: relative;
            z-index: 2;
        }

        .footer-text {
            font-size: 1rem;
            color: #999;
        }

        .print-instructions {
            background: #e8f4fd;
            color: #333;
            padding: 1rem;
            margin-bottom: 2rem;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #b3d9ff;
        }

        .print-button {
            background: #d4af37;
            color: #000;
            border: none;
            padding: 1rem 2rem;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 0 0.5rem;
            font-family: inherit;
            transition: background 0.3s ease;
        }

        .print-button:hover {
            background: #b8941f;
        }

        /* Print Styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .print-instructions {
                display: none;
            }
            
            .print-container {
                box-shadow: none;
                border-radius: 0;
                max-width: none;
                width: 100%;
            }
            
            .card {
                min-height: 100vh;
                page-break-inside: avoid;
                background: linear-gradient(135deg, #000 0%, #1a1a2e 50%, #16213e 100%) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .decorative-border {
                border-color: #d4af37 !important;
            }
            
            .verse-section,
            .condolence-section,
            .prayer-section {
                border-color: rgba(144, 238, 144, 0.5) !important;
                background: rgba(144, 238, 144, 0.1) !important;
            }
            
            .date-section {
                border-color: rgba(212, 175, 55, 0.7) !important;
                background: rgba(212, 175, 55, 0.2) !important;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .card {
                padding: 2rem;
            }
            
            .imam-name {
                font-size: 2.5rem;
            }
            
            .card-title {
                font-size: 1.5rem;
            }
            
            .verse-text {
                font-size: 1.2rem;
            }
            
            .condolence-text {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Print Instructions -->
    <div class="print-instructions">
        <h3>تعليمات الطباعة</h3>
        <p>للحصول على أفضل نتائج الطباعة، يُرجى استخدام الإعدادات التالية:</p>
        <p>• حجم الورق: A4 • الاتجاه: عمودي • الهوامش: صغيرة • طباعة الخلفيات: مفعل</p>
        <button class="print-button" onclick="window.print()">🖨️ طباعة البطاقة</button>
        <button class="print-button" onclick="downloadPDF()" style="background: #90ee90;">📄 حفظ كـ PDF</button>
    </div>

    <div class="print-container">
        <div class="card">
            <div class="decorative-border"></div>
            <div class="geometric-pattern"></div>
            
            <!-- Header -->
            <div class="header-section">
                <div class="brand-section">
                    <img src="app_icon.png" alt="AliToucan" class="brand-logo">
                    <span class="brand-name">AliToucan</span>
                </div>
                <h1 class="card-title">بطاقة تعزية للعالم الإسلامي</h1>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Quranic Verse -->
                <div class="verse-section">
                    <div class="verse-text">
                        "وَبَشِّرِ الصَّابِرِينَ * الَّذِينَ إِذَا أَصَابَتْهُم مُّصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ"
                    </div>
                </div>

                <!-- Imam Name -->
                <div class="imam-section">
                    <h2 class="imam-name">الإمام محمد الجواد</h2>
                    <p class="honorific">(عليه السلام)</p>
                </div>

                <!-- Condolence Message -->
                <div class="condolence-section">
                    <p class="condolence-text">
                        بقلوب مؤمنة بقضاء الله وقدره، نتقدم إلى العالم الإسلامي وجميع المؤمنين والمؤمنات 
                        في مشارق الأرض ومغاربها بأحر التعازي وأصدق المواساة بمناسبة ذكرى استشهاد 
                        الإمام التاسع من أئمة أهل البيت الأطهار، الذي استشهد مسموماً وهو في ريعان شبابه، 
                        تاركاً للأمة الإسلامية إرثاً عظيماً من العلم والحكمة والتقوى.
                    </p>
                </div>

                <!-- Date -->
                <div class="date-section">
                    <div class="date-text">٢٩ ذو القعدة الحرام - ذكرى الاستشهاد</div>
                </div>

                <!-- Prayer -->
                <div class="prayer-section">
                    <div class="prayer-text">
                        اللهم صل على محمد وآل محمد، وعجل فرج وليك الحجة بن الحسن، 
                        واجعلنا من أنصاره وأعوانه والذابين عنه، والمستشهدين بين يديه
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="footer-section">
                <p class="footer-text">© ٢٠٢٤ AliToucan - تم إعداد هذه البطاقة بكل احترام وتقدير لذكرى الإمام الجواد (عليه السلام)</p>
            </div>
        </div>
    </div>

    <script>
        function downloadPDF() {
            // Simple PDF generation using browser's print to PDF
            const originalTitle = document.title;
            document.title = 'بطاقة_تعزية_الإمام_الجواد_عليه_السلام';
            
            setTimeout(() => {
                window.print();
                document.title = originalTitle;
            }, 100);
        }

        // Optimize for printing
        window.addEventListener('beforeprint', function() {
            document.body.style.background = 'white';
        });

        window.addEventListener('afterprint', function() {
            document.body.style.background = '#f5f5f5';
        });
    </script>
</body>
</html>
