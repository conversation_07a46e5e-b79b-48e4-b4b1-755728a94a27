<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عيد الأضحى المبارك</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Tajawal', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f5dc, #f8f8e1);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }
        
        .moon {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 100px;
            background: #f8c537;
            border-radius: 50%;
            box-shadow: 0 0 30px #f8c537;
            z-index: 1;
        }
        
        .moon::after {
            content: '';
            position: absolute;
            width: 90px;
            height: 90px;
            background: #f5f5dc;
            border-radius: 50%;
            top: -10px;
            right: 15px;
        }
        
        .greeting {
            margin-top: 100px;
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .subtitle {
            font-size: 1.5rem;
            color: #7f8c8d;
            margin-bottom: 40px;
            max-width: 800px;
            line-height: 1.6;
        }
        
        .mosque {
            width: 200px;
            height: 150px;
            margin: 30px auto;
            position: relative;
            z-index: 2;
        }
        
        .mosque::before,
        .mosque::after {
            content: '';
            position: absolute;
            bottom: 0;
            background: #2c3e50;
        }
        
        .mosque::before {
            width: 120px;
            height: 100px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 50% 50% 0 0 / 30%;
        }
        
        .mosque::after {
            width: 200px;
            height: 60px;
            left: 0;
            border-radius: 5px 5px 0 0;
        }
        
        .minaret {
            position: absolute;
            width: 20px;
            height: 80px;
            background: #2c3e50;
            bottom: 60px;
            border-radius: 5px;
        }
        
        .minaret.left {
            left: 20px;
        }
        
        .minaret.right {
            right: 20px;
        }
        
        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: -1;
        }
        
        .lantern {
            position: absolute;
            width: 30px;
            height: 40px;
            background: #f8c537;
            border-radius: 50% 50% 5px 5px;
            animation: flicker 2s infinite alternate;
        }
        
        @keyframes flicker {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }
        
        .lantern::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 10px;
            background: #f8c537;
            top: -5px;
            left: 0;
            border-radius: 5px;
        }
        
        .lantern-1 { top: 30%; left: 20%; }
        .lantern-2 { top: 40%; right: 25%; }
        .lantern-3 { top: 70%; left: 30%; }
        .lantern-4 { top: 60%; right: 20%; }
        
        .eid-message {
            background: rgba(255, 255, 255, 0.8);
            padding: 30px;
            border-radius: 15px;
            max-width: 700px;
            margin: 20px auto;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .eid-message h2 {
            color: #c0392b;
            margin-bottom: 15px;
        }
        
        .eid-message p {
            color: #34495e;
            line-height: 1.8;
            margin-bottom: 15px;
        }
        
        .blessing {
            font-size: 1.8rem;
            color: #16a085;
            margin: 30px 0;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .greeting {
                font-size: 2rem;
                margin-top: 80px;
            }
            
            .subtitle {
                font-size: 1.2rem;
                padding: 0 20px;
            }
            
            .mosque {
                width: 150px;
                height: 120px;
            }
            
            .minaret {
                height: 60px;
                bottom: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="moon"></div>
    
    <div class="decoration">
        <div class="lantern lantern-1"></div>
        <div class="lantern lantern-2"></div>
        <div class="lantern lantern-3"></div>
        <div class="lantern lantern-4"></div>
    </div>
    
    <div class="mosque">
        <div class="minaret left"></div>
        <div class="minaret right"></div>
    </div>
    
    <h1 class="greeting">كل عام وأنتم بخير</h1>
    <p class="subtitle">بمناسبة عيد الأضحى المبارك، نتمنى لكم أياماً سعيدة مليئة بالخير والبركة</p>
    
    <div class="eid-message">
        <h2>عيد مبارك</h2>
        <p>نحتفل معكم بهذه المناسبة العطرة، سائلين المولى عز وجل أن يتقبل منا ومنكم صالح الأعمال، وأن يعيده علينا وعليكم بالخير واليمن والبركات.</p>
        <p>كل عام وأنتم بألف خير، وكل عام وأنتم إلى الله أقرب، تقبل الله منا ومنكم صالح الأعمال، وجعل أيامكم كلها أفراح وسرور.</p>
    </div>
    
    <div class="blessing">
        تقبل الله منا ومنكم صالح الأعمال
    </div>
</body>
</html>
