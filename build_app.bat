@echo off
title Professional Image Editor - Build Script
echo Professional Image Editor - Standalone Build
echo =============================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Check if pip is available
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo Error: pip is not available
    pause
    exit /b 1
)

echo.
echo Installing/Updating required packages...
python -m pip install --upgrade pip
python -m pip install pyinstaller
python -m pip install -r requirements.txt

echo.
echo Starting build process...
python build_standalone.py

echo.
echo Build process completed!
echo Check the 'distribution' folder for the final application.
echo.
pause
