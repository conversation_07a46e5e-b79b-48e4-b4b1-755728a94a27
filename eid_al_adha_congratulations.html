<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهنئة عيد الأضحى المبارك - مولد كريم</title>
    <meta name="description" content="مولد تهنئة عيد الأضحى المبارك - تصميم إسلامي أنيق للمشاركة على وسائل التواصل الاجتماعي">
    <meta name="keywords" content="عيد الأضحى، تهنئة، عيد مبارك، إسلامي، تصميم، مشاركة">

    <!-- Arabic Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- html2canvas Library for Image Generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'Scheherazade New', 'Noto Sans Arabic', serif;
            background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 25%, #4caf50 50%, #66bb6a 75%, #81c784 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="islamic-bg" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M25,0 L50,25 L25,50 L0,25 Z" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="1"/><circle cx="25" cy="25" r="8" fill="none" stroke="rgba(255,215,0,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23islamic-bg)"/></svg>') repeat;
            animation: backgroundMove 20s linear infinite;
            z-index: -1;
        }

        @keyframes backgroundMove {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(50px) translateY(50px); }
        }

        .main-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .eid-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%);
            border-radius: 25px;
            padding: 40px 30px;
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.3),
                0 8px 25px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 3px solid #d4af37;
            position: relative;
            overflow: hidden;
            aspect-ratio: 1 / 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 600px;
        }

        /* Islamic Geometric Pattern Overlay */
        .eid-card::before {
            content: '';
            position: absolute;
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><pattern id="geometric" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,0 L40,20 L20,40 L0,20 Z" fill="none" stroke="%23d4af37" stroke-width="2" opacity="0.3"/><circle cx="20" cy="20" r="6" fill="%23d4af37" opacity="0.1"/></pattern></defs><rect width="200" height="200" fill="url(%23geometric)"/></svg>') no-repeat;
            opacity: 0.4;
            animation: rotatePattern 30s linear infinite;
        }

        .eid-card::after {
            content: '';
            position: absolute;
            bottom: -50px;
            left: -50px;
            width: 200px;
            height: 200px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><pattern id="geometric2" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,0 L40,20 L20,40 L0,20 Z" fill="none" stroke="%234caf50" stroke-width="2" opacity="0.3"/><circle cx="20" cy="20" r="6" fill="%234caf50" opacity="0.1"/></pattern></defs><rect width="200" height="200" fill="url(%23geometric2)"/></svg>') no-repeat;
            opacity: 0.4;
            animation: rotatePattern 25s linear infinite reverse;
        }

        @keyframes rotatePattern {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Header Section */
        .eid-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .crescent-decoration {
            font-size: 3rem;
            color: #d4af37;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 10px rgba(212, 175, 55, 0.5); }
            to { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.8); }
        }

        .main-greeting {
            font-size: 2.8rem;
            font-weight: 700;
            color: #1a5f3f;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            margin-bottom: 10px;
            line-height: 1.2;
        }

        .sub-greeting {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d8f5f;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Content Section */
        .eid-content {
            text-align: center;
            position: relative;
            z-index: 2;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .quranic-verse {
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            border: 3px solid #d4af37;
            border-radius: 20px;
            padding: 25px;
            margin: 25px 0;
            font-size: 1.4rem;
            font-weight: 600;
            color: #1a5f3f;
            line-height: 1.8;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .verse-reference {
            font-size: 1rem;
            color: #666;
            margin-top: 15px;
            font-style: italic;
        }

        .eid-wishes {
            font-size: 1.6rem;
            color: #1a5f3f;
            font-weight: 600;
            margin: 20px 0;
            line-height: 1.6;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .decorative-symbols {
            font-size: 2rem;
            color: #d4af37;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Footer Section */
        .eid-footer {
            text-align: center;
            margin-top: 30px;
            position: relative;
            z-index: 2;
        }

        .date-info {
            font-size: 1.3rem;
            color: #2d8f5f;
            font-weight: 600;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(212, 175, 55, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(212, 175, 55, 0.3);
        }

        /* Control Buttons */
        .control-buttons {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
            color: #1a5f3f;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            font-family: 'Amiri', serif;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #f4d03f 0%, #d4af37 100%);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* Loading Animation */
        .loading {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 40px;
            border-radius: 15px;
            font-size: 1.2rem;
            z-index: 2000;
        }

        .loading.show {
            display: block;
        }

        /* Success Message */
        .success-message {
            display: none;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            z-index: 2000;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .success-message.show {
            display: block;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }
        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                max-width: 95%;
                padding: 10px;
            }

            .eid-card {
                padding: 25px 20px;
                min-height: 500px;
            }

            .main-greeting {
                font-size: 2.2rem;
            }

            .sub-greeting {
                font-size: 1.4rem;
            }

            .quranic-verse {
                font-size: 1.2rem;
                padding: 20px;
            }

            .eid-wishes {
                font-size: 1.3rem;
            }

            .control-buttons {
                bottom: 10px;
                gap: 10px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .eid-card {
                padding: 20px 15px;
                min-height: 450px;
            }

            .main-greeting {
                font-size: 1.8rem;
            }

            .sub-greeting {
                font-size: 1.2rem;
            }

            .quranic-verse {
                font-size: 1rem;
                padding: 15px;
            }

            .eid-wishes {
                font-size: 1.1rem;
            }

            .crescent-decoration {
                font-size: 2.5rem;
            }

            .decorative-symbols {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="eid-card" id="eidCard">
            <!-- Header Section -->
            <div class="eid-header">
                <div class="crescent-decoration">🌙 🕌 ☪️</div>
                <h1 class="main-greeting">عِيدُ الأَضْحَى المُبَارَك</h1>
                <h2 class="sub-greeting">كُلُّ عَامٍ وَأَنْتُمْ بِخَيْر</h2>
            </div>

            <!-- Content Section -->
            <div class="eid-content">
                <div class="quranic-verse">
                    "وَالْبُدْنَ جَعَلْنَاهَا لَكُم مِّن شَعَائِرِ اللَّهِ لَكُمْ فِيهَا خَيْرٌ"
                    <div class="verse-reference">سورة الحج - آية ٣٦</div>
                </div>

                <div class="eid-wishes">
                    تَقَبَّلَ اللهُ مِنَّا وَمِنْكُمْ صَالِحَ الأَعْمَال
                    <br>
                    وَكُلُّ عَامٍ وَأَنْتُمْ بِأَلْفِ خَيْر
                </div>

                <div class="decorative-symbols">🐑 ✨ 🤲 ✨ 🐑</div>

                <div class="eid-wishes">
                    عِيدٌ مُبَارَكٌ سَعِيد
                    <br>
                    أَعَادَهُ اللهُ عَلَيْكُمْ بِالْيُمْنِ وَالْبَرَكَات
                </div>
            </div>

            <!-- Footer Section -->
            <div class="eid-footer">
                <div class="date-info">
                    ١٠ ذُو الْحِجَّة ١٤٤٥ هـ
                    <br>
                    عِيدُ الأَضْحَى المُبَارَك
                </div>
            </div>
        </div>
    </div>

    <!-- Control Buttons -->
    <div class="control-buttons">
        <button class="btn" onclick="downloadImage()">تحميل الصورة 📥</button>
        <button class="btn" onclick="shareOnSocial()">مشاركة 📱</button>
        <button class="btn" onclick="printCard()">طباعة 🖨️</button>
    </div>

    <!-- Loading Animation -->
    <div class="loading" id="loadingMessage">
        جاري إنشاء الصورة... ⏳
    </div>

    <!-- Success Message -->
    <div class="success-message" id="successMessage">
        تم تحميل صورة التهنئة بنجاح! 🎉
    </div>

    <script>
        // Global variables
        let isGenerating = false;

        // Show loading message
        function showLoading() {
            document.getElementById('loadingMessage').classList.add('show');
        }

        // Hide loading message
        function hideLoading() {
            document.getElementById('loadingMessage').classList.remove('show');
        }

        // Show success message
        function showSuccess(message = 'تم تحميل صورة التهنئة بنجاح! 🎉') {
            const successMsg = document.getElementById('successMessage');
            successMsg.textContent = message;
            successMsg.classList.add('show');
            setTimeout(() => {
                successMsg.classList.remove('show');
            }, 3000);
        }

        // Download image function
        async function downloadImage() {
            if (isGenerating) return;

            isGenerating = true;
            showLoading();

            try {
                // Hide control buttons temporarily
                const controlButtons = document.querySelector('.control-buttons');
                controlButtons.style.display = 'none';

                // Get the card element
                const cardElement = document.getElementById('eidCard');

                // Configure html2canvas options for high quality
                const options = {
                    scale: 3, // High resolution
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    width: 1080,
                    height: 1080,
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: 1080,
                    windowHeight: 1080
                };

                // Generate canvas
                const canvas = await html2canvas(cardElement, options);

                // Create final canvas with exact 1:1 ratio
                const finalCanvas = document.createElement('canvas');
                finalCanvas.width = 1080;
                finalCanvas.height = 1080;
                const ctx = finalCanvas.getContext('2d');

                // Fill background
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, 1080, 1080);

                // Calculate scaling to fit the card in 1080x1080
                const scale = Math.min(1080 / canvas.width, 1080 / canvas.height);
                const scaledWidth = canvas.width * scale;
                const scaledHeight = canvas.height * scale;
                const x = (1080 - scaledWidth) / 2;
                const y = (1080 - scaledHeight) / 2;

                // Draw the card centered
                ctx.drawImage(canvas, x, y, scaledWidth, scaledHeight);

                // Create download link
                const link = document.createElement('a');
                link.download = 'عيد_الأضحى_المبارك_تهنئة_1080x1080.png';

                // Convert to blob for better quality
                finalCanvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    link.href = url;

                    // Trigger download
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Clean up
                    URL.revokeObjectURL(url);

                    // Show success message
                    showSuccess('تم تحميل صورة عيد الأضحى بنجاح! 🎉');
                }, 'image/png', 1.0);

            } catch (error) {
                console.error('Error generating image:', error);
                showSuccess('حدث خطأ في إنشاء الصورة. يرجى المحاولة مرة أخرى.');
            } finally {
                // Restore control buttons
                const controlButtons = document.querySelector('.control-buttons');
                controlButtons.style.display = 'flex';

                hideLoading();
                isGenerating = false;
            }
        }

        // Share on social media
        function shareOnSocial() {
            const shareText = encodeURIComponent('عيد الأضحى المبارك! كل عام وأنتم بخير 🌙🕌 تقبل الله منا ومنكم صالح الأعمال');
            const shareUrl = encodeURIComponent(window.location.href);

            // Create share menu
            const shareMenu = document.createElement('div');
            shareMenu.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 3000;
                text-align: center;
                font-family: 'Amiri', serif;
                direction: rtl;
            `;

            shareMenu.innerHTML = `
                <h3 style="margin-bottom: 20px; color: #1a5f3f;">مشاركة التهنئة</h3>
                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                    <button onclick="shareOnFacebook('${shareText}', '${shareUrl}')" style="background: #1877f2; color: white; border: none; padding: 10px 20px; border-radius: 10px; cursor: pointer;">فيسبوك</button>
                    <button onclick="shareOnTwitter('${shareText}', '${shareUrl}')" style="background: #1da1f2; color: white; border: none; padding: 10px 20px; border-radius: 10px; cursor: pointer;">تويتر</button>
                    <button onclick="shareOnWhatsApp('${shareText}')" style="background: #25d366; color: white; border: none; padding: 10px 20px; border-radius: 10px; cursor: pointer;">واتساب</button>
                    <button onclick="copyToClipboard('${decodeURIComponent(shareText)}')" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 10px; cursor: pointer;">نسخ النص</button>
                </div>
                <button onclick="closeShareMenu()" style="margin-top: 15px; background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 8px; cursor: pointer;">إغلاق</button>
            `;

            shareMenu.id = 'shareMenu';
            document.body.appendChild(shareMenu);

            // Add backdrop
            const backdrop = document.createElement('div');
            backdrop.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 2999;
            `;
            backdrop.id = 'shareBackdrop';
            backdrop.onclick = closeShareMenu;
            document.body.appendChild(backdrop);
        }

        // Close share menu
        function closeShareMenu() {
            const menu = document.getElementById('shareMenu');
            const backdrop = document.getElementById('shareBackdrop');
            if (menu) menu.remove();
            if (backdrop) backdrop.remove();
        }

        // Share functions
        function shareOnFacebook(text, url) {
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
            closeShareMenu();
        }

        function shareOnTwitter(text, url) {
            window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
            closeShareMenu();
        }

        function shareOnWhatsApp(text) {
            window.open(`https://wa.me/?text=${text}`, '_blank');
            closeShareMenu();
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showSuccess('تم نسخ النص بنجاح! 📋');
                closeShareMenu();
            }).catch(() => {
                showSuccess('لم يتم نسخ النص. يرجى المحاولة يدوياً.');
                closeShareMenu();
            });
        }

        // Print function
        function printCard() {
            // Hide control buttons
            const controlButtons = document.querySelector('.control-buttons');
            controlButtons.style.display = 'none';

            // Print
            window.print();

            // Restore control buttons after print dialog
            setTimeout(() => {
                controlButtons.style.display = 'flex';
            }, 1000);
        }

        // Add print styles
        const printStyles = document.createElement('style');
        printStyles.textContent = `
            @media print {
                body {
                    background: white !important;
                    padding: 0 !important;
                    margin: 0 !important;
                }
                .main-container {
                    max-width: none !important;
                    padding: 0 !important;
                }
                .eid-card {
                    box-shadow: none !important;
                    border: 2px solid #d4af37 !important;
                    page-break-inside: avoid;
                    margin: 0 !important;
                }
                .control-buttons,
                .loading,
                .success-message {
                    display: none !important;
                }
                body::before {
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(printStyles);

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth animations
            const card = document.getElementById('eidCard');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.8s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 's':
                            e.preventDefault();
                            downloadImage();
                            break;
                        case 'p':
                            e.preventDefault();
                            printCard();
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>
