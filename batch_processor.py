"""
Batch processing functionality for the Professional Image Editor
Handles multiple image operations, format conversions, and automated tasks
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Callable, Optional, Tuple
from PIL import Image
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from image_processor import ImageProcessor
from filters import FilterEngine
from config import SOCIAL_MEDIA_PRESETS, SUPPORTED_FORMATS
from utils import FileUtils, ImageUtils

class BatchOperation:
    """Represents a single batch operation"""
    
    def __init__(self, operation_type: str, parameters: Dict[str, Any]):
        self.operation_type = operation_type
        self.parameters = parameters
        self.description = self._generate_description()
    
    def _generate_description(self) -> str:
        """Generate human-readable description of the operation"""
        op_type = self.operation_type
        params = self.parameters
        
        if op_type == 'resize':
            return f"Resize to {params.get('width', '?')}x{params.get('height', '?')}"
        elif op_type == 'format_convert':
            return f"Convert to {params.get('format', '?').upper()}"
        elif op_type == 'filter':
            return f"Apply {params.get('filter_name', '?')} filter"
        elif op_type == 'quality':
            return f"Set quality to {params.get('quality', '?')}%"
        elif op_type == 'watermark':
            return f"Add watermark: {params.get('text', '?')}"
        elif op_type == 'crop':
            return f"Crop to {params.get('width', '?')}x{params.get('height', '?')}"
        elif op_type == 'rotate':
            return f"Rotate {params.get('angle', '?')}°"
        elif op_type == 'brightness':
            return f"Adjust brightness: {params.get('factor', '?')}"
        elif op_type == 'contrast':
            return f"Adjust contrast: {params.get('factor', '?')}"
        elif op_type == 'saturation':
            return f"Adjust saturation: {params.get('factor', '?')}"
        else:
            return f"{op_type.title()} operation"

class BatchProcessor:
    """Main batch processing engine"""
    
    def __init__(self):
        self.image_processor = ImageProcessor()
        self.filter_engine = FilterEngine()
        self.operations: List[BatchOperation] = []
        self.progress_callback: Optional[Callable] = None
        self.cancel_flag = False
    
    def add_operation(self, operation_type: str, parameters: Dict[str, Any]):
        """Add operation to batch queue"""
        operation = BatchOperation(operation_type, parameters)
        self.operations.append(operation)
    
    def clear_operations(self):
        """Clear all operations from queue"""
        self.operations.clear()
    
    def get_operations(self) -> List[BatchOperation]:
        """Get list of current operations"""
        return self.operations.copy()
    
    def set_progress_callback(self, callback: Callable[[int, int, str], None]):
        """Set callback for progress updates (current, total, status)"""
        self.progress_callback = callback
    
    def cancel_processing(self):
        """Cancel current batch processing"""
        self.cancel_flag = True
    
    def process_files(self, input_files: List[str], output_directory: str,
                     max_workers: int = 4) -> Dict[str, Any]:
        """Process multiple files with batch operations"""
        self.cancel_flag = False
        results = {
            'processed': 0,
            'failed': 0,
            'errors': [],
            'output_files': []
        }
        
        if not self.operations:
            return results
        
        # Ensure output directory exists
        Path(output_directory).mkdir(parents=True, exist_ok=True)
        
        # Filter valid input files
        valid_files = [f for f in input_files if FileUtils.is_supported_format(f)]
        total_files = len(valid_files)
        
        if total_files == 0:
            return results
        
        # Process files
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self._process_single_file, file_path, output_directory): file_path
                for file_path in valid_files
            }
            
            # Process completed tasks
            for future in as_completed(future_to_file):
                if self.cancel_flag:
                    break
                
                file_path = future_to_file[future]
                
                try:
                    output_file = future.result()
                    if output_file:
                        results['processed'] += 1
                        results['output_files'].append(output_file)
                    else:
                        results['failed'] += 1
                        results['errors'].append(f"Failed to process: {file_path}")
                
                except Exception as e:
                    results['failed'] += 1
                    results['errors'].append(f"Error processing {file_path}: {str(e)}")
                
                # Update progress
                if self.progress_callback:
                    completed = results['processed'] + results['failed']
                    status = f"Processing {Path(file_path).name}"
                    self.progress_callback(completed, total_files, status)
        
        return results
    
    def _process_single_file(self, input_file: str, output_directory: str) -> Optional[str]:
        """Process a single file with all operations"""
        try:
            # Load image
            image = self.image_processor.load_image(input_file)
            if image is None:
                return None
            
            # Apply all operations
            for operation in self.operations:
                if self.cancel_flag:
                    return None
                
                image = self._apply_operation(image, operation)
                if image is None:
                    return None
            
            # Generate output filename
            input_path = Path(input_file)
            output_filename = self._generate_output_filename(input_path, output_directory)
            
            # Save processed image
            if self.image_processor.save_image(image, output_filename):
                return output_filename
            else:
                return None
        
        except Exception as e:
            print(f"Error processing {input_file}: {e}")
            return None
    
    def _apply_operation(self, image: Image.Image, operation: BatchOperation) -> Optional[Image.Image]:
        """Apply a single operation to an image"""
        try:
            op_type = operation.operation_type
            params = operation.parameters
            
            if op_type == 'resize':
                width = params.get('width')
                height = params.get('height')
                maintain_aspect = params.get('maintain_aspect', True)
                
                if width and height:
                    return self.image_processor.resize_image(image, (width, height), maintain_aspect)
            
            elif op_type == 'crop':
                x = params.get('x', 0)
                y = params.get('y', 0)
                width = params.get('width')
                height = params.get('height')
                
                if width and height:
                    crop_box = (x, y, x + width, y + height)
                    return self.image_processor.crop_image(image, crop_box)
            
            elif op_type == 'rotate':
                angle = params.get('angle', 0)
                expand = params.get('expand', True)
                return self.image_processor.rotate_image(image, angle, expand)
            
            elif op_type == 'flip':
                direction = params.get('direction', 'horizontal')
                return self.image_processor.flip_image(image, direction)
            
            elif op_type == 'brightness':
                factor = params.get('factor', 1.0)
                return self.image_processor.adjust_brightness(image, factor)
            
            elif op_type == 'contrast':
                factor = params.get('factor', 1.0)
                return self.image_processor.adjust_contrast(image, factor)
            
            elif op_type == 'saturation':
                factor = params.get('factor', 1.0)
                return self.image_processor.adjust_saturation(image, factor)
            
            elif op_type == 'sharpness':
                factor = params.get('factor', 1.0)
                return self.image_processor.adjust_sharpness(image, factor)
            
            elif op_type == 'filter':
                filter_name = params.get('filter_name')
                if filter_name:
                    return self.filter_engine.apply_preset(image, filter_name)
            
            elif op_type == 'blur':
                radius = params.get('radius', 1.0)
                return self.image_processor.apply_blur(image, radius)
            
            elif op_type == 'noise_reduction':
                strength = params.get('strength', 10)
                return self.image_processor.apply_noise_reduction(image, strength)
            
            elif op_type == 'auto_enhance':
                return self.image_processor.auto_enhance(image)
            
            elif op_type == 'border':
                width = params.get('width', 10)
                color = params.get('color', 'white')
                return self.image_processor.add_border(image, width, color)
            
            elif op_type == 'social_media_resize':
                preset = params.get('preset')
                if preset in SOCIAL_MEDIA_PRESETS:
                    size = SOCIAL_MEDIA_PRESETS[preset]
                    return self.image_processor.create_thumbnail_with_aspect(image, size)
            
            return image
        
        except Exception as e:
            print(f"Error applying operation {operation.operation_type}: {e}")
            return None
    
    def _generate_output_filename(self, input_path: Path, output_directory: str) -> str:
        """Generate output filename with suffix"""
        output_dir = Path(output_directory)
        
        # Check if format conversion is requested
        new_format = None
        for operation in self.operations:
            if operation.operation_type == 'format_convert':
                new_format = operation.parameters.get('format')
                break
        
        # Determine file extension
        if new_format:
            extension = f".{new_format.lower()}"
        else:
            extension = input_path.suffix
        
        # Generate base filename
        base_name = input_path.stem
        
        # Add batch processing suffix
        output_name = f"{base_name}_processed{extension}"
        output_path = output_dir / output_name
        
        # Ensure unique filename
        return FileUtils.get_unique_filename(str(output_path))
    
    def create_social_media_batch(self, preset_name: str) -> bool:
        """Create batch operations for social media preset"""
        if preset_name not in SOCIAL_MEDIA_PRESETS:
            return False
        
        self.clear_operations()
        
        # Add resize operation
        width, height = SOCIAL_MEDIA_PRESETS[preset_name]
        self.add_operation('social_media_resize', {
            'preset': preset_name,
            'width': width,
            'height': height
        })
        
        # Add format conversion to JPEG for better compatibility
        self.add_operation('format_convert', {'format': 'jpeg'})
        
        # Add quality optimization
        self.add_operation('quality', {'quality': 85})
        
        return True
    
    def create_web_optimization_batch(self) -> None:
        """Create batch operations for web optimization"""
        self.clear_operations()
        
        # Resize for web (max 1920px width)
        self.add_operation('resize', {
            'width': 1920,
            'height': 1080,
            'maintain_aspect': True
        })
        
        # Convert to JPEG
        self.add_operation('format_convert', {'format': 'jpeg'})
        
        # Optimize quality
        self.add_operation('quality', {'quality': 80})
        
        # Auto enhance
        self.add_operation('auto_enhance', {})
    
    def create_print_optimization_batch(self) -> None:
        """Create batch operations for print optimization"""
        self.clear_operations()
        
        # High quality settings
        self.add_operation('auto_enhance', {})
        
        # Slight sharpening
        self.add_operation('sharpness', {'factor': 1.1})
        
        # Convert to high-quality JPEG
        self.add_operation('format_convert', {'format': 'jpeg'})
        self.add_operation('quality', {'quality': 95})
    
    def estimate_processing_time(self, file_count: int) -> float:
        """Estimate processing time in seconds"""
        # Base time per file (seconds)
        base_time = 0.5
        
        # Additional time per operation
        operation_time = len(self.operations) * 0.2
        
        # Total estimated time
        return file_count * (base_time + operation_time)
    
    def get_operation_summary(self) -> str:
        """Get summary of all operations"""
        if not self.operations:
            return "No operations defined"
        
        summary = f"Batch operations ({len(self.operations)} total):\n"
        for i, operation in enumerate(self.operations, 1):
            summary += f"{i}. {operation.description}\n"
        
        return summary.strip()
