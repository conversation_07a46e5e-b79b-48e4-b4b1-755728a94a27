#!/usr/bin/env python3
"""
Create a sample image based on the Twitter/X post description for TikTok enhancement
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_social_media_image():
    """Create a sample image similar to the Twitter/X post for demonstration"""
    
    # Create image with Twitter-like dimensions
    width, height = 600, 800
    image = Image.new('RGB', (width, height), (21, 32, 43))  # Twitter dark background
    draw = ImageDraw.Draw(image)
    
    # Try to get a font
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        text_font = ImageFont.truetype("arial.ttf", 18)
        small_font = ImageFont.truetype("arial.ttf", 14)
    except:
        title_font = ImageFont.load_default()
        text_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Header area (like Twitter header)
    draw.rectangle([0, 0, width, 80], fill=(21, 32, 43))
    
    # Profile info area
    draw.text((20, 20), "Toucan", font=title_font, fill=(255, 255, 255))
    draw.text((20, 45), "@AliToucan99", font=small_font, fill=(139, 162, 180))
    
    # Arabic text area (simulated)
    arabic_text = "اتقوا بالمعروف واليوم مو بورقة وصعوت بس #جرب_الة #سوريا #الرواء_رحلة"
    draw.text((20, 100), arabic_text, font=text_font, fill=(29, 161, 242))
    
    # Hashtags
    hashtags = "#المقاومة_شرقية #الاستكبارين"
    draw.text((20, 130), hashtags, font=text_font, fill=(29, 161, 242))
    
    # Main content area (simulating the photo area)
    content_y = 180
    content_height = 400
    
    # Create a lighter background for the "photo" area
    draw.rectangle([20, content_y, width-20, content_y + content_height], fill=(45, 55, 72))
    
    # Simulate two people in the photo
    # Person 1 (left)
    draw.ellipse([80, content_y + 50, 180, content_y + 150], fill=(139, 162, 180))
    draw.text((110, content_y + 170), "Person 1", font=text_font, fill=(255, 255, 255))
    
    # Person 2 (right)  
    draw.ellipse([320, content_y + 50, 420, content_y + 150], fill=(139, 162, 180))
    draw.text((350, content_y + 170), "Person 2", font=text_font, fill=(255, 255, 255))
    
    # Bottom overlay text (like in the original)
    overlay_text = "موقع السكسكية"
    text_bbox = draw.textbbox((0, 0), overlay_text, font=text_font)
    text_width = text_bbox[2] - text_bbox[0]
    
    # Blue overlay background
    overlay_y = content_y + content_height - 60
    draw.rectangle([20, overlay_y, width-20, content_y + content_height], fill=(29, 161, 242))
    
    # White text on blue background
    text_x = (width - text_width) // 2
    draw.text((text_x, overlay_y + 15), overlay_text, font=text_font, fill=(255, 255, 255))
    
    # Bottom metadata
    metadata_y = content_y + content_height + 20
    draw.text((20, metadata_y), "8:44 AM · May 26, 2025 · 2 Views", font=small_font, fill=(139, 162, 180))
    
    # Save the image
    output_path = "sample_social_media_post.jpg"
    image.save(output_path, 'JPEG', quality=90)
    print(f"✅ Sample image created: {output_path}")
    
    return output_path

if __name__ == "__main__":
    create_sample_social_media_image()
