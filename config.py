"""
Configuration file for Professional Image Editor
Contains application settings, constants, and default values
"""

import os
from pathlib import Path

# Application Information
APP_NAME = "Professional Image Editor"
APP_VERSION = "1.0.0"
APP_AUTHOR = "AliToucan"

# File Paths
BASE_DIR = Path(__file__).parent
FONTS_DIR = BASE_DIR / "fonts"
ASSETS_DIR = BASE_DIR / "assets"
TEMP_DIR = BASE_DIR / "temp"

# Supported File Formats
SUPPORTED_FORMATS = {
    'input': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'],
    'output': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.pdf']
}

# Arabic Fonts
ARABIC_FONTS = {
    'amiri': str(FONTS_DIR / "Amiri-Regular.ttf"),
    'scheherazade': str(FONTS_DIR / "Scheherazade-Regular.ttf"),
    'noto_arabic': str(FONTS_DIR / "NotoSansArabic-Regular.ttf")
}

# Default Settings
DEFAULT_SETTINGS = {
    'theme': 'light',  # 'light' or 'dark'
    'language': 'en',  # 'en' or 'ar'
    'auto_save': True,
    'backup_enabled': True,
    'max_undo_steps': 50,
    'default_quality': 95,
    'canvas_bg_color': '#f0f0f0',
    'grid_enabled': False,
    'rulers_enabled': False
}

# UI Colors
COLORS = {
    'light': {
        'bg': '#ffffff',
        'fg': '#000000',
        'accent': '#0078d4',
        'secondary': '#f3f2f1',
        'border': '#d1d1d1',
        'hover': '#e1dfdd',
        'selected': '#cce4f7'
    },
    'dark': {
        'bg': '#2d2d30',
        'fg': '#ffffff',
        'accent': '#0078d4',
        'secondary': '#3e3e42',
        'border': '#464647',
        'hover': '#4a4a4c',
        'selected': '#094771'
    }
}

# Social Media Presets
SOCIAL_MEDIA_PRESETS = {
    'Instagram Post': (1080, 1080),
    'Instagram Story': (1080, 1920),
    'Facebook Post': (1200, 630),
    'Facebook Cover': (820, 312),
    'Twitter Post': (1024, 512),
    'Twitter Header': (1500, 500),
    'YouTube Thumbnail': (1280, 720),
    'LinkedIn Post': (1200, 627),
    'Pinterest Pin': (735, 1102),
    'TikTok Video': (1080, 1920)
}

# Filter Presets
FILTER_PRESETS = {
    'Vintage': {'sepia': 0.3, 'contrast': 1.2, 'brightness': 0.9},
    'Black & White': {'saturation': 0.0, 'contrast': 1.1},
    'Warm': {'temperature': 200, 'saturation': 1.1},
    'Cool': {'temperature': -200, 'saturation': 1.1},
    'Dramatic': {'contrast': 1.4, 'saturation': 1.2, 'brightness': 0.95},
    'Soft': {'blur': 1, 'brightness': 1.05, 'contrast': 0.9}
}

# Drawing Tools
DRAWING_TOOLS = {
    'brush_sizes': [1, 2, 3, 5, 8, 12, 16, 20, 25, 30],
    'default_colors': ['#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff', 
                      '#ffff00', '#ff00ff', '#00ffff', '#ffa500', '#800080'],
    'shape_types': ['rectangle', 'circle', 'line', 'arrow', 'polygon']
}

# Keyboard Shortcuts
SHORTCUTS = {
    'new': '<Control-n>',
    'open': '<Control-o>',
    'save': '<Control-s>',
    'save_as': '<Control-Shift-s>',
    'undo': '<Control-z>',
    'redo': '<Control-y>',
    'copy': '<Control-c>',
    'paste': '<Control-v>',
    'zoom_in': '<Control-plus>',
    'zoom_out': '<Control-minus>',
    'zoom_fit': '<Control-0>',
    'crop': '<Control-r>',
    'resize': '<Control-t>',
    'rotate_left': '<Control-Left>',
    'rotate_right': '<Control-Right>',
    'flip_horizontal': '<Control-h>',
    'flip_vertical': '<Control-v>',
    'fullscreen': '<F11>',
    'quit': '<Control-q>'
}

# Performance Settings
PERFORMANCE = {
    'max_image_size': (8000, 8000),  # Maximum image dimensions
    'preview_size': (800, 600),      # Preview panel size
    'thumbnail_size': (150, 150),    # Thumbnail size
    'chunk_size': 1024 * 1024,       # File reading chunk size
    'max_memory_usage': 512 * 1024 * 1024  # 512MB max memory
}

# Create directories if they don't exist
def ensure_directories():
    """Create necessary directories if they don't exist"""
    for directory in [FONTS_DIR, ASSETS_DIR, TEMP_DIR]:
        directory.mkdir(exist_ok=True)

# Initialize directories
ensure_directories()
