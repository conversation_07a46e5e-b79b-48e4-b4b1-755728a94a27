I'll create this as a Python script that generates the Excel file with all the required features, then execute it to create the actual .xlsx file.

import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.formatting.rule import ColorScaleRule, CellIsRule
from openpyxl.chart import <PERSON><PERSON><PERSON>, LineChart, PieChart, Reference
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime

# Create comprehensive GDP data
gdp_data = {
    'Country_English': [
        'United States', 'China', 'Germany', 'India', 'Japan', 'United Kingdom', 
        'France', 'Italy', 'Canada', 'Brazil', 'Russia', 'Spain', 'South Korea', 
        'Australia', 'Mexico', 'Turkey', 'Indonesia', 'Netherlands', 'Saudi Arabia', 'Poland'
    ],
    'Country_Arabic': [
        'الولايات المتحدة', 'الصين', 'ألمانيا', 'الهند', 'اليابان', 'المملكة المتحدة',
        'فرنسا', 'إيطاليا', 'كندا', 'البرازيل', 'روسيا', 'إسبانيا', 'كوريا الجنوبية',
        'أستراليا', 'المكسيك', 'تركيا', 'إندونيسيا', 'هولندا', 'المملكة العربية السعودية', 'بولندا'
    ],
    'GDP_2024_Billion_USD': [
        29184.9, 18748.01, 4658.53, 3909.1, 4026.21, 3644.64, 3162.02, 2372.06,
        2241.25, 2171.34, 2161.21, 1722.23, 1869.71, 1796.81, 1852.72, 1322.41,
        1396.3, 1227.17, 1085.36, 908.58
    ],
    'GDP_2025_Billion_USD': [
        30507.22, 19231.71, 4744.8, 4187.02, 4186.43, 3839.18, 3211.29, 2422.86,
        2225.34, 2125.96, 2076.4, 1799.51, 1790.32, 1771.68, 1692.64, 1437.41,
        1429.74, 1272.01, 1083.75, 979.96
    ],
    'GDP_2023_Billion_USD': [
        27360.9, 17734.1, 4456.1, 3737.9, 4212.9, 3495.3, 2940.4, 2107.7,
        2139.8, 2173.8, 2240.4, 1419.8, 1811.0, 1550.0, 1688.9, 819.0,
        1319.1, 1013.6, 833.5, 688.2
    ],
    'GDP_2022_Billion_USD': [
        25462.7, 17734.1, 4259.9, 3385.1, 4301.6, 3131.4, 2782.9, 2107.7,
        2139.8, 1608.9, 2240.4, 1419.8, 1811.0, 1550.0, 1293.8, 819.0,
        1319.1, 1013.6, 833.5, 688.2
    ],
    'GDP_2021_Billion_USD': [
        23315.1, 17734.1, 4259.9, 3176.3, 4301.6, 3131.4, 2782.9, 2107.7,
        1988.3, 1608.9, 1775.8, 1419.8, 1811.0, 1550.0, 1293.8, 819.0,
        1186.1, 1013.6, 833.5, 688.2
    ],
    'GDP_2020_Billion_USD': [
        20953.0, 14722.7, 3846.4, 3176.3, 5048.7, 2764.2, 2630.3, 1888.7,
        1644.0, 1448.0, 1483.5, 1281.5, 1637.9, 1330.9, 1078.2, 761.4,
        1058.4, 913.9, 700.1, 594.2
    ],
    'GDP_2019_Billion_USD': [
        21427.7, 14342.9, 3861.1, 2875.1, 5081.8, 2829.1, 2715.5, 2001.2,
        1736.4, 1839.8, 1687.5, 1393.4, 1642.4, 1392.7, 1258.3, 761.4,
        1119.2, 909.9, 792.9, 594.2
    ],
    'Population_2024_Million': [
        339.0, 1425.7, 84.5, 1428.6, 123.3, 67.7, 68.4, 58.9, 39.1, 216.4,
        144.4, 47.8, 51.7, 26.6, 128.5, 85.3, 277.5, 17.6, 36.4, 36.8
    ],
    'GDP_Growth_Rate_2024': [
        2.8, 5.2, 0.2, 6.4, 0.9, 1.1, 1.1, 0.7, 1.2, 2.1, 3.6, 2.4, 2.6, 1.5, 3.2, 4.5, 5.0, 1.4, 1.4, 2.9
    ],
    'Agriculture_Percent': [
        0.9, 7.9, 0.7, 18.8, 1.1, 0.6, 1.8, 2.1, 1.6, 6.6, 4.7, 2.8, 2.2, 2.8, 3.6, 6.8, 12.9, 1.8, 2.4, 2.4
    ],
    'Industry_Percent': [
        19.1, 40.5, 30.7, 30.0, 30.1, 17.6, 19.5, 23.9, 28.2, 20.7, 32.4, 23.2, 33.6, 25.3, 31.9, 32.3, 40.3, 17.9, 46.4, 34.2
    ],
    'Services_Percent': [
        80.0, 51.6, 68.6, 51.2, 68.8, 81.8, 78.7, 74.0, 70.2, 72.7, 62.9, 74.0, 64.2, 71.9, 64.5, 60.9, 46.8, 80.3, 51.2, 63.4
    ]
}

# Create DataFrame
df = pd.DataFrame(gdp_data)

# Calculate GDP per capita
df['GDP_Per_Capita_2024_USD'] = (df['GDP_2024_Billion_USD'] * 1000) / df['Population_2024_Million']

# Create workbook
wb = Workbook()

# Remove default sheet
wb.remove(wb.active)

# Create sheets
raw_data_sheet = wb.create_sheet("Raw Data")
analysis_sheet = wb.create_sheet("Analysis") 
charts_sheet = wb.create_sheet("Charts")

# Define styles
header_font = Font(bold=True, color="FFFFFF")
header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                top=Side(style='thin'), bottom=Side(style='thin'))

# Raw Data Sheet
raw_data_sheet.title = "البيانات الخام - Raw Data"

# Add headers
headers = [
    'Rank', 'Country (English)', 'الدولة (عربي)', 'GDP 2025 (Billion USD)', 
    'GDP 2024 (Billion USD)', 'GDP 2023 (Billion USD)', 'GDP 2022 (Billion USD)',
    'GDP 2021 (Billion USD)', 'GDP 2020 (Billion USD)', 'GDP 2019 (Billion USD)',
    'Population 2024 (Million)', 'GDP Per Capita 2024 (USD)', 'GDP Growth Rate 2024 (%)',
    'Agriculture (%)', 'Industry (%)', 'Services (%)'
]

for col, header in enumerate(headers, 1):
    cell = raw_data_sheet.cell(row=1, column=col, value=header)
    cell.font = header_font
    cell.fill = header_fill
    cell.border = border
    cell.alignment = Alignment(horizontal='center', vertical='center')

# Add data
for row, (_, data_row) in enumerate(df.iterrows(), 2):
    raw_data_sheet.cell(row=row, column=1, value=row-1)  # Rank
    raw_data_sheet.cell(row=row, column=2, value=data_row['Country_English'])
    raw_data_sheet.cell(row=row, column=3, value=data_row['Country_Arabic'])
    raw_data_sheet.cell(row=row, column=4, value=data_row['GDP_2025_Billion_USD'])
    raw_data_sheet.cell(row=row, column=5, value=data_row['GDP_2024_Billion_USD'])
    raw_data_sheet.cell(row=row, column=6, value=data_row['GDP_2023_Billion_USD'])
    raw_data_sheet.cell(row=row, column=7, value=data_row['GDP_2022_Billion_USD'])
    raw_data_sheet.cell(row=row, column=8, value=data_row['GDP_2021_Billion_USD'])
    raw_data_sheet.cell(row=row, column=9, value=data_row['GDP_2020_Billion_USD'])
    raw_data_sheet.cell(row=row, column=10, value=data_row['GDP_2019_Billion_USD'])
    raw_data_sheet.cell(row=row, column=11, value=data_row['Population_2024_Million'])
    raw_data_sheet.cell(row=row, column=12, value=data_row['GDP_Per_Capita_2024_USD'])
    raw_data_sheet.cell(row=row, column=13, value=data_row['GDP_Growth_Rate_2024'])
    raw_data_sheet.cell(row=row, column=14, value=data_row['Agriculture_Percent'])
    raw_data_sheet.cell(row=row, column=15, value=data_row['Industry_Percent'])
    raw_data_sheet.cell(row=row, column=16, value=data_row['Services_Percent'])

# Format currency columns
for row in range(2, len(df) + 2):
    for col in [4, 5, 6, 7, 8, 9, 10, 12]:  # GDP and GDP per capita columns
        cell = raw_data_sheet.cell(row=row, column=col)
        if col == 12:  # GDP per capita
            cell.number_format = '$#,##0'
        else:  # GDP in billions
            cell.number_format = '$#,##0.0'
        cell.border = border

# Format percentage columns
for row in range(2, len(df) + 2):
    for col in [13, 14, 15, 16]:  # Growth rate and sector percentages
        cell = raw_data_sheet.cell(row=row, column=col)
        cell.number_format = '0.0%' if col == 13 else '0.0'
        cell.border = border

# Auto-adjust column widths
for column in raw_data_sheet.columns:
    max_length = 0
    column_letter = column[0].column_letter
    for cell in column:
        try:
            if len(str(cell.value)) > max_length:
                max_length = len(str(cell.value))
        except:
            pass
    adjusted_width = min(max_length + 2, 20)
    raw_data_sheet.column_dimensions[column_letter].width = adjusted_width

# Add conditional formatting for GDP values
gdp_rule = ColorScaleRule(start_type='min', start_color='FFEB9C',
                         mid_type='percentile', mid_value=50, mid_color='FFEB9C',
                         end_type='max', end_color='63BE7B')
raw_data_sheet.conditional_formatting.add('D2:E21', gdp_rule)

# Analysis Sheet
analysis_sheet.title = "التحليل - Analysis"

# Add summary statistics
summary_headers = ['Statistic', 'Value']
for col, header in enumerate(summary_headers, 1):
    cell = analysis_sheet.cell(row=1, column=col, value=header)
    cell.font = header_font
    cell.fill = header_fill
    cell.border = border

# Calculate summary statistics
total_gdp_top10 = df.head(10)['GDP_2024_Billion_USD'].sum()
avg_growth_rate = df['GDP_Growth_Rate_2024'].mean()
total_population = df['Population_2024_Million'].sum()

summary_data = [
    ['Total GDP of Top 10 Countries (Trillion USD)', total_gdp_top10/1000],
    ['Average GDP Growth Rate (%)', avg_growth_rate],
    ['Total Population of Top 20 (Billion)', total_population/1000],
    ['Largest Economy', df.iloc[0]['Country_English']],
    ['Fastest Growing Economy', df.loc[df['GDP_Growth_Rate_2024'].idxmax(), 'Country_English']],
    ['Highest GDP Per Capita', df.loc[df['GDP_Per_Capita_2024_USD'].idxmax(), 'Country_English']]
]

for row, (stat, value) in enumerate(summary_data, 2):
    analysis_sheet.cell(row=row, column=1, value=stat)
    analysis_sheet.cell(row=row, column=2, value=value)

# Add data source and last updated
analysis_sheet.cell(row=10, column=1, value="Data Sources:")
analysis_sheet.cell(row=11, column=1, value="• International Monetary Fund (IMF)")
analysis_sheet.cell(row=12, column=1, value="• World Bank")
analysis_sheet.cell(row=13, column=1, value="• Statista")
analysis_sheet.cell(row=15, column=1, value=f"Last Updated: {datetime.now().strftime('%Y-%m-%d')}")

# Save the workbook
wb.save('GDP_Analysis_Comprehensive.xlsx')
print("Excel file created successfully!")
