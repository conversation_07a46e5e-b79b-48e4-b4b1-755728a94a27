<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد ردود إنستغرام - مبسط</title>
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
            border-radius: 8px;
        }

        .logo {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: #e1306c;
        }

        .main-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .panel-header {
            background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .panel-content {
            padding: 2rem;
        }

        .section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .method-tabs {
            display: flex;
            background: #f5f5f5;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 1rem;
        }

        .method-tab {
            flex: 1;
            background: transparent;
            border: none;
            padding: 0.8rem;
            border-radius: 6px;
            cursor: pointer;
            transition: 0.3s;
            font-size: 1rem;
        }

        .method-tab.active {
            background: #e1306c;
            color: white;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: 0.3s;
        }

        .upload-area:hover {
            border-color: #e1306c;
            background: rgba(225, 48, 108, 0.05);
        }

        .upload-icon {
            font-size: 3rem;
            color: #999;
            margin-bottom: 1rem;
        }

        .upload-text {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .upload-button {
            background: #e1306c;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            margin-top: 1rem;
        }

        .upload-button:hover {
            background: #c42d5f;
        }

        .file-input {
            display: none;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-family: inherit;
            font-size: 1rem;
            direction: rtl;
        }

        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: #e1306c;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .text-options {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .font-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .font-size-slider {
            width: 120px;
        }

        .generate-button {
            background: #e1306c;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 1rem 0;
        }

        .generate-button:hover {
            background: #c42d5f;
        }

        .generate-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .result-section {
            text-align: center;
            margin-top: 2rem;
        }

        .result-canvas {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .download-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
        }

        .download-button:hover {
            background: #218838;
        }

        .status-message {
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            text-align: center;
        }

        .status-message.success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-message.error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .panel-content {
                padding: 1rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .text-options {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fab fa-instagram"></i>
                مولد ردود إنستغرام
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Main Panel -->
        <div class="main-panel">
            <div class="panel-header">
                <h1>إنشاء صورة رد على إنستغرام</h1>
            </div>
            <div class="panel-content">
                <!-- Input Method Selection -->
                <div class="section">
                    <h3 class="section-title">طريقة إدخال التعليق الأصلي</h3>
                    <div class="method-tabs">
                        <button class="method-tab active" onclick="switchMethod('image')">رفع صورة</button>
                        <button class="method-tab" onclick="switchMethod('text')">كتابة نص</button>
                    </div>
                </div>

                <!-- Image Upload Method -->
                <div class="section" id="imageMethod">
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">انقر لرفع صورة التعليق</div>
                        <div class="upload-subtext">JPG, PNG (حد أقصى 10MB)</div>
                        <button class="upload-button" type="button">اختيار صورة</button>
                    </div>
                    <input type="file" id="fileInput" class="file-input" accept="image/*">
                </div>

                <!-- Text Input Method -->
                <div class="section hidden" id="textMethod">
                    <div class="form-group">
                        <label>اسم المستخدم:</label>
                        <input type="text" id="username" class="form-input" value="user123">
                    </div>
                    <div class="form-group">
                        <label>نص التعليق الأصلي:</label>
                        <textarea id="originalComment" class="form-textarea" placeholder="اكتب التعليق الأصلي هنا..."></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>الوقت:</label>
                            <input type="text" id="commentTime" class="form-input" value="منذ ساعة">
                        </div>
                        <div class="form-group">
                            <label>عدد الإعجابات:</label>
                            <input type="number" id="commentLikes" class="form-input" value="5" min="0">
                        </div>
                    </div>
                </div>

                <!-- Reply Text -->
                <div class="section">
                    <h3 class="section-title">كتابة الرد</h3>
                    <div class="form-group">
                        <textarea id="replyText" class="form-textarea" placeholder="اكتب ردك هنا..."></textarea>
                    </div>
                    <div class="text-options">
                        <label>نوع الخط:</label>
                        <select id="fontSelect" class="font-select">
                            <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                            <option value="Amiri">Amiri</option>
                        </select>
                        <label>حجم الخط:</label>
                        <input type="range" id="fontSize" class="font-size-slider" min="16" max="32" value="22">
                        <span id="fontSizeValue">22px</span>
                    </div>
                </div>

                <!-- Generate Button -->
                <button class="generate-button" onclick="generateImage()">
                    <i class="fas fa-magic"></i>
                    إنشاء صورة الرد
                </button>

                <!-- Result Section -->
                <div class="result-section hidden" id="resultSection">
                    <canvas id="resultCanvas" class="result-canvas"></canvas>
                    <br>
                    <button class="download-button" onclick="downloadImage()">
                        <i class="fas fa-download"></i>
                        تحميل الصورة
                    </button>
                </div>

                <!-- Status Messages -->
                <div id="statusMessage" class="status-message hidden"></div>
            </div>
        </div>
    </div>

    <script>
        let uploadedImage = null;
        let currentMethod = 'image';

        // Switch between input methods
        function switchMethod(method) {
            currentMethod = method;

            // Update tabs
            document.querySelectorAll('.method-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // Show/hide sections
            document.getElementById('imageMethod').classList.toggle('hidden', method !== 'image');
            document.getElementById('textMethod').classList.toggle('hidden', method !== 'text');
        }

        // Handle file upload
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 10 * 1024 * 1024) {
                    showStatus('حجم الملف كبير جداً (أكثر من 10MB)', 'error');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        uploadedImage = img;
                        showStatus('تم رفع الصورة بنجاح!', 'success');
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Update font size display
        document.getElementById('fontSize').addEventListener('input', function() {
            document.getElementById('fontSizeValue').textContent = this.value + 'px';
        });

        // Generate image
        function generateImage() {
            try {
                // Validate inputs
                if (currentMethod === 'image' && !uploadedImage) {
                    showStatus('يرجى رفع صورة التعليق أولاً', 'error');
                    return;
                }

                if (currentMethod === 'text' && !document.getElementById('originalComment').value.trim()) {
                    showStatus('يرجى كتابة نص التعليق الأصلي', 'error');
                    return;
                }

                const replyText = document.getElementById('replyText').value.trim();
                if (!replyText) {
                    showStatus('يرجى كتابة نص الرد', 'error');
                    return;
                }

                // Show loading
                const button = document.querySelector('.generate-button');
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';

                // Create canvas with delay for UI update
                setTimeout(() => {
                    try {
                        createImage();
                        showStatus('تم إنشاء الصورة بنجاح!', 'success');
                    } catch (error) {
                        console.error('Error creating image:', error);
                        showStatus('حدث خطأ في إنشاء الصورة. يرجى المحاولة مرة أخرى', 'error');
                    } finally {
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-magic"></i> إنشاء صورة الرد';
                    }
                }, 500);
            } catch (error) {
                console.error('Error in generateImage:', error);
                showStatus('حدث خطأ غير متوقع', 'error');
            }
        }

        // Create the final image
        function createImage() {
            const canvas = document.getElementById('resultCanvas');
            const ctx = canvas.getContext('2d');

            // Set canvas size
            canvas.width = 1080;
            canvas.height = 1080;

            // Enable text rendering optimizations
            ctx.textBaseline = 'top';
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';

            // Background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add border
            ctx.strokeStyle = '#dbdbdb';
            ctx.lineWidth = 2;
            ctx.strokeRect(0, 0, canvas.width, canvas.height);

            let yPosition = 80;

            // Draw original comment
            if (currentMethod === 'image' && uploadedImage) {
                // Calculate image dimensions to fit
                const maxWidth = canvas.width - 120;
                const maxHeight = 500;
                let imgWidth = uploadedImage.width;
                let imgHeight = uploadedImage.height;

                // Scale image to fit
                const scaleX = maxWidth / imgWidth;
                const scaleY = maxHeight / imgHeight;
                const scale = Math.min(scaleX, scaleY);

                imgWidth *= scale;
                imgHeight *= scale;

                // Center the image
                const x = (canvas.width - imgWidth) / 2;

                // Draw image with border
                ctx.save();
                ctx.shadowColor = 'rgba(0,0,0,0.1)';
                ctx.shadowBlur = 10;
                ctx.drawImage(uploadedImage, x, yPosition, imgWidth, imgHeight);
                ctx.restore();

                yPosition += imgHeight + 80;
            } else if (currentMethod === 'text') {
                // Draw text comment
                yPosition = drawTextComment(ctx, yPosition);
                yPosition += 80;
            }

            // Draw reply
            drawReply(ctx, yPosition);

            // Show result
            document.getElementById('resultSection').classList.remove('hidden');
        }

        // Draw text comment
        function drawTextComment(ctx, y) {
            const username = document.getElementById('username').value || 'user123';
            const comment = document.getElementById('originalComment').value;
            const time = document.getElementById('commentTime').value || 'منذ ساعة';
            const likes = document.getElementById('commentLikes').value || '5';

            const boxWidth = canvas.width - 120;
            const boxHeight = 200;
            const x = 60;

            // Comment background with shadow
            ctx.save();
            ctx.shadowColor = 'rgba(0,0,0,0.1)';
            ctx.shadowBlur = 10;
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(x, y, boxWidth, boxHeight);
            ctx.restore();

            // Comment border
            ctx.strokeStyle = '#e1e8ed';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, boxWidth, boxHeight);

            // Avatar circle
            ctx.fillStyle = '#e1306c';
            ctx.beginPath();
            ctx.arc(x + 30, y + 30, 15, 0, 2 * Math.PI);
            ctx.fill();

            // Avatar icon
            ctx.fillStyle = '#ffffff';
            ctx.font = '16px FontAwesome';
            ctx.textAlign = 'center';
            ctx.fillText('👤', x + 30, y + 35);

            // Username
            ctx.fillStyle = '#262626';
            ctx.font = 'bold 20px "Noto Sans Arabic", Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.direction = 'rtl';
            ctx.fillText(username, canvas.width - 80, y + 35);

            // Comment text with word wrap
            ctx.fillStyle = '#262626';
            ctx.font = '18px "Noto Sans Arabic", Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.direction = 'rtl';
            drawWrappedText(ctx, comment, canvas.width - 80, y + 70, boxWidth - 100, 25);

            // Time and likes
            ctx.fillStyle = '#8e8e8e';
            ctx.font = '16px "Noto Sans Arabic", Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.direction = 'rtl';
            ctx.fillText(`${time} • ${likes} إعجاب`, canvas.width - 80, y + 160);

            return y + boxHeight;
        }

        // Helper function to draw wrapped text
        function drawWrappedText(ctx, text, x, y, maxWidth, lineHeight) {
            if (!text || text.trim() === '') return;

            const words = text.split(' ');
            let line = '';
            let currentY = y;
            const lines = [];

            // Build lines array
            for (let i = 0; i < words.length; i++) {
                const testLine = line + words[i] + ' ';
                const metrics = ctx.measureText(testLine);
                const testWidth = metrics.width;

                if (testWidth > maxWidth && i > 0) {
                    lines.push(line.trim());
                    line = words[i] + ' ';
                } else {
                    line = testLine;
                }
            }
            if (line.trim() !== '') {
                lines.push(line.trim());
            }

            // Draw lines
            for (let i = 0; i < lines.length; i++) {
                ctx.fillText(lines[i], x, currentY);
                currentY += lineHeight;
            }
        }

        // Draw reply
        function drawReply(ctx, y) {
            const replyText = document.getElementById('replyText').value;
            const fontSize = document.getElementById('fontSize').value;
            const fontFamily = document.getElementById('fontSelect').value;

            const boxWidth = canvas.width - 120;
            const boxHeight = 150;
            const x = 60;

            // Reply background with gradient
            const gradient = ctx.createLinearGradient(x, y, x + boxWidth, y + boxHeight);
            gradient.addColorStop(0, '#e1306c');
            gradient.addColorStop(1, '#c42d5f');

            // Draw reply box with shadow
            ctx.save();
            ctx.shadowColor = 'rgba(0,0,0,0.2)';
            ctx.shadowBlur = 15;
            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, boxWidth, boxHeight);
            ctx.restore();

            // Reply border
            ctx.strokeStyle = '#b02a56';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, boxWidth, boxHeight);

            // Reply icon
            ctx.fillStyle = '#ffffff';
            ctx.font = '24px FontAwesome';
            ctx.textAlign = 'center';
            ctx.fillText('💬', x + 40, y + 40);

            // Reply label
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 18px "Noto Sans Arabic", Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.direction = 'rtl';
            ctx.fillText('ردك:', canvas.width - 80, y + 35);

            // Reply text with word wrap
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${fontSize}px "${fontFamily}", Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.direction = 'rtl';

            // Center the text vertically
            const textY = y + boxHeight / 2 + parseInt(fontSize) / 3;
            drawWrappedText(ctx, replyText, canvas.width / 2, textY, boxWidth - 80, parseInt(fontSize) + 5);
        }

        // Download image
        function downloadImage() {
            try {
                const canvas = document.getElementById('resultCanvas');
                if (!canvas) {
                    showStatus('لا توجد صورة للتحميل', 'error');
                    return;
                }

                canvas.toBlob(function(blob) {
                    if (!blob) {
                        showStatus('فشل في إنشاء الصورة للتحميل', 'error');
                        return;
                    }

                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `رد_انستغرام_${new Date().getTime()}.png`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    showStatus('تم تحميل الصورة بنجاح!', 'success');
                }, 'image/png', 0.95);
            } catch (error) {
                console.error('Download error:', error);
                showStatus('حدث خطأ في تحميل الصورة', 'error');
            }
        }

        // Show status message
        function showStatus(message, type) {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status-message ${type}`;
            statusEl.classList.remove('hidden');

            setTimeout(() => {
                statusEl.classList.add('hidden');
            }, 3000);
        }
    </script>
</body>
</html>
