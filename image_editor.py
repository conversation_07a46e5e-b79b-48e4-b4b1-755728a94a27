"""
Main Image Editor GUI Application
Professional image editing application with comprehensive features
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, Menu
from PIL import Image, ImageTk
import os
from typing import Optional, List, Dict, Any, Tuple
import threading

# Import our modules
from config import *
from utils import *
from image_processor import ImageProcessor
from filters import FilterEngine
from layer_manager import LayerManager, Layer, BlendMode
from drawing_tools import DrawingEngine, DrawingTool
from batch_processor import BatchProcessor
from arabic_support import ArabicTextRenderer
from ui_components import *
from dialogs import *

class ImageEditor:
    """Main Image Editor Application"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title(f"{APP_NAME} v{APP_VERSION}")
        self.root.geometry("1200x800")

        # Initialize components
        self.image_processor = ImageProcessor()
        self.filter_engine = FilterEngine()
        self.drawing_engine = DrawingEngine()
        self.batch_processor = BatchProcessor()
        self.arabic_renderer = ArabicTextRenderer()
        self.settings = SettingsManager()
        self.history = HistoryManager(self.settings.get('max_undo_steps', 50))

        # Application state
        self.current_image: Optional[Image.Image] = None
        self.current_file_path: Optional[str] = None
        self.layer_manager: Optional[LayerManager] = None
        self.zoom_factor = 1.0
        self.is_modified = False
        self.current_tool = 'select'
        self.drawing_points = []
        self.drawing_active = False

        # UI components
        self.image_preview: Optional[ImagePreview] = None
        self.layer_panel: Optional[LayerPanel] = None
        self.tool_panel: Optional[ToolPanel] = None

        # Initialize UI
        self.setup_ui()
        self.setup_menu()
        self.setup_shortcuts()
        self.apply_theme()

        # Initialize status bar components
        self.status_label = None
        self.info_label = None

        # Center window
        UIUtils.center_window(self.root, 1200, 800)

    def setup_ui(self):
        """Setup main user interface"""
        # Main container
        main_container = tk.PanedWindow(self.root, orient='horizontal', sashrelief='raised')
        main_container.pack(fill='both', expand=True)

        # Left panel (tools and properties)
        left_panel = tk.Frame(main_container, width=250, bg='lightgray')
        main_container.add(left_panel, minsize=200)

        # Center panel (image canvas)
        center_panel = tk.Frame(main_container)
        main_container.add(center_panel, minsize=400)

        # Right panel (layers and adjustments)
        right_panel = tk.Frame(main_container, width=250, bg='lightgray')
        main_container.add(right_panel, minsize=200)

        # Setup left panel
        self.setup_left_panel(left_panel)

        # Setup center panel
        self.setup_center_panel(center_panel)

        # Setup right panel
        self.setup_right_panel(right_panel)

        # Status bar
        self.setup_status_bar()

    def setup_left_panel(self, parent):
        """Setup left panel with tools and properties"""
        # Tool panel
        self.tool_panel = ToolPanel(parent, self.on_tool_select)
        self.tool_panel.pack(fill='x', padx=5, pady=5)

        # Separator
        ttk.Separator(parent, orient='horizontal').pack(fill='x', pady=10)

        # Tool properties frame
        props_frame = tk.LabelFrame(parent, text="Tool Properties", font=('Arial', 9, 'bold'))
        props_frame.pack(fill='x', padx=5, pady=5)

        # Brush size
        self.brush_size_slider = SliderControl(
            props_frame, "Brush Size", 1, 50, 5, self.on_brush_size_change, 0
        )
        self.brush_size_slider.pack(fill='x', padx=5, pady=2)

        # Color picker
        color_frame = tk.Frame(props_frame)
        color_frame.pack(fill='x', padx=5, pady=5)
        tk.Label(color_frame, text="Color:", font=('Arial', 9)).pack(anchor='w')
        self.color_picker = ColorPicker(color_frame, '#000000', self.on_color_change)
        self.color_picker.pack(anchor='w', pady=2)

        # Opacity
        self.opacity_slider = SliderControl(
            props_frame, "Opacity", 0.0, 1.0, 1.0, self.on_opacity_change
        )
        self.opacity_slider.pack(fill='x', padx=5, pady=2)

    def setup_center_panel(self, parent):
        """Setup center panel with image canvas"""
        # Toolbar
        toolbar = tk.Frame(parent, height=40, bg='lightgray')
        toolbar.pack(fill='x', side='top')
        toolbar.pack_propagate(False)

        # Toolbar buttons
        self.setup_toolbar(toolbar)

        # Image preview
        self.image_preview = ImagePreview(parent, 600, 500)
        self.image_preview.pack(fill='both', expand=True, padx=5, pady=5)

        # Bind canvas events for drawing
        self.image_preview.canvas.bind('<Button-1>', self.on_canvas_click)
        self.image_preview.canvas.bind('<B1-Motion>', self.on_canvas_drag)
        self.image_preview.canvas.bind('<ButtonRelease-1>', self.on_canvas_release)

    def setup_toolbar(self, parent):
        """Setup toolbar with common actions"""
        # File operations
        tk.Button(parent, text="Open", command=self.open_file, width=8).pack(side='left', padx=2, pady=5)
        tk.Button(parent, text="Save", command=self.save_file, width=8).pack(side='left', padx=2, pady=5)

        # Separator
        ttk.Separator(parent, orient='vertical').pack(side='left', fill='y', padx=5)

        # Edit operations
        tk.Button(parent, text="Undo", command=self.undo, width=8).pack(side='left', padx=2, pady=5)
        tk.Button(parent, text="Redo", command=self.redo, width=8).pack(side='left', padx=2, pady=5)

        # Separator
        ttk.Separator(parent, orient='vertical').pack(side='left', fill='y', padx=5)

        # Zoom controls
        tk.Button(parent, text="Fit", command=self.fit_to_window, width=8).pack(side='left', padx=2, pady=5)
        tk.Button(parent, text="100%", command=self.zoom_actual_size, width=8).pack(side='left', padx=2, pady=5)

    def setup_right_panel(self, parent):
        """Setup right panel with layers and adjustments"""
        # Layer panel
        self.layer_panel = LayerPanel(parent, self.on_layer_action)
        self.layer_panel.pack(fill='both', expand=True, padx=5, pady=5)

        # Separator
        ttk.Separator(parent, orient='horizontal').pack(fill='x', pady=10)

        # Adjustments frame
        adj_frame = tk.LabelFrame(parent, text="Adjustments", font=('Arial', 9, 'bold'))
        adj_frame.pack(fill='x', padx=5, pady=5)

        # Brightness
        self.brightness_slider = SliderControl(
            adj_frame, "Brightness", 0.0, 2.0, 1.0, self.on_brightness_change
        )
        self.brightness_slider.pack(fill='x', padx=5, pady=2)

        # Contrast
        self.contrast_slider = SliderControl(
            adj_frame, "Contrast", 0.0, 2.0, 1.0, self.on_contrast_change
        )
        self.contrast_slider.pack(fill='x', padx=5, pady=2)

        # Saturation
        self.saturation_slider = SliderControl(
            adj_frame, "Saturation", 0.0, 2.0, 1.0, self.on_saturation_change
        )
        self.saturation_slider.pack(fill='x', padx=5, pady=2)

        # Filters frame
        filter_frame = tk.LabelFrame(parent, text="Filters", font=('Arial', 9, 'bold'))
        filter_frame.pack(fill='x', padx=5, pady=5)

        # Filter buttons
        filters = ['Vintage', 'Black & White', 'Sepia', 'Dramatic', 'Soft']
        for filter_name in filters:
            tk.Button(
                filter_frame,
                text=filter_name,
                command=lambda f=filter_name: self.apply_filter(f),
                width=12
            ).pack(pady=2, padx=5, fill='x')

    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = tk.Frame(self.root, height=25, bg='lightgray', relief='sunken', bd=1)
        self.status_bar.pack(fill='x', side='bottom')
        self.status_bar.pack_propagate(False)

        # Status label
        self.status_label = tk.Label(
            self.status_bar,
            text="Ready",
            bg='lightgray',
            anchor='w',
            font=('Arial', 9)
        )
        self.status_label.pack(side='left', padx=5)

        # Image info label
        self.info_label = tk.Label(
            self.status_bar,
            text="",
            bg='lightgray',
            anchor='e',
            font=('Arial', 9)
        )
        self.info_label.pack(side='right', padx=5)

    def setup_menu(self):
        """Setup application menu"""
        menubar = Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New", command=self.new_file, accelerator="Ctrl+N")
        file_menu.add_command(label="Open", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Save", command=self.save_file, accelerator="Ctrl+S")
        file_menu.add_command(label="Save As", command=self.save_as_file, accelerator="Ctrl+Shift+S")
        file_menu.add_command(label="Export", command=self.export_file)
        file_menu.add_separator()
        file_menu.add_command(label="Batch Process", command=self.open_batch_dialog)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.quit_app, accelerator="Ctrl+Q")

        # Edit menu
        edit_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Undo", command=self.undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="Redo", command=self.redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="Copy", command=self.copy_image, accelerator="Ctrl+C")
        edit_menu.add_command(label="Paste", command=self.paste_image, accelerator="Ctrl+V")

        # Image menu
        image_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Image", menu=image_menu)
        image_menu.add_command(label="Resize", command=self.resize_dialog)
        image_menu.add_command(label="Crop", command=self.crop_dialog)
        image_menu.add_command(label="Rotate", command=self.rotate_dialog)
        image_menu.add_separator()
        image_menu.add_command(label="Auto Enhance", command=self.auto_enhance)

        # Filters menu
        filters_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Filters", menu=filters_menu)
        for filter_name in self.filter_engine.get_available_filters():
            filters_menu.add_command(
                label=filter_name,
                command=lambda f=filter_name: self.apply_filter(f)
            )

        # View menu
        view_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Fit to Window", command=self.fit_to_window, accelerator="Ctrl+0")
        view_menu.add_command(label="Actual Size", command=self.zoom_actual_size, accelerator="Ctrl+1")
        view_menu.add_separator()
        view_menu.add_command(label="Toggle Theme", command=self.toggle_theme)

        # Help menu
        help_menu = Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="Shortcuts", command=self.show_shortcuts)

    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        for action, shortcut in SHORTCUTS.items():
            if hasattr(self, action):
                self.root.bind(shortcut, lambda e, a=action: getattr(self, a)())

    def apply_theme(self):
        """Apply current theme"""
        theme = self.settings.get('theme', 'light')
        colors = COLORS[theme]

        # Apply colors to main components
        self.root.configure(bg=colors['bg'])

        # Update status bar
        if hasattr(self, 'status_bar'):
            self.status_bar.configure(bg=colors['secondary'])
            self.status_label.configure(bg=colors['secondary'], fg=colors['fg'])
            self.info_label.configure(bg=colors['secondary'], fg=colors['fg'])

    def update_status(self, message: str):
        """Update status bar message"""
        if hasattr(self, 'status_label') and self.status_label:
            self.status_label.config(text=message)
            self.root.update_idletasks()
        else:
            print(f"Status: {message}")

    def update_image_info(self):
        """Update image information in status bar"""
        if hasattr(self, 'info_label') and self.info_label:
            if self.current_image:
                info = ImageUtils.get_image_info(self.current_file_path or "")
                if 'error' not in info:
                    size_text = f"{info['width']}x{info['height']}"
                    format_text = info.get('format', 'Unknown')
                    size_mb = info.get('file_size_mb', 0)
                    self.info_label.config(text=f"{size_text} | {format_text} | {size_mb:.1f}MB")
                else:
                    self.info_label.config(text="Image loaded")
            else:
                self.info_label.config(text="")

    def run(self):
        """Start the application"""
        self.root.mainloop()

    # File operations
    def new_file(self):
        """Create new file"""
        if self.check_unsaved_changes():
            # Create new blank image
            width, height = 800, 600
            self.current_image = Image.new('RGB', (width, height), 'white')
            self.current_file_path = None
            self.is_modified = False

            # Initialize layer manager
            self.layer_manager = LayerManager((width, height))
            background_layer = Layer("Background", self.current_image)
            self.layer_manager.add_layer(background_layer)

            # Update UI
            self.image_preview.set_image(self.current_image)
            self.update_layer_panel()
            self.update_image_info()
            self.update_status("New file created")

    def open_file(self):
        """Open image file"""
        if not self.check_unsaved_changes():
            return

        file_path = filedialog.askopenfilename(
            title="Open Image",
            filetypes=[
                ("All Images", "*.jpg *.jpeg *.png *.gif *.bmp *.tiff"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("PNG files", "*.png"),
                ("GIF files", "*.gif"),
                ("BMP files", "*.bmp"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.load_image(file_path)

    def load_image(self, file_path: str):
        """Load image from file path"""
        try:
            image = self.image_processor.load_image(file_path)
            if image:
                self.current_image = image
                self.current_file_path = file_path
                self.is_modified = False

                # Initialize layer manager
                self.layer_manager = LayerManager(image.size)
                background_layer = Layer("Background", image)
                self.layer_manager.add_layer(background_layer)

                # Reset adjustments
                self.reset_adjustments()

                # Update UI
                self.image_preview.set_image(image)
                self.update_layer_panel()
                self.update_image_info()
                self.update_status(f"Opened: {os.path.basename(file_path)}")

                # Add to history
                self.history.clear()
                self.save_state()
            else:
                messagebox.showerror("Error", "Failed to load image")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open file: {str(e)}")

    def save_file(self):
        """Save current file"""
        if self.current_file_path:
            self.save_to_path(self.current_file_path)
        else:
            self.save_as_file()

    def save_as_file(self):
        """Save file with new name"""
        if not self.current_image:
            return

        file_path = filedialog.asksaveasfilename(
            title="Save Image",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("GIF files", "*.gif"),
                ("BMP files", "*.bmp"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.save_to_path(file_path)

    def save_to_path(self, file_path: str):
        """Save image to specific path"""
        try:
            # Get flattened image from layer manager
            if self.layer_manager:
                save_image = self.layer_manager.flatten_image()
            else:
                save_image = self.current_image

            quality = self.settings.get('default_quality', 95)
            if self.image_processor.save_image(save_image, file_path, quality):
                self.current_file_path = file_path
                self.is_modified = False
                self.update_status(f"Saved: {os.path.basename(file_path)}")
            else:
                messagebox.showerror("Error", "Failed to save image")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save file: {str(e)}")

    def export_file(self):
        """Export with options"""
        if not self.current_image:
            return

        # Create export dialog
        export_dialog = ExportDialog(self.root, self.current_image)
        if export_dialog.result:
            self.update_status("Image exported successfully")

    def check_unsaved_changes(self) -> bool:
        """Check for unsaved changes and prompt user"""
        if self.is_modified:
            result = messagebox.askyesnocancel(
                "Unsaved Changes",
                "You have unsaved changes. Do you want to save before continuing?"
            )
            if result is True:  # Yes - save
                self.save_file()
                return True
            elif result is False:  # No - don't save
                return True
            else:  # Cancel
                return False
        return True

    # Edit operations
    def undo(self):
        """Undo last operation"""
        if self.history.can_undo():
            state = self.history.undo()
            if state:
                self.restore_state(state)
                self.update_status("Undo")

    def redo(self):
        """Redo last undone operation"""
        if self.history.can_redo():
            state = self.history.redo()
            if state:
                self.restore_state(state)
                self.update_status("Redo")

    def copy_image(self):
        """Copy current image to clipboard"""
        # Implementation depends on platform
        self.update_status("Copy not implemented yet")

    def paste_image(self):
        """Paste image from clipboard"""
        # Implementation depends on platform
        self.update_status("Paste not implemented yet")

    def save_state(self):
        """Save current state for undo/redo"""
        if self.layer_manager:
            # Save layer manager state
            state = {
                'layers': self.layer_manager.get_layer_info(),
                'canvas_size': self.layer_manager.canvas_size,
                'active_layer': self.layer_manager.active_layer_index
            }
            self.history.add_state(state)

    def restore_state(self, state: Dict[str, Any]):
        """Restore state from history"""
        # This is a simplified version - full implementation would restore all layer data
        self.update_status("State restored")

    def reset_adjustments(self):
        """Reset all adjustment sliders to default values"""
        self.brightness_slider.set_value(1.0)
        self.contrast_slider.set_value(1.0)
        self.saturation_slider.set_value(1.0)

    # Image operations
    def resize_dialog(self):
        """Show resize dialog"""
        if not self.current_image:
            return

        dialog = ResizeDialog(self.root, self.current_image.size)
        if dialog.result:
            new_size, maintain_aspect = dialog.result
            self.resize_image(new_size, maintain_aspect)

    def resize_image(self, new_size: Tuple[int, int], maintain_aspect: bool = True):
        """Resize current image"""
        if not self.current_image:
            return

        try:
            self.save_state()
            resized = self.image_processor.resize_image(self.current_image, new_size, maintain_aspect)
            self.current_image = resized

            # Update layer manager
            if self.layer_manager:
                active_layer = self.layer_manager.get_active_layer()
                if active_layer:
                    active_layer.image = resized

            self.image_preview.set_image(resized)
            self.is_modified = True
            self.update_status(f"Resized to {resized.width}x{resized.height}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to resize image: {str(e)}")

    def crop_dialog(self):
        """Show crop dialog"""
        if not self.current_image:
            return

        # For now, use a simple crop dialog
        # In a full implementation, this would be an interactive crop tool
        dialog = CropDialog(self.root, self.current_image.size)
        if dialog.result:
            crop_box = dialog.result
            self.crop_image(crop_box)

    def crop_image(self, crop_box: Tuple[int, int, int, int]):
        """Crop current image"""
        if not self.current_image:
            return

        try:
            self.save_state()
            cropped = self.image_processor.crop_image(self.current_image, crop_box)
            self.current_image = cropped

            # Update layer manager
            if self.layer_manager:
                active_layer = self.layer_manager.get_active_layer()
                if active_layer:
                    active_layer.image = cropped

            self.image_preview.set_image(cropped)
            self.is_modified = True
            self.update_status(f"Cropped to {cropped.width}x{cropped.height}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to crop image: {str(e)}")

    def rotate_dialog(self):
        """Show rotate dialog"""
        if not self.current_image:
            return

        dialog = RotateDialog(self.root)
        if dialog.result:
            angle = dialog.result
            self.rotate_image(angle)

    def rotate_image(self, angle: float):
        """Rotate current image"""
        if not self.current_image:
            return

        try:
            self.save_state()
            rotated = self.image_processor.rotate_image(self.current_image, angle)
            self.current_image = rotated

            # Update layer manager
            if self.layer_manager:
                active_layer = self.layer_manager.get_active_layer()
                if active_layer:
                    active_layer.image = rotated

            self.image_preview.set_image(rotated)
            self.is_modified = True
            self.update_status(f"Rotated by {angle}°")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to rotate image: {str(e)}")

    def auto_enhance(self):
        """Apply auto enhancement"""
        if not self.current_image:
            return

        try:
            self.save_state()
            enhanced = self.image_processor.auto_enhance(self.current_image)
            self.current_image = enhanced

            # Update layer manager
            if self.layer_manager:
                active_layer = self.layer_manager.get_active_layer()
                if active_layer:
                    active_layer.image = enhanced

            self.image_preview.set_image(enhanced)
            self.is_modified = True
            self.update_status("Auto enhancement applied")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to enhance image: {str(e)}")

    # Filter operations
    def apply_filter(self, filter_name: str):
        """Apply filter to current image"""
        if not self.current_image:
            return

        try:
            self.save_state()

            if filter_name in self.filter_engine.presets:
                filtered = self.filter_engine.apply_preset(self.current_image, filter_name)
            else:
                # Handle special filters
                if filter_name == 'Vintage':
                    filtered = self.filter_engine.vintage_effect(self.current_image)
                elif filter_name == 'Black & White':
                    filtered = self.filter_engine.black_and_white_effect(self.current_image)
                elif filter_name == 'Sepia':
                    filtered = self.filter_engine.sepia_effect(self.current_image)
                elif filter_name == 'Dramatic':
                    filtered = self.filter_engine.dramatic_effect(self.current_image)
                elif filter_name == 'Soft':
                    filtered = self.filter_engine.soft_effect(self.current_image)
                else:
                    filtered = self.current_image

            self.current_image = filtered

            # Update layer manager
            if self.layer_manager:
                active_layer = self.layer_manager.get_active_layer()
                if active_layer:
                    active_layer.image = filtered

            self.image_preview.set_image(filtered)
            self.is_modified = True
            self.update_status(f"Applied {filter_name} filter")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply filter: {str(e)}")

    # Adjustment operations
    def on_brightness_change(self, value: float):
        """Handle brightness adjustment"""
        if self.current_image:
            self.apply_adjustment('brightness', value)

    def on_contrast_change(self, value: float):
        """Handle contrast adjustment"""
        if self.current_image:
            self.apply_adjustment('contrast', value)

    def on_saturation_change(self, value: float):
        """Handle saturation adjustment"""
        if self.current_image:
            self.apply_adjustment('saturation', value)

    def apply_adjustment(self, adjustment_type: str, value: float):
        """Apply real-time adjustment"""
        if not self.current_image or not self.layer_manager:
            return

        try:
            # Get original layer image
            active_layer = self.layer_manager.get_active_layer()
            if not active_layer:
                return

            # Apply adjustment
            if adjustment_type == 'brightness':
                adjusted = self.image_processor.adjust_brightness(active_layer.image, value)
            elif adjustment_type == 'contrast':
                adjusted = self.image_processor.adjust_contrast(active_layer.image, value)
            elif adjustment_type == 'saturation':
                adjusted = self.image_processor.adjust_saturation(active_layer.image, value)
            else:
                return

            # Update preview (not the actual layer yet)
            self.image_preview.set_image(adjusted)
            self.is_modified = True
        except Exception as e:
            print(f"Error applying adjustment: {e}")

    # Tool operations
    def on_tool_select(self, tool_id: str):
        """Handle tool selection"""
        self.current_tool = tool_id
        self.update_status(f"Selected tool: {tool_id.title()}")

    def on_brush_size_change(self, size: float):
        """Handle brush size change"""
        self.brush_size = int(size)

    def on_color_change(self, color: str):
        """Handle color change"""
        self.current_color = color

    def on_opacity_change(self, opacity: float):
        """Handle opacity change"""
        self.current_opacity = opacity

    # Canvas events for drawing
    def on_canvas_click(self, event):
        """Handle canvas click"""
        if self.current_tool in ['brush', 'pencil', 'eraser']:
            self.drawing_active = True
            self.drawing_points = [(event.x, event.y)]
        elif self.current_tool == 'text':
            self.add_text_at_position(event.x, event.y)

    def on_canvas_drag(self, event):
        """Handle canvas drag"""
        if self.drawing_active and self.current_tool in ['brush', 'pencil', 'eraser']:
            self.drawing_points.append((event.x, event.y))
            # Real-time drawing preview could be added here

    def on_canvas_release(self, event):
        """Handle canvas release"""
        if self.drawing_active:
            self.drawing_active = False
            self.apply_drawing()

    def apply_drawing(self):
        """Apply drawing to current layer"""
        if not self.drawing_points or not self.layer_manager:
            return

        try:
            active_layer = self.layer_manager.get_active_layer()
            if not active_layer:
                return

            # Convert canvas coordinates to image coordinates
            # This is simplified - full implementation would handle zoom and pan
            image_points = self.drawing_points

            if self.current_tool == 'brush':
                modified_image = self.drawing_engine.draw_brush_stroke(
                    active_layer.image, image_points,
                    getattr(self, 'brush_size', 5),
                    getattr(self, 'current_color', '#000000'),
                    getattr(self, 'current_opacity', 1.0)
                )
            elif self.current_tool == 'pencil':
                modified_image = self.drawing_engine.draw_pencil_stroke(
                    active_layer.image, image_points,
                    getattr(self, 'brush_size', 3),
                    getattr(self, 'current_color', '#000000'),
                    getattr(self, 'current_opacity', 0.8)
                )
            elif self.current_tool == 'eraser':
                modified_image = self.drawing_engine.erase_area(
                    active_layer.image, image_points,
                    getattr(self, 'brush_size', 10)
                )
            else:
                return

            # Update layer
            active_layer.image = modified_image

            # Update preview
            flattened = self.layer_manager.flatten_image()
            self.image_preview.set_image(flattened)
            self.is_modified = True

        except Exception as e:
            print(f"Error applying drawing: {e}")
        finally:
            self.drawing_points = []

    def add_text_at_position(self, x: int, y: int):
        """Add text at specified position"""
        text_dialog = TextDialog(self.root)
        if text_dialog.result:
            text, font_size, is_arabic = text_dialog.result

            try:
                active_layer = self.layer_manager.get_active_layer()
                if not active_layer:
                    return

                # Convert canvas coordinates to image coordinates
                image_x, image_y = x, y  # Simplified

                modified_image = self.drawing_engine.add_text(
                    active_layer.image, (image_x, image_y), text,
                    font_size, None, getattr(self, 'current_color', '#000000'),
                    is_arabic
                )

                # Update layer
                active_layer.image = modified_image

                # Update preview
                flattened = self.layer_manager.flatten_image()
                self.image_preview.set_image(flattened)
                self.is_modified = True

            except Exception as e:
                messagebox.showerror("Error", f"Failed to add text: {str(e)}")

    # Layer operations
    def on_layer_action(self, action: str, data: Any):
        """Handle layer panel actions"""
        if action == 'select':
            self.layer_manager.set_active_layer(data)
            self.update_layer_panel()
        elif action == 'add':
            self.add_new_layer()
        elif action == 'delete':
            self.delete_active_layer()
        elif action == 'duplicate':
            self.duplicate_active_layer()
        elif action == 'opacity':
            self.set_layer_opacity(data)

    def add_new_layer(self):
        """Add new blank layer"""
        if not self.layer_manager:
            return

        # Create blank layer
        size = self.layer_manager.canvas_size
        blank_image = Image.new('RGBA', size, (255, 255, 255, 0))
        new_layer = Layer(f"Layer {len(self.layer_manager.layers) + 1}", blank_image)

        self.layer_manager.add_layer(new_layer)
        self.update_layer_panel()
        self.is_modified = True

    def delete_active_layer(self):
        """Delete active layer"""
        if not self.layer_manager:
            return

        active_layer = self.layer_manager.get_active_layer()
        if active_layer and len(self.layer_manager.layers) > 1:
            if messagebox.askyesno("Delete Layer", f"Delete layer '{active_layer.name}'?"):
                self.layer_manager.remove_layer(active_layer.id)
                self.update_layer_panel()
                self.update_preview()
                self.is_modified = True
        else:
            messagebox.showwarning("Warning", "Cannot delete the last layer")

    def duplicate_active_layer(self):
        """Duplicate active layer"""
        if not self.layer_manager:
            return

        active_layer = self.layer_manager.get_active_layer()
        if active_layer:
            new_layer_id = self.layer_manager.duplicate_layer(active_layer.id)
            if new_layer_id:
                self.update_layer_panel()
                self.is_modified = True

    def set_layer_opacity(self, opacity: float):
        """Set active layer opacity"""
        if not self.layer_manager:
            return

        active_layer = self.layer_manager.get_active_layer()
        if active_layer:
            active_layer.set_opacity(opacity)
            self.update_preview()
            self.is_modified = True

    def update_layer_panel(self):
        """Update layer panel display"""
        if self.layer_panel and self.layer_manager:
            layer_info = self.layer_manager.get_layer_info()
            self.layer_panel.update_layers(layer_info)

    def update_preview(self):
        """Update image preview"""
        if self.layer_manager:
            flattened = self.layer_manager.flatten_image()
            self.image_preview.set_image(flattened)

    # View operations
    def fit_to_window(self):
        """Fit image to window"""
        if self.image_preview:
            self.image_preview.fit_to_window()

    def zoom_actual_size(self):
        """Zoom to actual size"""
        if self.image_preview:
            self.image_preview.zoom_to_actual_size()

    # Batch processing
    def open_batch_dialog(self):
        """Open batch processing dialog"""
        dialog = BatchDialog(self.root, self.batch_processor)
        if dialog.result:
            self.update_status("Batch processing completed")

    # Utility methods
    def toggle_theme(self):
        """Toggle between light and dark theme"""
        current_theme = self.settings.get('theme', 'light')
        new_theme = 'dark' if current_theme == 'light' else 'light'
        self.settings.set('theme', new_theme)
        self.apply_theme()
        self.update_status(f"Switched to {new_theme} theme")

    def show_about(self):
        """Show about dialog"""
        about_text = f"""
{APP_NAME} v{APP_VERSION}

Professional Image Editor with Arabic Support
Developed by {APP_AUTHOR}

Features:
• Advanced image editing tools
• Layer support with blending modes
• Arabic text rendering (RTL)
• Batch processing
• Multiple export formats
• Customizable interface

Built with Python, Pillow, and tkinter
        """
        messagebox.showinfo("About", about_text.strip())

    def show_shortcuts(self):
        """Show keyboard shortcuts"""
        shortcuts_text = "Keyboard Shortcuts:\n\n"
        for action, shortcut in SHORTCUTS.items():
            action_name = action.replace('_', ' ').title()
            shortcuts_text += f"{action_name}: {shortcut}\n"

        messagebox.showinfo("Keyboard Shortcuts", shortcuts_text)

    def quit_app(self):
        """Quit application"""
        if self.check_unsaved_changes():
            self.root.quit()
