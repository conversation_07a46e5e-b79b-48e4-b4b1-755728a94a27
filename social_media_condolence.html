<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منشور تعزية - الإمام محمد الجواد (عليه السلام)</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- html2canvas Library for Image Generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>', 'Noto Sans Arabic', serif;
            background: #f0f2f5;
            direction: rtl;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .social-post {
            width: 500px;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .post-header {
            background: rgba(212, 175, 55, 0.1);
            padding: 1rem;
            border-bottom: 2px solid #d4af37;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .brand-logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #d4af37;
        }

        .brand-info h3 {
            color: #d4af37;
            font-size: 1.2rem;
            margin-bottom: 0.2rem;
        }

        .brand-info p {
            color: #90ee90;
            font-size: 0.9rem;
        }

        .post-content {
            padding: 2rem;
            color: #f5f5f5;
            text-align: center;
        }

        .verse {
            background: rgba(144, 238, 144, 0.1);
            border: 2px solid rgba(144, 238, 144, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
            color: #90ee90;
            font-weight: 600;
        }

        .imam-name {
            font-size: 2rem;
            color: #d4af37;
            font-weight: 700;
            margin: 1rem 0;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        .honorific {
            display: block;
            font-size: 1.2rem;
            color: #90ee90;
            margin-top: 0.5rem;
        }

        .condolence-message {
            font-size: 1rem;
            line-height: 1.8;
            margin: 1.5rem 0;
            text-align: justify;
        }

        .date-highlight {
            background: rgba(212, 175, 55, 0.2);
            border: 2px solid rgba(212, 175, 55, 0.5);
            border-radius: 25px;
            padding: 0.8rem 1.5rem;
            margin: 1.5rem 0;
            font-size: 1.1rem;
            color: #d4af37;
            font-weight: 600;
            display: inline-block;
        }

        .hashtags {
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
            font-size: 0.9rem;
            color: #4a9eff;
            line-height: 1.5;
        }

        .copy-button {
            background: #d4af37;
            color: #000;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 1rem;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .copy-button:hover {
            background: #b8941f;
            transform: translateY(-2px);
        }

        .copy-success {
            color: #90ee90;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .copy-success.show {
            opacity: 1;
        }

        /* Download Button Styles */
        .download-section {
            padding: 1rem;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
        }

        .download-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.8rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .download-btn {
            background: linear-gradient(90deg, #90ee90, #4a9eff);
            color: #000;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(144, 238, 144, 0.3);
        }

        .download-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .download-progress {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(144, 238, 144, 0.3);
            display: none;
        }

        .progress-text {
            color: #90ee90;
            font-weight: 600;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(144, 238, 144, 0.2);
            border-radius: 3px;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #90ee90, #4a9eff);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 3px;
        }

        /* Mobile Responsive */
        @media (max-width: 600px) {
            .social-post {
                width: 100%;
                max-width: 400px;
            }

            .post-content {
                padding: 1.5rem;
            }

            .imam-name {
                font-size: 1.8rem;
            }

            .condolence-message {
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    <div class="social-post">
        <!-- Post Header -->
        <div class="post-header">
            <img src="app_icon.png" alt="AliToucan" class="brand-logo">
            <div class="brand-info">
                <h3>AliToucan</h3>
                <p>تعزية العالم الإسلامي</p>
            </div>
        </div>

        <!-- Post Content -->
        <div class="post-content">
            <!-- Quranic Verse -->
            <div class="verse">
                "إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ"
            </div>

            <!-- Imam Name -->
            <h2 class="imam-name">
                الإمام محمد الجواد
                <span class="honorific">(عليه السلام)</span>
            </h2>

            <!-- Condolence Message -->
            <p class="condolence-message">
                نتقدم إلى العالم الإسلامي بأحر التعازي بمناسبة ذكرى استشهاد الإمام التاسع من أئمة أهل البيت الأطهار،
                الذي استشهد مسموماً وهو في ريعان شبابه، تاركاً للأمة إرثاً عظيماً من العلم والحكمة.
            </p>

            <!-- Date -->
            <div class="date-highlight">
                ٢٩ ذو القعدة - ذكرى الاستشهاد
            </div>

            <!-- Hashtags -->
            <div class="hashtags">
                #الإمام_الجواد #أهل_البيت #ذو_القعدة #تعزية_العالم_الإسلامي #الشيعة #AliToucan
                <br>
                #ImamJawad #AhlulBayt #IslamicCondolence #ShiaIslam
            </div>

            <!-- Copy Button -->
            <button class="copy-button" onclick="copyToClipboard()">
                📋 نسخ المنشور
            </button>
            <div class="copy-success" id="copySuccess">تم نسخ المنشور بنجاح! ✅</div>
        </div>

        <!-- Download Section -->
        <div class="download-section">
            <h4 style="color: #d4af37; text-align: center; margin-bottom: 0.5rem;">تحميل كصورة</h4>
            <div class="download-buttons">
                <button class="download-btn" onclick="downloadSocialImage('instagram', 1080, 1080)">
                    📱 إنستغرام
                </button>
                <button class="download-btn" onclick="downloadSocialImage('story', 1080, 1920)">
                    📖 ستوري
                </button>
                <button class="download-btn" onclick="downloadSocialImage('facebook', 1200, 630)">
                    📘 فيسبوك
                </button>
            </div>

            <!-- Progress Indicator -->
            <div class="download-progress" id="downloadProgress">
                <div class="progress-text" id="progressText">جاري إنشاء الصورة...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables for image generation
        let isGenerating = false;

        function copyToClipboard() {
            const postText = `
"إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ"

الإمام محمد الجواد (عليه السلام)

نتقدم إلى العالم الإسلامي بأحر التعازي بمناسبة ذكرى استشهاد الإمام التاسع من أئمة أهل البيت الأطهار، الذي استشهد مسموماً وهو في ريعان شبابه، تاركاً للأمة إرثاً عظيماً من العلم والحكمة.

٢٩ ذو القعدة - ذكرى الاستشهاد

#الإمام_الجواد #أهل_البيت #ذو_القعدة #تعزية_العالم_الإسلامي #الشيعة #AliToucan
#ImamJawad #AhlulBayt #IslamicCondolence #ShiaIslam

© AliToucan
            `.trim();

            navigator.clipboard.writeText(postText).then(function() {
                const successMsg = document.getElementById('copySuccess');
                successMsg.classList.add('show');
                setTimeout(() => {
                    successMsg.classList.remove('show');
                }, 3000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = postText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const successMsg = document.getElementById('copySuccess');
                successMsg.classList.add('show');
                setTimeout(() => {
                    successMsg.classList.remove('show');
                }, 3000);
            });
        }

        // Progress management for downloads
        function showProgress() {
            document.getElementById('downloadProgress').style.display = 'block';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = 'جاري إنشاء الصورة...';
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            if (text) {
                document.getElementById('progressText').textContent = text;
            }
        }

        function hideProgress() {
            setTimeout(() => {
                document.getElementById('downloadProgress').style.display = 'none';
            }, 1000);
        }

        function toggleButtons(disabled) {
            const buttons = document.querySelectorAll('.download-btn');
            buttons.forEach(btn => {
                btn.disabled = disabled;
            });
        }

        function generateFilename(format) {
            const date = new Date();
            const timestamp = date.getFullYear() + '-' +
                            String(date.getMonth() + 1).padStart(2, '0') + '-' +
                            String(date.getDate()).padStart(2, '0');

            const formatNames = {
                'instagram': 'انستغرام_مربع',
                'story': 'ستوري_عمودي',
                'facebook': 'فيسبوك_أفقي'
            };

            return `منشور_تعزية_الإمام_الجواد_${formatNames[format]}_${timestamp}.png`;
        }

        // Main download function for social media
        async function downloadSocialImage(format, width, height) {
            if (isGenerating) return;

            isGenerating = true;
            toggleButtons(true);
            showProgress();

            try {
                updateProgress(10, 'تحضير المنشور...');

                // Get the social post element
                const element = document.querySelector('.social-post');

                // Temporarily hide download section for clean image
                const downloadSection = document.querySelector('.download-section');
                downloadSection.style.display = 'none';

                updateProgress(30, 'تحسين جودة الخطوط...');

                // Wait for fonts to load
                await document.fonts.ready;

                updateProgress(50, 'إنشاء الصورة...');

                // Configure html2canvas options for high quality
                const options = {
                    allowTaint: true,
                    useCORS: true,
                    scale: 2,
                    width: element.offsetWidth,
                    height: element.offsetHeight,
                    backgroundColor: null,
                    logging: false,
                    imageTimeout: 15000,
                    removeContainer: true,
                    foreignObjectRendering: true,
                    onclone: function(clonedDoc) {
                        const clonedElement = clonedDoc.querySelector('.social-post');
                        if (clonedElement) {
                            clonedElement.style.fontFamily = "'Amiri', 'Noto Sans Arabic', serif";
                            clonedElement.style.direction = 'rtl';
                        }
                    }
                };

                updateProgress(70, 'معالجة النص العربي...');

                // Generate canvas
                const canvas = await html2canvas(element, options);

                updateProgress(85, 'تحسين الصورة...');

                // Create final canvas with desired dimensions
                const finalCanvas = document.createElement('canvas');
                finalCanvas.width = width;
                finalCanvas.height = height;
                const ctx = finalCanvas.getContext('2d');

                // Fill background
                ctx.fillStyle = '#f0f2f5';
                ctx.fillRect(0, 0, width, height);

                // Calculate scaling and positioning
                const scale = Math.min(width / canvas.width, height / canvas.height) * 0.95;
                const scaledWidth = canvas.width * scale;
                const scaledHeight = canvas.height * scale;
                const x = (width - scaledWidth) / 2;
                const y = (height - scaledHeight) / 2;

                // Draw the content
                ctx.drawImage(canvas, x, y, scaledWidth, scaledHeight);

                updateProgress(95, 'حفظ الملف...');

                // Convert to blob and download
                finalCanvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = generateFilename(format);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    updateProgress(100, 'تم التحميل بنجاح! ✅');

                    // Show download section again
                    downloadSection.style.display = 'block';

                    setTimeout(() => {
                        hideProgress();
                        toggleButtons(false);
                        isGenerating = false;
                    }, 1500);

                }, 'image/png', 0.95);

            } catch (error) {
                console.error('Error generating image:', error);
                updateProgress(0, 'حدث خطأ في إنشاء الصورة ❌');

                // Show download section again
                downloadSection.style.display = 'block';

                setTimeout(() => {
                    hideProgress();
                    toggleButtons(false);
                    isGenerating = false;
                }, 2000);
            }
        }
    </script>
</body>
</html>
