@echo off
title Professional Image Editor - Portable
echo Professional Image Editor - Portable Version
echo ============================================
echo.

REM Check if executable exists
if not exist "Professional_Image_Editor.exe" (
    echo Error: Professional_Image_Editor.exe not found!
    echo Please make sure you're running this from the correct directory.
    pause
    exit /b 1
)

REM Set portable mode environment
set PORTABLE_MODE=1
set APP_DATA_DIR=%~dp0data

REM Create data directory for portable settings
if not exist "%APP_DATA_DIR%" mkdir "%APP_DATA_DIR%"

echo Starting Professional Image Editor...
echo Data directory: %APP_DATA_DIR%
echo.

REM Launch the application
start "" "Professional_Image_Editor.exe"

REM Optional: Wait for application to close
REM "Professional_Image_Editor.exe"

echo Application closed.
pause
