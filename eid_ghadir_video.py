#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Video Generator for Eid al-Ghadir Celebration
Converts PowerPoint presentation to high-quality MP4 video
For Shia Muslim Community Celebrations
Enhanced Arabic Text Rendering Quality
"""

import os
import sys
from moviepy.editor import <PERSON>Clip, concatenate_videoclips, CompositeVideoClip
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
import numpy as np

class EidGhadirVideoGenerator:
    def __init__(self):
        self.video_width = 1920
        self.video_height = 1080
        self.fps = 30
        self.slide_duration = 5  # 5 seconds per slide as requested

        # Islamic festive colors for Eid al-Ghadir
        self.colors = {
            'gold': (255, 215, 0),      # Gold #FFD700
            'green': (34, 139, 34),     # Forest Green #228B22
            'white': (255, 255, 255),   # White #FFFFFF
            'navy_blue': (0, 0, 128),   # Navy Blue #000080
            'light_green': (144, 238, 144), # Light Green for accents
            'dark_green': (0, 100, 0)   # Dark Green for backgrounds
        }

        # High-quality Arabic fonts in priority order
        self.arabic_font_paths = [
            "fonts/Amiri-Regular.ttf",           # Primary choice - excellent for religious texts
            "fonts/NotoSansArabic-Regular.ttf"   # Fallback - reliable cross-platform support
        ]

        # English fonts (only for English text)
        self.english_font_paths = [
            "fonts/Calibri.ttf",        # Modern, clean appearance
            "arial.ttf",                # System fallback
            "Arial.ttf"                 # Alternative system fallback
        ]

        # Slide content data matching the PowerPoint presentation
        self.slides_data = [
            {
                'title': 'عيد الغدير المبارك',
                'subtitle': 'كل عام وأنتم بخير بمناسبة عيد الولاية المباركة',
                'eng_subtitle': 'Blessed Eid al-Ghadir\nCelebrating the Divine Appointment at Ghadir Khumm',
                'date': '١٨ ذو الحجة ١٠ هجرية - 18th Dhul Hijjah, 10 AH',
                'greeting': 'عيد مبارك - Eid Mubarak',
                'bg_color': 'dark_green'
            },
            {
                'title': 'السياق التاريخي لحادثة غدير خم',
                'content': [
                    '• في طريق العودة من حجة الوداع في السنة العاشرة للهجرة',
                    '• توقف النبي (صلى الله عليه وآله وسلم) في غدير خم',
                    '• جمع المسلمين في يوم شديد الحر',
                    '• نزول آية إكمال الدين بعد الإعلان',
                    '• حضور أكثر من مائة ألف مسلم',
                    '• شهادة الصحابة على هذا الحدث التاريخي',
                    '• تأكيد الولاية الإلهية لأمير المؤمنين علي (عليه السلام)'
                ],
                'eng_content': 'Historical Context of Ghadir Khumm Event:\n• On the return journey from Farewell Pilgrimage in 10 AH\n• Prophet (peace be upon him and his family) stopped at Ghadir Khumm\n• Gathered Muslims on an extremely hot day\n• Revelation of verse of completion of religion after the announcement\n• Presence of more than 100,000 Muslims\n• Testimony of companions to this historical event\n• Confirmation of divine authority for Commander of Faithful Ali (peace be upon him)',
                'bg_color': 'navy_blue'
            },
            {
                'title': 'الإعلان النبوي المبارك',
                'hadith': 'مَن كُنتُ مَولاهُ فَعَلِيٌّ مَولاهُ، اللَّهُمَّ والِ مَن والاهُ وَعادِ مَن عاداهُ',
                'eng_translation': '"Whoever I am his master (mawla), Ali is his master.\nO Allah, befriend whoever befriends him and be enemy to whoever is his enemy."',
                'source': 'حديث الغدير المتواتر - رواه أكثر من مائة صحابي',
                'eng_source': 'The Mutawatir Hadith of Ghadir - Narrated by more than 100 companions',
                'bg_color': 'green'
            },
            {
                'title': 'أهمية عيد الغدير في التاريخ الإسلامي',
                'content': [
                    '• إكمال الدين الإسلامي بتعيين الخليفة الشرعي',
                    '• تأكيد مبدأ الولاية الإلهية في الإسلام',
                    '• ضمان استمرارية الهداية الربانية بعد النبي (ص)',
                    '• إقامة الحجة على الأمة في مسألة الخلافة',
                    '• بداية عهد الإمامة المعصومة',
                    '• أعظم الأعياد عند الشيعة الإمامية',
                    '• يوم إتمام النعمة الإلهية على المسلمين',
                    '• تثبيت أسس العدالة والحق في الأمة'
                ],
                'eng_content': 'Significance of Eid al-Ghadir in Islamic History:\n• Completion of Islamic religion by appointing the rightful successor\n• Confirmation of divine authority principle in Islam\n• Ensuring continuity of divine guidance after the Prophet (pbuh)\n• Establishing proof for the nation regarding succession\n• Beginning of the era of infallible Imamate\n• Greatest celebration for Shia Muslims\n• Day of completion of divine blessing upon Muslims\n• Establishing foundations of justice and truth in the nation',
                'bg_color': 'dark_green'
            },
            {
                'title': 'فضائل أمير المؤمنين علي (عليه السلام)',
                'content': [
                    '• أول من آمن بالنبي (صلى الله عليه وآله وسلم)',
                    '• باب مدينة علم النبي (ص)',
                    '• نفس النبي في آية المباهلة',
                    '• أخو النبي ووصيه ووزيره',
                    '• فارس الإسلام وبطل المسلمين',
                    '• قاضي المسلمين وحاكمهم العادل',
                    '• إمام المتقين وقائد الغر المحجلين',
                    '• أعلم الصحابة وأتقاهم وأشجعهم'
                ],
                'eng_content': 'Virtues of Commander of Faithful Ali (peace be upon him):\n• First to believe in the Prophet (peace be upon him and his family)\n• Gate to the city of Prophet\'s knowledge\n• Soul of the Prophet in verse of Mubahala\n• Brother, successor and minister of the Prophet\n• Knight of Islam and hero of Muslims\n• Judge of Muslims and their just ruler\n• Imam of the pious and leader of the distinguished\n• Most knowledgeable, pious and brave of companions',
                'bg_color': 'navy_blue'
            },
            {
                'title': 'الآيات القرآنية المرتبطة بالغدير',
                'verse1': 'الْيَوْمَ أَكْمَلْتُ لَكُمْ دِينَكُمْ وَأَتْمَمْتُ عَلَيْكُمْ نِعْمَتِي وَرَضِيتُ لَكُمُ الْإِسْلَامَ دِينًا',
                'verse1_ref': 'سُورَةُ الْمَائِدَةِ - آيَة ٣',
                'verse2': 'إِنَّمَا وَلِيُّكُمُ اللَّهُ وَرَسُولُهُ وَالَّذِينَ آمَنُوا الَّذِينَ يُقِيمُونَ الصَّلَاةَ وَيُؤْتُونَ الزَّكَاةَ وَهُمْ رَاكِعُونَ',
                'verse2_ref': 'سُورَةُ الْمَائِدَةِ - آيَة ٥٥',
                'eng_translation1': '"Today I have perfected your religion for you, completed My favor upon you, and chosen Islam as your religion."',
                'eng_translation2': '"Your guardian is only Allah, His Messenger, and the believers who establish prayer and give charity while bowing."',
                'bg_color': 'green'
            },
            {
                'title': 'التهاني والتبريكات بعيد الغدير',
                'content': [
                    '• كل عام وأنتم بخير بمناسبة عيد الولاية المباركة',
                    '• عيد غدير خم مبارك على جميع المؤمنين',
                    '• بارك الله لكم في هذا العيد العظيم',
                    '• أعاده الله عليكم بالخير والبركات',
                    '• عيد مبارك وكل عام وأنتم إلى الله أقرب',
                    '• تقبل الله منا ومنكم صالح الأعمال',
                    '• جعلنا الله من المتمسكين بولاية أهل البيت (ع)',
                    '• عيد الغدير الأغر على الأمة الإسلامية مبارك'
                ],
                'eng_content': 'Congratulations and Blessings for Eid al-Ghadir:\n• May you be blessed every year on this blessed occasion of Wilayah\n• Blessed Eid al-Ghadir Khumm to all believers\n• May Allah bless you on this great celebration\n• May Allah return it to you with goodness and blessings\n• Blessed Eid and may you be closer to Allah each year\n• May Allah accept our and your righteous deeds\n• May Allah make us among those who hold fast to Ahl al-Bayt\'s authority\n• Blessed radiant Eid al-Ghadir upon the Islamic nation',
                'bg_color': 'dark_green'
            },
            {
                'title': 'الدروس والعبر من عيد الغدير',
                'content': [
                    '• أهمية الطاعة لله ورسوله والأئمة المعصومين (ع)',
                    '• ضرورة التمسك بولاية أهل البيت (عليهم السلام)',
                    '• الولاية أساس قبول الأعمال عند الله تعالى',
                    '• العدالة والحق مبادئ أساسية في الإسلام',
                    '• القيادة الربانية ضرورة لهداية الأمة',
                    '• الوحدة تحت راية الحق والعدالة',
                    '• التضحية في سبيل الدين والمبادئ السامية',
                    '• الثبات على الحق مهما كانت التحديات'
                ],
                'eng_content': 'Lessons and Teachings from Eid al-Ghadir:\n• Importance of obedience to Allah, His Messenger and infallible Imams\n• Necessity of holding fast to Ahl al-Bayt\'s authority\n• Wilayah is foundation for acceptance of deeds by Allah\n• Justice and truth are fundamental principles in Islam\n• Divine leadership is necessary for guiding the nation\n• Unity under the banner of truth and justice\n• Sacrifice for religion and noble principles\n• Steadfastness on truth regardless of challenges',
                'bg_color': 'navy_blue'
            },
            {
                'title': 'الوحدة في اتباع أهل البيت (عليهم السلام)',
                'content': [
                    '• أهل البيت (ع) هم سفينة النجاة للأمة الإسلامية',
                    '• التمسك بهم يوحد المسلمين تحت راية الحق',
                    '• منهجهم طريق الهداية والصلاح',
                    '• محبتهم تجمع القلوب على الخير',
                    '• اتباعهم يحقق العدالة والسلام',
                    '• تعاليمهم تبني مجتمعاً مؤمناً قوياً',
                    '• ولايتهم تضمن الفوز في الدنيا والآخرة',
                    '• هم الطريق المستقيم إلى الله تعالى'
                ],
                'eng_content': 'Unity in Following Ahl al-Bayt (peace be upon them):\n• Ahl al-Bayt are the ark of salvation for the Islamic nation\n• Holding fast to them unites Muslims under the banner of truth\n• Their methodology is the path of guidance and righteousness\n• Love for them brings hearts together for goodness\n• Following them achieves justice and peace\n• Their teachings build a strong believing society\n• Their authority ensures success in this world and the hereafter\n• They are the straight path to Allah Almighty',
                'bg_color': 'green'
            },
            {
                'title': 'عيد الغدير الأغر مبارك',
                'closing_text': 'تقبل الله منا ومنكم\nوجعلنا من المتمسكين بولاية أهل البيت الأطهار\nعليهم أفضل الصلاة والسلام',
                'final_greeting': 'كل عام وأنتم بخير',
                'eng_closing': 'Blessed Radiant Eid al-Ghadir\n\nMay Allah accept from us and from you\nAnd make us among those who hold fast to the authority of the pure Ahl al-Bayt\nUpon them be the best prayers and peace\n\nMay you be blessed every year',
                'bg_color': 'dark_green'
            }
        ]

    def format_arabic_text(self, text):
        """Enhanced Arabic text formatting with improved RTL display and diacritics"""
        try:
            # Use arabic_reshaper with default settings that preserve diacritics
            # The library automatically handles proper letter connections and RTL text
            reshaped_text = arabic_reshaper.reshape(text)

            # Apply bidirectional algorithm for proper RTL display
            bidi_text = get_display(reshaped_text)

            return bidi_text
        except Exception as e:
            print(f"Warning: Arabic text processing failed: {e}")
            return text

    def get_arabic_font(self, size):
        """Get the best available Arabic font"""
        # Try Arabic fonts first
        for font_path in self.arabic_font_paths:
            try:
                font = ImageFont.truetype(font_path, size)
                return font
            except Exception as e:
                continue

        # Try system Arabic fonts
        system_arabic_fonts = [
            "arial.ttf",  # Arial supports Arabic on Windows
            "Arial.ttf",
            "tahoma.ttf", # Tahoma has good Arabic support
            "Tahoma.ttf"
        ]

        for font_path in system_arabic_fonts:
            try:
                font = ImageFont.truetype(font_path, size)
                return font
            except Exception as e:
                continue

        # Fallback to default font
        print("Warning: No Arabic fonts available, using default font")
        return ImageFont.load_default()

    def get_english_font(self, size):
        """Get the best available English font"""
        # Try system English fonts
        system_english_fonts = [
            "arial.ttf",
            "Arial.ttf",
            "calibri.ttf",
            "Calibri.ttf",
            "times.ttf",
            "Times.ttf"
        ]

        for font_path in system_english_fonts:
            try:
                font = ImageFont.truetype(font_path, size)
                return font
            except Exception as e:
                continue

        # Fallback to default font
        return ImageFont.load_default()

    def create_slide_image(self, slide_data, slide_num):
        """Create an image for a single slide with enhanced Arabic text rendering"""
        # Create image with background color
        bg_color = self.colors[slide_data['bg_color']]
        img = Image.new('RGB', (self.video_width, self.video_height), bg_color)
        draw = ImageDraw.Draw(img)

        # Load high-quality fonts with proper separation
        title_font_ar = self.get_arabic_font(80)      # Arabic titles
        content_font_ar = self.get_arabic_font(50)    # Arabic content
        subtitle_font_ar = self.get_arabic_font(45)   # Arabic subtitles
        hadith_font_ar = self.get_arabic_font(60)     # Arabic hadith text

        # English fonts (separate from Arabic)
        title_font_en = self.get_english_font(70)     # English titles
        content_font_en = self.get_english_font(35)   # English content
        subtitle_font_en = self.get_english_font(30)  # English subtitles

        y_position = 100

        # Title (always Arabic - use Arabic font)
        if 'title' in slide_data:
            title_text = self.format_arabic_text(slide_data['title'])

            # Get text size for centering with Arabic font
            bbox = draw.textbbox((0, 0), title_text, font=title_font_ar)
            text_width = bbox[2] - bbox[0]
            x_position = (self.video_width - text_width) // 2

            draw.text((x_position, y_position), title_text,
                     font=title_font_ar, fill=self.colors['gold'])
            y_position += 150

        # Handle different slide types
        if slide_num == 1:  # Title slide
            if 'subtitle' in slide_data:
                subtitle_text = self.format_arabic_text(slide_data['subtitle'])
                bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), subtitle_text,
                         font=subtitle_font_ar, fill=self.colors['white'])
                y_position += 100

            if 'eng_subtitle' in slide_data:
                eng_lines = slide_data['eng_subtitle'].split('\n')
                for line in eng_lines:
                    bbox = draw.textbbox((0, 0), line, font=content_font_en)
                    text_width = bbox[2] - bbox[0]
                    x_position = (self.video_width - text_width) // 2
                    draw.text((x_position, y_position), line,
                             font=content_font_en, fill=self.colors['white'])
                    y_position += 50

            if 'date' in slide_data:
                # Mixed Arabic-English text - use Arabic font for proper rendering
                date_text = self.format_arabic_text(slide_data['date'])
                bbox = draw.textbbox((0, 0), date_text, font=subtitle_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position + 50), date_text,
                         font=subtitle_font_ar, fill=self.colors['gold'])

            if 'greeting' in slide_data:
                # Mixed Arabic-English text - use Arabic font for proper rendering
                greeting_text = self.format_arabic_text(slide_data['greeting'])
                bbox = draw.textbbox((0, 0), greeting_text, font=subtitle_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position + 150), greeting_text,
                         font=subtitle_font_ar, fill=self.colors['light_green'])

        elif slide_num == 3:  # Hadith slide
            if 'hadith' in slide_data:
                hadith_text = self.format_arabic_text(slide_data['hadith'])
                bbox = draw.textbbox((0, 0), hadith_text, font=hadith_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), hadith_text,
                         font=hadith_font_ar, fill=self.colors['gold'])
                y_position += 200

            if 'eng_translation' in slide_data:
                eng_lines = slide_data['eng_translation'].split('\n')
                for line in eng_lines:
                    bbox = draw.textbbox((0, 0), line, font=content_font_en)
                    text_width = bbox[2] - bbox[0]
                    x_position = (self.video_width - text_width) // 2
                    draw.text((x_position, y_position), line,
                             font=content_font_en, fill=self.colors['white'])
                    y_position += 50

            if 'source' in slide_data:
                y_position += 100
                source_text = self.format_arabic_text(slide_data['source'])
                bbox = draw.textbbox((0, 0), source_text, font=subtitle_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), source_text,
                         font=subtitle_font_ar, fill=self.colors['white'])

        elif slide_num == 6:  # Quranic verses slide
            if 'verse1' in slide_data:
                verse1_text = self.format_arabic_text(slide_data['verse1'])
                bbox = draw.textbbox((0, 0), verse1_text, font=content_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), verse1_text,
                         font=content_font_ar, fill=self.colors['gold'])
                y_position += 100

            if 'verse1_ref' in slide_data:
                ref1_text = self.format_arabic_text(slide_data['verse1_ref'])
                bbox = draw.textbbox((0, 0), ref1_text, font=subtitle_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), ref1_text,
                         font=subtitle_font_ar, fill=self.colors['white'])
                y_position += 80

            if 'verse2' in slide_data:
                verse2_text = self.format_arabic_text(slide_data['verse2'])
                bbox = draw.textbbox((0, 0), verse2_text, font=content_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), verse2_text,
                         font=content_font_ar, fill=self.colors['gold'])
                y_position += 100

            if 'verse2_ref' in slide_data:
                ref2_text = self.format_arabic_text(slide_data['verse2_ref'])
                bbox = draw.textbbox((0, 0), ref2_text, font=subtitle_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), ref2_text,
                         font=subtitle_font_ar, fill=self.colors['white'])

        elif slide_num == 10:  # Closing slide
            if 'closing_text' in slide_data:
                closing_lines = slide_data['closing_text'].split('\n')
                for line in closing_lines:
                    formatted_line = self.format_arabic_text(line)
                    bbox = draw.textbbox((0, 0), formatted_line, font=content_font_ar)
                    text_width = bbox[2] - bbox[0]
                    x_position = (self.video_width - text_width) // 2
                    draw.text((x_position, y_position), formatted_line,
                             font=content_font_ar, fill=self.colors['white'])
                    y_position += 80

            if 'final_greeting' in slide_data:
                y_position += 50
                greeting_text = self.format_arabic_text(slide_data['final_greeting'])
                bbox = draw.textbbox((0, 0), greeting_text, font=subtitle_font_ar)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), greeting_text,
                         font=subtitle_font_ar, fill=self.colors['gold'])

        else:  # Content slides (Arabic bullet points)
            if 'content' in slide_data:
                for item in slide_data['content']:
                    formatted_item = self.format_arabic_text(item)
                    # Right-align Arabic content with proper Arabic font
                    bbox = draw.textbbox((0, 0), formatted_item, font=content_font_ar)
                    text_width = bbox[2] - bbox[0]
                    x_position = self.video_width - text_width - 100
                    draw.text((x_position, y_position), formatted_item,
                             font=content_font_ar, fill=self.colors['white'])
                    y_position += 60

        return img

    def create_video(self):
        """Create the complete video from slides"""
        print("🎬 Creating Eid al-Ghadir Celebration Video...")

        # Create slide images
        slide_images = []
        for i, slide_data in enumerate(self.slides_data, 1):
            print(f"📄 Creating slide {i}...")
            img = self.create_slide_image(slide_data, i)

            # Save temporary image
            temp_filename = f"temp_ghadir_slide_{i}.png"
            img.save(temp_filename)
            slide_images.append(temp_filename)

        # Create video clips from images
        clips = []
        for i, img_path in enumerate(slide_images):
            print(f"🎞️ Processing slide {i+1} for video...")
            clip = ImageClip(img_path, duration=self.slide_duration)
            clips.append(clip)

        # Add smooth transitions (crossfade)
        print("🎨 Adding smooth transitions...")
        final_clips = []
        for i, clip in enumerate(clips):
            if i == 0:
                # First clip - no transition
                final_clips.append(clip)
            else:
                # Add crossfade transition
                transition_duration = 1.0  # 1 second crossfade

                # Adjust previous clip to overlap
                if len(final_clips) > 0:
                    prev_clip = final_clips[-1]
                    # Reduce previous clip duration by transition time
                    prev_clip = prev_clip.set_duration(prev_clip.duration - transition_duration/2)
                    final_clips[-1] = prev_clip

                # Add current clip with fade in
                current_clip = clip.fadein(transition_duration/2)
                final_clips.append(current_clip)

        # Create final video
        print("🎥 Creating final video...")
        final_video = concatenate_videoclips(final_clips, method="compose")

        # Set video properties
        final_video = final_video.set_fps(self.fps)

        # Export video
        output_filename = "Eid_Ghadir_Video.mp4"
        print(f"💾 Exporting video as {output_filename}...")

        final_video.write_videofile(
            output_filename,
            fps=self.fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            ffmpeg_params=['-crf', '18']  # High quality
        )

        # Clean up temporary files
        print("🧹 Cleaning up temporary files...")
        for img_path in slide_images:
            try:
                os.remove(img_path)
            except:
                pass

        print(f"✅ Video created successfully: {output_filename}")
        print(f"📊 Video specifications:")
        print(f"   • Resolution: {self.video_width}x{self.video_height}")
        print(f"   • Frame Rate: {self.fps} FPS")
        print(f"   • Duration: ~{len(self.slides_data) * self.slide_duration} seconds ({len(self.slides_data) * self.slide_duration / 60:.1f} minutes)")
        print(f"   • Format: MP4 (H.264)")
        print(f"   • Slides: {len(self.slides_data)}")
        print(f"   • Slide Duration: {self.slide_duration} seconds each")

        return output_filename

def main():
    """Main function to create the video"""
    try:
        generator = EidGhadirVideoGenerator()
        video_file = generator.create_video()

        print(f"\n🎉 Successfully created video: {video_file}")
        print("\n🕌 This video is ready for:")
        print("• Eid al-Ghadir celebration gatherings")
        print("• Islamic centers and mosques")
        print("• Educational institutions and Islamic schools")
        print("• Community centers and cultural organizations")
        print("• Social media sharing (WhatsApp, Telegram, Instagram, Facebook)")
        print("• Religious conferences and seminars")
        print("• Digital archives for Islamic commemorations")
        print("• Family and community Eid celebrations")

        print(f"\n📍 File location: {os.path.abspath(video_file)}")
        print("\n🌟 Video features:")
        print("• High-quality 1080p Full HD resolution")
        print("• Enhanced Arabic RTL text rendering with preserved diacritical marks")
        print("• Professional Arabic fonts (Amiri/Scheherazade/Noto Sans Arabic)")
        print("• Proper letter connections and Arabic text shaping")
        print("• Islamic festive color scheme")
        print("• Smooth slide transitions (crossfade effects)")
        print("• 5-second slides optimized for celebration viewing")
        print("• Culturally appropriate for Shia Muslim audiences")
        print("• Professional H.264 encoding for broad compatibility")
        print("• Optimized for social media and presentation systems")
        print("• Bilingual content (Arabic RTL + English)")
        print("• Comprehensive coverage of Eid al-Ghadir significance")
        print("• Superior Arabic typography quality for religious content")

        print("\n📋 Video Content Summary:")
        print("1. Title Slide - Eid al-Ghadir Celebration")
        print("2. Historical Context - Background of Ghadir Khumm Event")
        print("3. Prophetic Declaration - The Famous Hadith of Ghadir")
        print("4. Significance - Importance in Islamic History")
        print("5. Imam Ali's Virtues - Key Qualities and Achievements")
        print("6. Quranic References - Relevant Verses")
        print("7. Congratulatory Messages - Festive Greetings")
        print("8. Spiritual Reflection - Lessons and Teachings")
        print("9. Community Unity - Following Ahl al-Bayt")
        print("10. Closing Slide - Final Congratulations and Prayers")

    except Exception as e:
        print(f"❌ Error creating video: {str(e)}")
        print("Please ensure all required packages are installed:")
        print("pip install moviepy pillow arabic-reshaper python-bidi")

if __name__ == "__main__":
    main()
