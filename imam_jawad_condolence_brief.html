<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعزية العالم الإسلامي - استشهاد الإمام محمد الجواد (عليه السلام)</title>
    <meta name="description" content="رسالة تعزية موجزة للعالم الإسلامي بمناسبة ذكرى استشهاد الإمام محمد الجواد عليه السلام">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Local Fonts Fallback -->
    <style>
        @font-face {
            font-family: 'Amiri-Local';
            src: url('./fonts/Amiri-Regular.ttf') format('truetype');
        }
        @font-face {
            font-family: 'NotoSansArabic-Local';
            src: url('./fonts/NotoSansArabic-Regular.ttf') format('truetype');
        }
    </style>

    <!-- Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'Amiri-Local', 'Noto Sans Arabic', 'NotoSansArabic-Local', serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #f5f5f5;
            direction: rtl;
            text-align: right;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .condolence-card {
            max-width: 600px;
            background: rgba(0, 0, 0, 0.8);
            border: 3px solid #d4af37;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow: hidden;
        }

        .condolence-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.1) 2px, transparent 2px);
            background-size: 50px 50px;
            pointer-events: none;
        }

        .brand-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .brand-logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }

        .brand-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #d4af37;
        }

        .opening-verse {
            font-size: 1.3rem;
            color: #90ee90;
            font-weight: 600;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(144, 238, 144, 0.3);
            position: relative;
            z-index: 2;
        }

        .main-title {
            font-size: 2.5rem;
            color: #d4af37;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .imam-name {
            font-size: 3rem;
            color: #d4af37;
            font-weight: 700;
            margin: 1rem 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            position: relative;
            z-index: 2;
        }

        .honorific {
            display: block;
            font-size: 1.5rem;
            color: #90ee90;
            margin-top: 0.5rem;
        }

        .condolence-text {
            font-size: 1.2rem;
            line-height: 2;
            margin: 2rem 0;
            text-align: justify;
            position: relative;
            z-index: 2;
        }

        .date-martyrdom {
            font-size: 1.4rem;
            color: #d4af37;
            font-weight: 600;
            margin: 2rem 0;
            padding: 1rem;
            background: rgba(212, 175, 55, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(212, 175, 55, 0.3);
            position: relative;
            z-index: 2;
        }

        .closing-prayer {
            font-size: 1.1rem;
            color: #90ee90;
            font-weight: 600;
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(144, 238, 144, 0.3);
            position: relative;
            z-index: 2;
        }

        .footer-brand {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
            font-size: 0.9rem;
            color: #999;
            position: relative;
            z-index: 2;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .condolence-card {
                padding: 2rem;
                margin: 1rem;
            }

            .main-title {
                font-size: 2rem;
            }

            .imam-name {
                font-size: 2.5rem;
            }

            .opening-verse, .condolence-text {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .condolence-card {
                padding: 1.5rem;
            }

            .main-title {
                font-size: 1.8rem;
            }

            .imam-name {
                font-size: 2rem;
            }

            .opening-verse, .condolence-text {
                font-size: 1rem;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white;
                color: black;
            }

            .condolence-card {
                background: white;
                border: 2px solid #000;
                box-shadow: none;
            }

            .condolence-card::before {
                display: none;
            }
        }

        /* Download Button Styles */
        .download-section {
            margin-top: 2rem;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .download-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .download-btn {
            background: linear-gradient(90deg, #d4af37, #b8941f);
            color: #000;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
        }

        .download-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .download-progress {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(144, 238, 144, 0.1);
            border-radius: 10px;
            border: 2px solid rgba(144, 238, 144, 0.3);
            display: none;
            position: relative;
            z-index: 2;
        }

        .progress-text {
            color: #90ee90;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(144, 238, 144, 0.2);
            border-radius: 4px;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #90ee90, #d4af37);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }
    </style>

    <!-- html2canvas Library for Image Generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="condolence-card">
        <!-- Brand Header -->
        <div class="brand-header">
            <img src="app_icon.png" alt="AliToucan" class="brand-logo">
            <span class="brand-name">AliToucan</span>
        </div>

        <!-- Opening Quranic Verse -->
        <div class="opening-verse">
            "وَبَشِّرِ الصَّابِرِينَ * الَّذِينَ إِذَا أَصَابَتْهُم مُّصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ"
        </div>

        <!-- Main Title -->
        <h1 class="main-title">تعزية العالم الإسلامي</h1>

        <!-- Imam Name -->
        <h2 class="imam-name">
            الإمام محمد الجواد
            <span class="honorific">(عليه السلام)</span>
        </h2>

        <!-- Condolence Message -->
        <div class="condolence-text">
            بقلوب مؤمنة بقضاء الله وقدره، نتقدم إلى العالم الإسلامي وجميع المؤمنين في مشارق الأرض ومغاربها
            بأحر التعازي وأصدق المواساة بمناسبة ذكرى استشهاد الإمام التاسع من أئمة أهل البيت الأطهار،
            الإمام أبو جعفر محمد بن علي الجواد (عليه السلام)، الذي استشهد مسموماً وهو في ريعان شبابه،
            تاركاً للأمة إرثاً عظيماً من العلم والحكمة والتقوى.
        </div>

        <!-- Martyrdom Date -->
        <div class="date-martyrdom">
            ٢٩ ذو القعدة الحرام - ذكرى الاستشهاد
        </div>

        <!-- Closing Prayer -->
        <div class="closing-prayer">
            اللهم صل على محمد وآل محمد، وعجل فرج وليك الحجة بن الحسن،
            واجعلنا من أنصاره وأعوانه والذابين عنه
        </div>

        <!-- Footer -->
        <div class="footer-brand">
            © ٢٠٢٤ AliToucan - في ذكرى الإمام الجواد (عليه السلام)
        </div>

        <!-- Download Section -->
        <div class="download-section">
            <h3 style="color: #d4af37; margin-bottom: 1rem;">تحميل الصورة للمشاركة</h3>
            <div class="download-buttons">
                <button class="download-btn" onclick="downloadImage('square', 1080, 1080)">
                    📱 مربع (إنستغرام)
                </button>
                <button class="download-btn" onclick="downloadImage('story', 1080, 1920)">
                    📖 ستوري (عمودي)
                </button>
                <button class="download-btn" onclick="downloadImage('facebook', 1200, 630)">
                    📘 فيسبوك (أفقي)
                </button>
                <button class="download-btn" onclick="downloadImage('print', 2480, 3508)">
                    🖨️ طباعة (A4)
                </button>
                <button class="download-btn" onclick="downloadSimpleImage()" style="background: linear-gradient(90deg, #90ee90, #4a9eff);">
                    📷 تحميل بسيط
                </button>
            </div>

            <!-- Progress Indicator -->
            <div class="download-progress" id="downloadProgress">
                <div class="progress-text" id="progressText">جاري إنشاء الصورة...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Enhanced Functionality -->
    <script>
        // Global variables for image generation
        let isGenerating = false;

        // Progress management
        function showProgress() {
            document.getElementById('downloadProgress').style.display = 'block';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = 'جاري إنشاء الصورة...';
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            if (text) {
                document.getElementById('progressText').textContent = text;
            }
        }

        function hideProgress() {
            setTimeout(() => {
                document.getElementById('downloadProgress').style.display = 'none';
            }, 1000);
        }

        // Disable/Enable download buttons
        function toggleButtons(disabled) {
            const buttons = document.querySelectorAll('.download-btn');
            buttons.forEach(btn => {
                btn.disabled = disabled;
            });
        }

        // Generate Arabic-safe filename
        function generateFilename(format) {
            const date = new Date();
            const timestamp = date.getFullYear() + '-' +
                            String(date.getMonth() + 1).padStart(2, '0') + '-' +
                            String(date.getDate()).padStart(2, '0');

            const formatNames = {
                'square': 'مربع_انستغرام',
                'story': 'ستوري_عمودي',
                'facebook': 'فيسبوك_أفقي',
                'print': 'طباعة_A4'
            };

            return `تعزية_الإمام_الجواد_عليه_السلام_${formatNames[format]}_${timestamp}.png`;
        }

        // Main download function
        async function downloadImage(format, width, height) {
            if (isGenerating) return;

            isGenerating = true;
            toggleButtons(true);
            showProgress();

            try {
                updateProgress(10, 'تحضير العناصر...');

                // Get the condolence card element
                const element = document.querySelector('.condolence-card');

                // Temporarily hide download section for clean image
                const downloadSection = document.querySelector('.download-section');
                const originalDisplay = downloadSection.style.display;
                downloadSection.style.display = 'none';

                updateProgress(30, 'تحسين جودة الخطوط...');

                // Wait for fonts to load
                await document.fonts.ready;

                // Force a small delay to ensure rendering
                await new Promise(resolve => setTimeout(resolve, 500));

                updateProgress(50, 'إنشاء الصورة...');

                // Configure html2canvas options for high quality
                const options = {
                    allowTaint: true,
                    useCORS: true,
                    scale: 3, // Higher scale for better quality
                    width: element.offsetWidth,
                    height: element.offsetHeight,
                    backgroundColor: '#0a0a0a', // Set explicit background
                    logging: true, // Enable logging for debugging
                    imageTimeout: 30000, // Increase timeout
                    removeContainer: false,
                    foreignObjectRendering: false, // Disable for better compatibility
                    onclone: function(clonedDoc) {
                        // Ensure all styles are properly applied in cloned document
                        const clonedElement = clonedDoc.querySelector('.condolence-card');
                        if (clonedElement) {
                            // Apply all necessary styles
                            clonedElement.style.fontFamily = "'Amiri', 'Noto Sans Arabic', serif";
                            clonedElement.style.direction = 'rtl';
                            clonedElement.style.textAlign = 'center';
                            clonedElement.style.color = '#f5f5f5';
                            clonedElement.style.background = 'rgba(0, 0, 0, 0.8)';
                            clonedElement.style.border = '3px solid #d4af37';
                            clonedElement.style.borderRadius = '20px';
                            clonedElement.style.padding = '3rem';
                            clonedElement.style.position = 'relative';

                            // Ensure all text elements have proper styling
                            const textElements = clonedElement.querySelectorAll('*');
                            textElements.forEach(el => {
                                if (el.style) {
                                    el.style.fontFamily = "'Amiri', 'Noto Sans Arabic', serif";
                                    el.style.direction = 'rtl';
                                }
                            });
                        }

                        // Remove any problematic elements
                        const downloadSections = clonedDoc.querySelectorAll('.download-section');
                        downloadSections.forEach(section => section.remove());
                    }
                };

                updateProgress(70, 'معالجة النص العربي...');

                // Generate canvas
                const canvas = await html2canvas(element, options);

                // Check if canvas has content
                if (canvas.width === 0 || canvas.height === 0) {
                    throw new Error('Canvas is empty');
                }

                updateProgress(85, 'تحسين الصورة...');

                // Create final canvas with desired dimensions
                const finalCanvas = document.createElement('canvas');
                finalCanvas.width = width;
                finalCanvas.height = height;
                const ctx = finalCanvas.getContext('2d');

                // Fill background with gradient
                const gradient = ctx.createLinearGradient(0, 0, width, height);
                gradient.addColorStop(0, '#0a0a0a');
                gradient.addColorStop(0.5, '#1a1a2e');
                gradient.addColorStop(1, '#16213e');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, width, height);

                // Calculate scaling and positioning to fit content
                const scale = Math.min(width / canvas.width, height / canvas.height) * 0.85;
                const scaledWidth = canvas.width * scale;
                const scaledHeight = canvas.height * scale;
                const x = (width - scaledWidth) / 2;
                const y = (height - scaledHeight) / 2;

                // Draw the content
                ctx.drawImage(canvas, x, y, scaledWidth, scaledHeight);

                updateProgress(95, 'حفظ الملف...');

                // Convert to blob and download
                finalCanvas.toBlob(function(blob) {
                    if (!blob) {
                        throw new Error('Failed to create blob');
                    }

                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = generateFilename(format);
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Clean up
                    setTimeout(() => {
                        URL.revokeObjectURL(url);
                    }, 1000);

                    updateProgress(100, 'تم التحميل بنجاح! ✅');

                    // Show download section again
                    downloadSection.style.display = originalDisplay;

                    setTimeout(() => {
                        hideProgress();
                        toggleButtons(false);
                        isGenerating = false;
                    }, 1500);

                }, 'image/png', 1.0); // Maximum quality

            } catch (error) {
                console.error('Error generating image:', error);
                updateProgress(0, 'حدث خطأ في إنشاء الصورة: ' + error.message + ' ❌');

                // Show download section again
                const downloadSection = document.querySelector('.download-section');
                downloadSection.style.display = 'block';

                setTimeout(() => {
                    hideProgress();
                    toggleButtons(false);
                    isGenerating = false;
                }, 3000);
            }
        }

        // Simple download function as fallback
        async function downloadSimpleImage() {
            if (isGenerating) return;

            isGenerating = true;
            toggleButtons(true);
            showProgress();

            try {
                updateProgress(20, 'تحضير الصورة البسيطة...');

                const element = document.querySelector('.condolence-card');
                const downloadSection = document.querySelector('.download-section');
                downloadSection.style.display = 'none';

                updateProgress(50, 'إنشاء الصورة...');

                // Simple html2canvas with basic options
                const canvas = await html2canvas(element, {
                    scale: 2,
                    backgroundColor: '#0a0a0a',
                    logging: false,
                    allowTaint: true,
                    useCORS: true
                });

                updateProgress(80, 'تحضير التحميل...');

                // Direct download without resizing
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'تعزية_الإمام_الجواد_عليه_السلام_بسيط.png';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    updateProgress(100, 'تم التحميل بنجاح! ✅');
                    downloadSection.style.display = 'block';

                    setTimeout(() => {
                        hideProgress();
                        toggleButtons(false);
                        isGenerating = false;
                    }, 1500);

                }, 'image/png', 0.9);

            } catch (error) {
                console.error('Simple download error:', error);
                updateProgress(0, 'حدث خطأ في التحميل البسيط ❌');

                const downloadSection = document.querySelector('.download-section');
                downloadSection.style.display = 'block';

                setTimeout(() => {
                    hideProgress();
                    toggleButtons(false);
                    isGenerating = false;
                }, 2000);
            }
        }

        // Add keyboard shortcut for printing (Ctrl+P)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });

        // Preload fonts for better rendering
        document.addEventListener('DOMContentLoaded', function() {
            // Preload Google Fonts
            const link1 = document.createElement('link');
            link1.rel = 'preload';
            link1.href = 'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap';
            link1.as = 'style';
            document.head.appendChild(link1);

            const link2 = document.createElement('link');
            link2.rel = 'preload';
            link2.href = 'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap';
            link2.as = 'style';
            document.head.appendChild(link2);
        });
    </script>
</body>
</html>
