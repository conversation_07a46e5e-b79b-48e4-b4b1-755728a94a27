<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>GDP Analysis Charts</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .chart-container { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .chart-title { text-align: center; color: #2c3e50; font-size: 18px; font-weight: bold; margin-bottom: 20px; }
        canvas { max-height: 400px; }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #2c3e50;">📈 GDP Analysis Charts - الرسوم البيانية لتحليل الناتج المحلي</h1>
    
    <div class="chart-container">
        <div class="chart-title">أكبر 10 اقتصادات في العالم 2024 - Top 10 Economies by GDP 2024</div>
        <canvas id="gdpChart"></canvas>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">نصيب الفرد من الناتج المحلي - GDP Per Capita Comparison</div>
        <canvas id="perCapitaChart"></canvas>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">معدلات النمو الاقتصادي - Economic Growth Rates 2024</div>
        <canvas id="growthChart"></canvas>
    </div>

    <script>
        // GDP Chart
        const gdpCtx = document.getElementById('gdpChart').getContext('2d');
        new Chart(gdpCtx, {
            type: 'bar',
            data: {
                labels: ['USA', 'China', 'Germany', 'India', 'Japan', 'UK', 'France', 'Italy', 'Canada', 'Brazil'],
                datasets: [{
                    label: 'GDP (Trillion USD)',
                    data: [29.18, 18.75, 4.66, 3.91, 4.03, 3.64, 3.16, 2.37, 2.24, 2.17],
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6', '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#2ecc71']
                }]
            },
            options: {
                responsive: true,
                plugins: { legend: { display: false } },
                scales: { y: { beginAtZero: true, title: { display: true, text: 'GDP (Trillion USD)' } } }
            }
        });
        
        // Per Capita Chart
        const perCapitaCtx = document.getElementById('perCapitaChart').getContext('2d');
        new Chart(perCapitaCtx, {
            type: 'horizontalBar',
            data: {
                labels: ['Netherlands', 'Australia', 'Canada', 'Germany', 'UK', 'France', 'Italy', 'Japan', 'Spain', 'South Korea'],
                datasets: [{
                    label: 'GDP Per Capita (USD)',
                    data: [69726, 67564, 57334, 55133, 53844, 46228, 40272, 32656, 36030, 36167],
                    backgroundColor: '#2ecc71'
                }]
            },
            options: {
                responsive: true,
                plugins: { legend: { display: false } },
                scales: { x: { beginAtZero: true, title: { display: true, text: 'GDP Per Capita (USD)' } } }
            }
        });
        
        // Growth Chart
        const growthCtx = document.getElementById('growthChart').getContext('2d');
        new Chart(growthCtx, {
            type: 'bar',
            data: {
                labels: ['India', 'Indonesia', 'China', 'Turkey', 'Russia', 'USA', 'Poland', 'South Korea', 'Spain', 'Canada'],
                datasets: [{
                    label: 'Growth Rate (%)',
                    data: [6.4, 5.0, 5.2, 4.5, 3.6, 2.8, 2.9, 2.6, 2.4, 1.2],
                    backgroundColor: '#27ae60'
                }]
            },
            options: {
                responsive: true,
                plugins: { legend: { display: false } },
                scales: { y: { beginAtZero: true, title: { display: true, text: 'Growth Rate (%)' } } }
            }
        });
    </script>
</body>
</html>