#!/usr/bin/env python3
"""
Auto Upload Script for Professional Image Editor
Automatically uploads the application to various cloud services
"""

import os
import sys
import webbrowser
import urllib.parse
from pathlib import Path
import subprocess
import json

def check_file_exists():
    """Check if the distribution file exists"""
    zip_file = "Professional_Image_Editor_v1.0.0_20250526.zip"
    if Path(zip_file).exists():
        file_size = Path(zip_file).stat().st_size
        size_mb = file_size / (1024 * 1024)
        print(f"✓ Found file: {zip_file}")
        print(f"  Size: {size_mb:.1f} MB")
        return zip_file
    else:
        print(f"✗ File not found: {zip_file}")
        print("Please run create_release.py first to create the distribution package")
        return None

def open_google_drive():
    """Open Google Drive upload page"""
    print("Opening Google Drive...")
    webbrowser.open("https://drive.google.com/drive/my-drive")
    print("\nInstructions for Google Drive:")
    print("1. Click 'New' → 'File upload'")
    print("2. Select the ZIP file")
    print("3. Wait for upload to complete")
    print("4. Right-click the file → 'Share'")
    print("5. Change to 'Anyone with the link can view'")
    print("6. Copy the link and share with friends")

def open_onedrive():
    """Open OneDrive upload page"""
    print("Opening OneDrive...")
    webbrowser.open("https://onedrive.live.com")
    print("\nInstructions for OneDrive:")
    print("1. Click 'Upload' → 'Files'")
    print("2. Select the ZIP file")
    print("3. Wait for upload to complete")
    print("4. Right-click the file → 'Share'")
    print("5. Copy the link and share with friends")

def open_dropbox():
    """Open Dropbox upload page"""
    print("Opening Dropbox...")
    webbrowser.open("https://www.dropbox.com/upload")
    print("\nInstructions for Dropbox:")
    print("1. Drag the ZIP file to the browser")
    print("2. Wait for upload to complete")
    print("3. Click 'Share' → 'Create link'")
    print("4. Copy the link and share with friends")

def open_mediafire():
    """Open MediaFire upload page"""
    print("Opening MediaFire...")
    webbrowser.open("https://www.mediafire.com/upgrade/?utm_source=application&utm_medium=web&utm_campaign=upload_tab")
    print("\nInstructions for MediaFire:")
    print("1. Click 'Upload'")
    print("2. Select the ZIP file or drag it")
    print("3. Wait for upload to complete")
    print("4. Copy the download link")

def open_wetransfer():
    """Open WeTransfer upload page"""
    print("Opening WeTransfer...")
    webbrowser.open("https://wetransfer.com/")
    print("\nInstructions for WeTransfer:")
    print("1. Click 'Add files'")
    print("2. Select the ZIP file")
    print("3. Enter your email and recipient emails")
    print("4. Click 'Transfer'")
    print("Note: Files are deleted after 7 days")

def create_github_release_guide():
    """Create guide for GitHub release"""
    guide = """
# GitHub Release Guide

## Prerequisites
- GitHub account
- Repository for the project

## Steps:
1. Go to your repository on GitHub
2. Click "Releases" → "Create a new release"
3. Tag version: v1.0.0
4. Release title: "Professional Image Editor v1.0.0"
5. Description:
   ```
   🎨 Professional Image Editor with Arabic Support
   
   Features:
   - Professional image editing tools
   - Full Arabic RTL text support
   - Layer management system
   - Batch processing
   - Social media export presets
   - Standalone executable (no Python required)
   
   System Requirements:
   - Windows 10 or later
   - 4GB RAM minimum
   - 200MB disk space
   
   Download the ZIP file below and extract it.
   Run install.bat as administrator for system installation,
   or run_portable.bat for portable mode.
   ```
6. Attach the ZIP file as a binary
7. Click "Publish release"

## Direct Download Link:
The download link will be:
https://github.com/USERNAME/REPOSITORY/releases/download/v1.0.0/Professional_Image_Editor_v1.0.0_20250526.zip
"""
    
    with open('github_release_guide.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✓ Created GitHub release guide: github_release_guide.md")

def generate_share_message():
    """Generate ready-to-share message"""
    message = """🎨 المحرر الاحترافي للصور مع دعم اللغة العربية

السلام عليكم أصدقائي! 

أشارككم تطبيق محرر الصور الاحترافي الذي طورته - يدعم اللغة العربية بالكامل مع الكتابة من اليمين لليسار!

🔗 رابط التحميل: [ضع الرابط هنا]

✨ الميزات الرئيسية:
• تحرير احترافي للصور (قص، تدوير، تغيير الحجم)
• دعم كامل للنص العربي (RTL) مع خطوط عالية الجودة
• نظام طبقات متعدد مع أنماط المزج
• مرشحات ومؤثرات فنية (vintage، sepia، dramatic)
• أدوات رسم وتعليق متقدمة
• معالجة مجمعة للصور المتعددة
• إعدادات تصدير لوسائل التواصل الاجتماعي
• لا يحتاج تثبيت Python أو أي برامج إضافية!

📦 معلومات التحميل:
• حجم الملف: 69 MB
• متوافق مع: Windows 10 وأحدث
• مجاني بالكامل
• آمن ومختبر

📋 طريقة التشغيل:
1. حمل الملف المضغوط من الرابط أعلاه
2. استخرج محتويات الملف المضغوط
3. للتثبيت: شغل install.bat كمدير
4. للنسخة المحمولة: شغل run_portable.bat

🎯 مثالي للمصممين والمصورين والمحتوى العربي!

استمتعوا بالتحرير واتمنى ينال اعجابكم! 🎉

#تصميم #تحرير_الصور #عربي #مجاني #برمجة"""

    with open('share_message.txt', 'w', encoding='utf-8') as f:
        f.write(message)
    
    print("✓ Created share message: share_message.txt")
    return message

def create_qr_code():
    """Create QR code for easy sharing (if qrcode library is available)"""
    try:
        import qrcode
        
        # This will be updated with the actual download link
        download_url = "https://github.com/YOUR_USERNAME/YOUR_REPO/releases/download/v1.0.0/Professional_Image_Editor_v1.0.0_20250526.zip"
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(download_url)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        img.save("download_qr_code.png")
        
        print("✓ Created QR code: download_qr_code.png")
        print("  (Update the URL in the code with your actual download link)")
        
    except ImportError:
        print("⚠ QR code library not available")
        print("  Install with: pip install qrcode[pil]")

def show_upload_options():
    """Show upload options menu"""
    print("\n" + "="*60)
    print("🚀 UPLOAD OPTIONS FOR PROFESSIONAL IMAGE EDITOR")
    print("="*60)
    
    options = {
        '1': ('Google Drive (Recommended)', open_google_drive),
        '2': ('OneDrive', open_onedrive),
        '3': ('Dropbox', open_dropbox),
        '4': ('MediaFire', open_mediafire),
        '5': ('WeTransfer (7-day limit)', open_wetransfer),
        '6': ('Create GitHub Release Guide', create_github_release_guide),
        '7': ('Generate Share Message', lambda: print(generate_share_message())),
        '8': ('Create QR Code', create_qr_code),
        '9': ('Open All Upload Sites', lambda: [open_google_drive(), open_onedrive(), open_dropbox()]),
    }
    
    print("\nChoose an upload option:")
    for key, (description, _) in options.items():
        print(f"  {key}. {description}")
    
    print("  0. Exit")
    
    while True:
        choice = input("\nEnter your choice (0-9): ").strip()
        
        if choice == '0':
            print("Goodbye!")
            break
        elif choice in options:
            print(f"\n{options[choice][0]}:")
            print("-" * 40)
            options[choice][1]()
            
            input("\nPress Enter to continue...")
            break
        else:
            print("Invalid choice. Please try again.")

def main():
    """Main upload assistant"""
    print("Professional Image Editor - Upload Assistant")
    print("=" * 50)
    
    # Check if file exists
    zip_file = check_file_exists()
    if not zip_file:
        return
    
    # Generate share message
    generate_share_message()
    
    # Show upload options
    show_upload_options()
    
    print("\n" + "="*50)
    print("📋 QUICK REFERENCE:")
    print("• File to upload: Professional_Image_Editor_v1.0.0_20250526.zip")
    print("• File size: ~69 MB")
    print("• Recommended: Google Drive (most reliable)")
    print("• Share message: check share_message.txt")
    print("• After upload: test the download link before sharing")
    print("\n🎉 Happy sharing!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nUpload assistant cancelled by user.")
    except Exception as e:
        print(f"\nError: {e}")
        input("Press Enter to exit...")
