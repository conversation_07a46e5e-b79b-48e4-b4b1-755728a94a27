#!/usr/bin/env python3
"""
TikTok Image Enhancement Tool
Optimizes images for TikTok posting with Arabic text support and social media enhancements
"""

import os
from PIL import Image, ImageDraw, ImageFont, ImageEnhance, ImageFilter
import numpy as np
from arabic_reshaper import arabic_reshaper
from bidi.algorithm import get_display
import requests
from io import BytesIO

class TikTokImageEnhancer:
    def __init__(self):
        self.tiktok_size = (1080, 1920)  # 9:16 aspect ratio
        self.arabic_fonts = [
            "fonts/Amiri-Regular.ttf",
            "fonts/Scheherazade-Regular.ttf", 
            "fonts/NotoSansArabic-Regular.ttf"
        ]
        self.setup_fonts()
    
    def setup_fonts(self):
        """Setup Arabic fonts directory"""
        os.makedirs("fonts", exist_ok=True)
        
        # Download Arabic fonts if not available
        font_urls = {
            "fonts/Amiri-Regular.ttf": "https://github.com/aliftype/amiri/raw/main/Amiri-Regular.ttf",
            "fonts/NotoSansArabic-Regular.ttf": "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSansArabic/NotoSansArabic-Regular.ttf"
        }
        
        for font_path, url in font_urls.items():
            if not os.path.exists(font_path):
                try:
                    response = requests.get(url)
                    with open(font_path, 'wb') as f:
                        f.write(response.content)
                    print(f"Downloaded {font_path}")
                except Exception as e:
                    print(f"Could not download {font_path}: {e}")
    
    def get_arabic_font(self, size=40):
        """Get the best available Arabic font"""
        for font_path in self.arabic_fonts:
            if os.path.exists(font_path):
                try:
                    return ImageFont.truetype(font_path, size)
                except:
                    continue
        
        # Fallback to default font
        try:
            return ImageFont.truetype("arial.ttf", size)
        except:
            return ImageFont.load_default()
    
    def process_arabic_text(self, text):
        """Process Arabic text for proper RTL display"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    def enhance_image_quality(self, image):
        """Enhance image quality for social media"""
        # Increase sharpness
        sharpness_enhancer = ImageEnhance.Sharpness(image)
        image = sharpness_enhancer.enhance(1.2)
        
        # Increase contrast
        contrast_enhancer = ImageEnhance.Contrast(image)
        image = contrast_enhancer.enhance(1.15)
        
        # Increase color saturation
        color_enhancer = ImageEnhance.Color(image)
        image = color_enhancer.enhance(1.1)
        
        # Slight brightness adjustment
        brightness_enhancer = ImageEnhance.Brightness(image)
        image = brightness_enhancer.enhance(1.05)
        
        return image
    
    def resize_for_tiktok(self, image):
        """Resize image to TikTok's 9:16 aspect ratio"""
        # Calculate the best fit while maintaining aspect ratio
        img_width, img_height = image.size
        target_width, target_height = self.tiktok_size
        
        # Calculate scaling factors
        scale_width = target_width / img_width
        scale_height = target_height / img_height
        scale = max(scale_width, scale_height)
        
        # Resize image
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Create new image with TikTok dimensions
        tiktok_image = Image.new('RGB', self.tiktok_size, (0, 0, 0))
        
        # Center the resized image
        x_offset = (target_width - new_width) // 2
        y_offset = (target_height - new_height) // 2
        tiktok_image.paste(resized_image, (x_offset, y_offset))
        
        return tiktok_image
    
    def add_gradient_overlay(self, image, opacity=0.3):
        """Add subtle gradient overlay for better text visibility"""
        overlay = Image.new('RGBA', image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(overlay)
        
        # Create gradient from top to bottom
        for y in range(image.height):
            alpha = int(255 * opacity * (y / image.height))
            draw.rectangle([(0, y), (image.width, y+1)], fill=(0, 0, 0, alpha))
        
        # Blend with original image
        return Image.alpha_composite(image.convert('RGBA'), overlay).convert('RGB')
    
    def add_branding(self, image):
        """Add AliToucan branding"""
        draw = ImageDraw.Draw(image)
        
        # Brand text
        brand_text = "AliToucan"
        font = self.get_arabic_font(30)
        
        # Position at bottom right
        text_bbox = draw.textbbox((0, 0), brand_text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        x = image.width - text_width - 20
        y = image.height - text_height - 20
        
        # Add text shadow
        draw.text((x+2, y+2), brand_text, font=font, fill=(0, 0, 0, 128))
        draw.text((x, y), brand_text, font=font, fill=(255, 255, 255))
        
        return image
    
    def add_engagement_elements(self, image):
        """Add visual elements that increase TikTok engagement"""
        draw = ImageDraw.Draw(image)
        
        # Add subtle border frame
        border_width = 8
        border_color = (255, 215, 0)  # Gold color
        
        # Top border
        draw.rectangle([0, 0, image.width, border_width], fill=border_color)
        # Bottom border  
        draw.rectangle([0, image.height-border_width, image.width, image.height], fill=border_color)
        # Left border
        draw.rectangle([0, 0, border_width, image.height], fill=border_color)
        # Right border
        draw.rectangle([image.width-border_width, 0, image.width, image.height], fill=border_color)
        
        return image
    
    def enhance_for_tiktok(self, input_path, output_path="enhanced_tiktok_image.jpg"):
        """Main function to enhance image for TikTok"""
        try:
            # Load image
            if input_path.startswith('http'):
                response = requests.get(input_path)
                image = Image.open(BytesIO(response.content))
            else:
                image = Image.open(input_path)
            
            print(f"Original image size: {image.size}")
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Enhance image quality
            image = self.enhance_image_quality(image)
            print("✓ Enhanced image quality")
            
            # Resize for TikTok
            image = self.resize_for_tiktok(image)
            print(f"✓ Resized to TikTok format: {image.size}")
            
            # Add gradient overlay for better text visibility
            image = self.add_gradient_overlay(image, opacity=0.2)
            print("✓ Added gradient overlay")
            
            # Add engagement elements
            image = self.add_engagement_elements(image)
            print("✓ Added engagement elements")
            
            # Add branding
            image = self.add_branding(image)
            print("✓ Added AliToucan branding")
            
            # Save enhanced image
            image.save(output_path, 'JPEG', quality=95, optimize=True)
            print(f"✓ Enhanced image saved as: {output_path}")
            
            return output_path
            
        except Exception as e:
            print(f"Error enhancing image: {e}")
            return None

def main():
    enhancer = TikTokImageEnhancer()
    
    # You can provide the image path here
    input_image = input("Enter image path or URL: ").strip()
    
    if not input_image:
        print("Please provide an image path or URL")
        return
    
    output_file = enhancer.enhance_for_tiktok(input_image)
    
    if output_file:
        print(f"\n🎉 Successfully enhanced image for TikTok!")
        print(f"📱 Output file: {output_file}")
        print(f"📐 Format: 9:16 vertical (1080x1920)")
        print(f"✨ Optimized for mobile viewing and engagement")

if __name__ == "__main__":
    main()
