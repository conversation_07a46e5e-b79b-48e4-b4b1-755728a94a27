#!/usr/bin/env python3
"""
Simple script to enhance your actual image for TikTok
Just save your image in this folder and run this script!
"""

import os
import sys
from tiktok_image_enhancer import TikTokImageEnhancer

def main():
    print("🎬 TikTok Image Enhancement Tool")
    print("=" * 50)
    print("📱 Optimizing your image for TikTok success!")
    print()
    
    # Look for image files in current directory
    image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')
    image_files = [f for f in os.listdir('.') if f.lower().endswith(image_extensions) and not f.startswith('enhanced_') and not f.startswith('sample_')]
    
    if not image_files:
        print("❌ No image files found in the current directory.")
        print("📁 Please save your image file here and run the script again.")
        print("✅ Supported formats: JPG, PNG, GIF, BMP, WebP")
        return
    
    print("📁 Found these images:")
    for i, file in enumerate(image_files, 1):
        file_size = os.path.getsize(file) / 1024  # KB
        print(f"   {i}. {file} ({file_size:.1f} KB)")
    
    print()
    
    # Get user selection
    if len(image_files) == 1:
        selected_file = image_files[0]
        print(f"🎯 Auto-selected: {selected_file}")
    else:
        try:
            choice = input(f"Select image (1-{len(image_files)}): ").strip()
            if not choice.isdigit() or not (1 <= int(choice) <= len(image_files)):
                print("❌ Invalid selection. Please run again.")
                return
            selected_file = image_files[int(choice) - 1]
        except KeyboardInterrupt:
            print("\n👋 Cancelled by user")
            return
    
    print(f"\n🔄 Processing: {selected_file}")
    print("⏳ This may take a few moments...")
    
    # Initialize enhancer
    enhancer = TikTokImageEnhancer()
    
    # Generate output filename
    name, ext = os.path.splitext(selected_file)
    output_file = f"{name}_tiktok_enhanced.jpg"
    
    # Enhance the image
    try:
        result = enhancer.enhance_for_tiktok(selected_file, output_file)
        
        if result:
            print(f"\n🎉 SUCCESS! Your TikTok-optimized image is ready!")
            print(f"📂 Output file: {result}")
            print(f"📐 Format: 9:16 vertical (1080x1920 pixels)")
            print(f"📱 Optimized for mobile viewing")
            
            # Show file sizes
            original_size = os.path.getsize(selected_file) / 1024
            enhanced_size = os.path.getsize(result) / 1024
            print(f"\n📊 File sizes:")
            print(f"   Original: {original_size:.1f} KB")
            print(f"   Enhanced: {enhanced_size:.1f} KB")
            
            print(f"\n✨ Enhancements applied:")
            print(f"   ✅ Converted to TikTok 9:16 format")
            print(f"   ✅ Enhanced brightness, contrast & saturation")
            print(f"   ✅ Added golden engagement border")
            print(f"   ✅ Added AliToucan branding")
            print(f"   ✅ Optimized for Arabic text (if present)")
            print(f"   ✅ Mobile-optimized quality")
            
            print(f"\n🚀 Ready to upload to TikTok!")
            
        else:
            print("❌ Enhancement failed. Please check the image file and try again.")
            
    except Exception as e:
        print(f"❌ Error during enhancement: {e}")
        print("💡 Make sure the image file is not corrupted and try again.")

if __name__ == "__main__":
    main()
