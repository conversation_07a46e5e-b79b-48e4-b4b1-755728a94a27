<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل الصورة</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- html2canvas Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <style>
        body {
            font-family: 'Amiri', 'Noto Sans Arabic', serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #f5f5f5;
            direction: rtl;
            text-align: center;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .test-card {
            max-width: 500px;
            background: rgba(0, 0, 0, 0.8);
            border: 3px solid #d4af37;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .test-title {
            font-size: 2rem;
            color: #d4af37;
            margin-bottom: 1rem;
        }

        .test-content {
            font-size: 1.2rem;
            line-height: 1.8;
            margin: 1rem 0;
        }

        .download-btn {
            background: linear-gradient(90deg, #d4af37, #b8941f);
            color: #000;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 0.5rem;
            font-family: inherit;
        }

        .download-btn:hover {
            transform: translateY(-2px);
        }

        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 10px;
            display: none;
        }

        .success {
            background: rgba(144, 238, 144, 0.2);
            border: 2px solid rgba(144, 238, 144, 0.5);
            color: #90ee90;
        }

        .error {
            background: rgba(255, 100, 100, 0.2);
            border: 2px solid rgba(255, 100, 100, 0.5);
            color: #ff6464;
        }
    </style>
</head>
<body>
    <div class="test-card" id="testCard">
        <h1 class="test-title">اختبار تحميل الصورة</h1>
        <div class="test-content">
            <p>الإمام محمد الجواد (عليه السلام)</p>
            <p>هذا اختبار لتحميل الصورة مع النص العربي</p>
            <p>٢٩ ذو القعدة - ذكرى الاستشهاد</p>
        </div>
        
        <button class="download-btn" onclick="testDownload1()">
            📱 اختبار 1 - بسيط
        </button>
        
        <button class="download-btn" onclick="testDownload2()">
            🖼️ اختبار 2 - متقدم
        </button>
        
        <button class="download-btn" onclick="testDownload3()">
            📷 اختبار 3 - مباشر
        </button>
        
        <div class="status" id="status"></div>
    </div>

    <script>
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (isError ? 'error' : 'success');
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        // Test 1: Simple download
        async function testDownload1() {
            try {
                showStatus('بدء الاختبار الأول...');
                
                const element = document.getElementById('testCard');
                
                const canvas = await html2canvas(element, {
                    scale: 1,
                    backgroundColor: '#0a0a0a'
                });
                
                canvas.toBlob(function(blob) {
                    if (blob) {
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'اختبار_1_بسيط.png';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        URL.revokeObjectURL(url);
                        showStatus('نجح الاختبار الأول! ✅');
                    } else {
                        showStatus('فشل في إنشاء الصورة', true);
                    }
                });
                
            } catch (error) {
                showStatus('خطأ في الاختبار الأول: ' + error.message, true);
                console.error('Test 1 error:', error);
            }
        }

        // Test 2: Advanced download
        async function testDownload2() {
            try {
                showStatus('بدء الاختبار الثاني...');
                
                const element = document.getElementById('testCard');
                
                const canvas = await html2canvas(element, {
                    scale: 2,
                    backgroundColor: '#0a0a0a',
                    allowTaint: true,
                    useCORS: true,
                    logging: true
                });
                
                // Create final canvas
                const finalCanvas = document.createElement('canvas');
                finalCanvas.width = 1080;
                finalCanvas.height = 1080;
                const ctx = finalCanvas.getContext('2d');
                
                // Fill background
                const gradient = ctx.createLinearGradient(0, 0, 1080, 1080);
                gradient.addColorStop(0, '#0a0a0a');
                gradient.addColorStop(0.5, '#1a1a2e');
                gradient.addColorStop(1, '#16213e');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 1080, 1080);
                
                // Scale and center the content
                const scale = Math.min(1080 / canvas.width, 1080 / canvas.height) * 0.8;
                const scaledWidth = canvas.width * scale;
                const scaledHeight = canvas.height * scale;
                const x = (1080 - scaledWidth) / 2;
                const y = (1080 - scaledHeight) / 2;
                
                ctx.drawImage(canvas, x, y, scaledWidth, scaledHeight);
                
                finalCanvas.toBlob(function(blob) {
                    if (blob) {
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'اختبار_2_متقدم.png';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        URL.revokeObjectURL(url);
                        showStatus('نجح الاختبار الثاني! ✅');
                    } else {
                        showStatus('فشل في إنشاء الصورة المتقدمة', true);
                    }
                });
                
            } catch (error) {
                showStatus('خطأ في الاختبار الثاني: ' + error.message, true);
                console.error('Test 2 error:', error);
            }
        }

        // Test 3: Direct canvas download
        function testDownload3() {
            try {
                showStatus('بدء الاختبار الثالث...');
                
                // Create a simple canvas with text
                const canvas = document.createElement('canvas');
                canvas.width = 800;
                canvas.height = 600;
                const ctx = canvas.getContext('2d');
                
                // Fill background
                const gradient = ctx.createLinearGradient(0, 0, 800, 600);
                gradient.addColorStop(0, '#0a0a0a');
                gradient.addColorStop(1, '#1a1a2e');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 800, 600);
                
                // Add text
                ctx.fillStyle = '#d4af37';
                ctx.font = '48px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('اختبار النص العربي', 400, 200);
                
                ctx.fillStyle = '#f5f5f5';
                ctx.font = '32px Arial';
                ctx.fillText('الإمام محمد الجواد (عليه السلام)', 400, 300);
                
                ctx.fillStyle = '#90ee90';
                ctx.font = '24px Arial';
                ctx.fillText('٢٩ ذو القعدة - ذكرى الاستشهاد', 400, 400);
                
                canvas.toBlob(function(blob) {
                    if (blob) {
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'اختبار_3_مباشر.png';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        URL.revokeObjectURL(url);
                        showStatus('نجح الاختبار الثالث! ✅');
                    } else {
                        showStatus('فشل في إنشاء الصورة المباشرة', true);
                    }
                });
                
            } catch (error) {
                showStatus('خطأ في الاختبار الثالث: ' + error.message, true);
                console.error('Test 3 error:', error);
            }
        }
    </script>
</body>
</html>
