<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معركة الحيانية ضد أبو الخصيب - لعبة الحرب التفاعلية</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>', 'Scheherazade New', serif;
            background: linear-gradient(135deg, #2c1810 0%, #4a3728 50%, #6b5b47 100%);
            color: #f4f1e8;
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        .game-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: auto 1fr auto;
            gap: 20px;
            min-height: 100vh;
        }

        .game-header {
            grid-column: 1 / -1;
            text-align: center;
            background: linear-gradient(45deg, #8b4513, #a0522d);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            border: 2px solid #d4af37;
        }

        .game-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 10px;
        }

        .game-subtitle {
            font-size: 1.2rem;
            color: #f4f1e8;
            opacity: 0.9;
        }

        .team-panel {
            background: rgba(0,0,0,0.4);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid;
            backdrop-filter: blur(10px);
        }

        .hayaniya-panel {
            border-color: #4169e1;
            background: linear-gradient(135deg, rgba(65,105,225,0.2), rgba(30,144,255,0.1));
        }

        .abu-khaseeb-panel {
            border-color: #dc143c;
            background: linear-gradient(135deg, rgba(220,20,60,0.2), rgba(178,34,34,0.1));
        }

        .team-name {
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 10px;
        }

        .hayaniya-panel .team-name {
            background: linear-gradient(45deg, #4169e1, #1e90ff);
            color: white;
        }

        .abu-khaseeb-panel .team-name {
            background: linear-gradient(45deg, #dc143c, #b22222);
            color: white;
        }

        .battlefield {
            background: linear-gradient(45deg, #3e2723, #5d4037);
            border-radius: 15px;
            padding: 20px;
            border: 3px solid #8b4513;
            box-shadow: inset 0 0 50px rgba(0,0,0,0.5);
        }

        .game-board {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(6, 1fr);
            gap: 3px;
            background: #2e1a0f;
            padding: 15px;
            border-radius: 10px;
            aspect-ratio: 4/3;
            max-height: 500px;
        }

        .cell {
            background: linear-gradient(135deg, #8d6e63, #a1887f);
            border: 1px solid #5d4037;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-height: 60px;
        }

        .cell:hover {
            background: linear-gradient(135deg, #a1887f, #bcaaa4);
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(212,175,55,0.4);
        }

        .cell.selected {
            background: linear-gradient(135deg, #d4af37, #ffd700);
            box-shadow: 0 0 20px rgba(212,175,55,0.8);
        }

        .cell.possible-move {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
            animation: pulse 1s infinite;
        }

        .cell.enemy-target {
            background: linear-gradient(135deg, #f44336, #ef5350);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .unit {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
            border: 2px solid;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .unit:hover {
            transform: scale(1.1);
        }

        .hayaniya-unit {
            background: linear-gradient(135deg, #4169e1, #1e90ff);
            border-color: #0000cd;
            color: white;
        }

        .abu-khaseeb-unit {
            background: linear-gradient(135deg, #dc143c, #b22222);
            border-color: #8b0000;
            color: white;
        }

        .unit-stats {
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .stat-label {
            color: #d4af37;
            font-weight: 600;
        }

        .stat-value {
            color: #f4f1e8;
            font-weight: 700;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-family: 'Amiri', serif;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, #d4af37, #ffd700);
            color: #2c1810;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(212,175,55,0.4);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #8b4513, #a0522d);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(45deg, #a0522d, #cd853f);
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .game-log {
            grid-column: 1 / -1;
            background: rgba(0,0,0,0.4);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #8b4513;
            max-height: 150px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 5px;
            background: rgba(139,69,19,0.2);
            border-right: 4px solid #d4af37;
        }

        .turn-indicator {
            text-align: center;
            font-size: 1.3rem;
            font-weight: 700;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .hayaniya-turn {
            background: linear-gradient(45deg, #4169e1, #1e90ff);
            color: white;
        }

        .abu-khaseeb-turn {
            background: linear-gradient(45deg, #dc143c, #b22222);
            color: white;
        }

        .victory-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .victory-content {
            background: linear-gradient(135deg, #2c1810, #4a3728);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            border: 3px solid #d4af37;
            max-width: 500px;
        }

        .victory-title {
            font-size: 2.5rem;
            color: #d4af37;
            margin-bottom: 20px;
        }

        .victory-message {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        @media (max-width: 1200px) {
            .game-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
            }

            .team-panel {
                order: 1;
            }

            .battlefield {
                order: 2;
            }

            .game-log {
                order: 3;
            }
        }

        @media (max-width: 768px) {
            .game-title {
                font-size: 1.8rem;
            }

            .game-board {
                gap: 2px;
                padding: 10px;
            }

            .cell {
                min-height: 45px;
            }

            .unit {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
            }
        }

        .branding {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.7);
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #d4af37;
            border: 1px solid #d4af37;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Game Header -->
        <div class="game-header">
            <h1 class="game-title">معركة الحيانية ضد أبو الخصيب</h1>
            <p class="game-subtitle">لعبة الحرب الاستراتيجية التفاعلية</p>
        </div>

        <!-- Al-Hayaniya Team Panel -->
        <div class="team-panel hayaniya-panel">
            <div class="team-name">فريق الحيانية</div>

            <div class="turn-indicator" id="turnIndicator">
                دور فريق الحيانية
            </div>

            <div class="unit-stats">
                <h3 style="color: #d4af37; margin-bottom: 15px; text-align: center;">إحصائيات الفريق</h3>
                <div class="stat-row">
                    <span class="stat-label">الوحدات المتبقية:</span>
                    <span class="stat-value" id="hayaniyaUnits">8</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">النقاط:</span>
                    <span class="stat-value" id="hayaniyaScore">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">القوة الإجمالية:</span>
                    <span class="stat-value" id="hayaniyaPower">80</span>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="resetGame()">لعبة جديدة</button>
                <button class="btn btn-secondary" onclick="showInstructions()">التعليمات</button>
                <button class="btn btn-secondary" onclick="toggleSound()">الصوت: تشغيل</button>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: rgba(65,105,225,0.2); border-radius: 10px;">
                <h4 style="color: #4169e1; margin-bottom: 10px;">خصائص الفريق:</h4>
                <ul style="list-style: none; padding: 0;">
                    <li>• سرعة حركة عادية</li>
                    <li>• قوة هجوم متوسطة</li>
                    <li>• دفاع متوازن</li>
                    <li>• تكتيكات مرنة</li>
                </ul>
            </div>
        </div>

        <!-- Battlefield -->
        <div class="battlefield">
            <div class="game-board" id="gameBoard">
                <!-- Game cells will be generated by JavaScript -->
            </div>

            <div style="text-align: center; margin-top: 15px;">
                <div style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 8px; display: inline-block;">
                    <span style="color: #d4af37; font-weight: 600;">الوحدة المحددة: </span>
                    <span id="selectedUnitInfo" style="color: #f4f1e8;">لا توجد وحدة محددة</span>
                </div>
            </div>
        </div>

        <!-- Abu Al-Khaseeb Team Panel -->
        <div class="team-panel abu-khaseeb-panel">
            <div class="team-name">فريق أبو الخصيب</div>

            <div class="unit-stats">
                <h3 style="color: #d4af37; margin-bottom: 15px; text-align: center;">إحصائيات الفريق</h3>
                <div class="stat-row">
                    <span class="stat-label">الوحدات المتبقية:</span>
                    <span class="stat-value" id="abuKhaseebUnits">8</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">النقاط:</span>
                    <span class="stat-value" id="abuKhaseebScore">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">القوة الإجمالية:</span>
                    <span class="stat-value" id="abuKhaseebPower">120</span>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: rgba(220,20,60,0.2); border-radius: 10px;">
                <h4 style="color: #dc143c; margin-bottom: 10px;">مزايا الفريق:</h4>
                <ul style="list-style: none; padding: 0;">
                    <li>• قوة هجوم عالية</li>
                    <li>• دفاع محصن</li>
                    <li>• وحدات متخصصة</li>
                    <li>• تكتيكات متقدمة</li>
                    <li>• موارد إضافية</li>
                </ul>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: rgba(139,69,19,0.3); border-radius: 10px;">
                <h4 style="color: #d4af37; margin-bottom: 10px;">أنواع الوحدات:</h4>
                <div style="font-size: 0.9rem; line-height: 1.6;">
                    <div><span style="color: #4169e1;">🛡️ مشاة:</span> دفاع قوي</div>
                    <div><span style="color: #dc143c;">⚔️ مقاتلين:</span> هجوم سريع</div>
                    <div><span style="color: #ffd700;">🏹 رماة:</span> مدى بعيد</div>
                    <div><span style="color: #ff6b35;">🐎 فرسان:</span> حركة سريعة</div>
                </div>
            </div>
        </div>

        <!-- Game Log -->
        <div class="game-log">
            <h3 style="color: #d4af37; margin-bottom: 15px; text-align: center;">سجل المعركة</h3>
            <div id="gameLog">
                <div class="log-entry">مرحباً بكم في معركة الحيانية ضد أبو الخصيب! اختر وحدة للبدء.</div>
            </div>
        </div>
    </div>

    <!-- Victory Modal -->
    <div class="victory-modal" id="victoryModal">
        <div class="victory-content">
            <h2 class="victory-title" id="victoryTitle">انتهت المعركة!</h2>
            <p class="victory-message" id="victoryMessage"></p>
            <button class="btn btn-primary" onclick="resetGame(); closeVictoryModal();">معركة جديدة</button>
        </div>
    </div>

    <!-- Branding -->
    <div class="branding">
        AliToucan Gaming
    </div>

    <script>
        // Game State
        let gameState = {
            currentPlayer: 'hayaniya', // 'hayaniya' or 'abukhaseeb'
            selectedUnit: null,
            selectedCell: null,
            gameBoard: Array(6).fill().map(() => Array(8).fill(null)),
            gameOver: false,
            soundEnabled: true
        };

        // Unit Types
        const unitTypes = {
            infantry: { symbol: '🛡️', attack: 10, defense: 15, health: 100, range: 1, movement: 1 },
            fighter: { symbol: '⚔️', attack: 20, defense: 8, health: 80, range: 1, movement: 2 },
            archer: { symbol: '🏹', attack: 15, defense: 5, health: 60, range: 3, movement: 1 },
            cavalry: { symbol: '🐎', attack: 18, defense: 10, health: 90, range: 1, movement: 3 }
        };

        // Team configurations
        const teams = {
            hayaniya: {
                name: 'الحيانية',
                color: 'hayaniya',
                units: [
                    { type: 'infantry', attack: 10, defense: 15, health: 100 },
                    { type: 'fighter', attack: 20, defense: 8, health: 80 },
                    { type: 'archer', attack: 15, defense: 5, health: 60 },
                    { type: 'cavalry', attack: 18, defense: 10, health: 90 }
                ]
            },
            abukhaseeb: {
                name: 'أبو الخصيب',
                color: 'abu-khaseeb',
                units: [
                    { type: 'infantry', attack: 15, defense: 20, health: 120 }, // Enhanced stats
                    { type: 'fighter', attack: 25, defense: 12, health: 100 },
                    { type: 'archer', attack: 20, defense: 8, health: 80 },
                    { type: 'cavalry', attack: 22, defense: 15, health: 110 }
                ]
            }
        };

        // Initialize game
        function initGame() {
            createGameBoard();
            setupInitialUnits();
            updateUI();
            addToLog('بدأت المعركة! فريق الحيانية يبدأ أولاً.');
        }

        // Create game board
        function createGameBoard() {
            const board = document.getElementById('gameBoard');
            board.innerHTML = '';

            for (let row = 0; row < 6; row++) {
                for (let col = 0; col < 8; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    cell.onclick = () => handleCellClick(row, col);
                    board.appendChild(cell);
                }
            }
        }

        // Setup initial units
        function setupInitialUnits() {
            // Clear board
            gameState.gameBoard = Array(6).fill().map(() => Array(8).fill(null));

            // Place Hayaniya units (top rows)
            const hayaniyaUnits = ['infantry', 'fighter', 'archer', 'cavalry', 'infantry', 'fighter', 'archer', 'cavalry'];
            for (let i = 0; i < 8; i++) {
                gameState.gameBoard[0][i] = createUnit('hayaniya', hayaniyaUnits[i], 0, i);
            }

            // Place Abu Khaseeb units (bottom rows) - with enhanced stats
            const abuKhaseebUnits = ['cavalry', 'archer', 'fighter', 'infantry', 'infantry', 'fighter', 'archer', 'cavalry'];
            for (let i = 0; i < 8; i++) {
                gameState.gameBoard[5][i] = createUnit('abukhaseeb', abuKhaseebUnits[i], 5, i);
            }

            renderBoard();
        }

        // Create unit object
        function createUnit(team, type, row, col) {
            const baseStats = unitTypes[type];
            const teamStats = teams[team].units.find(u => u.type === type);

            return {
                id: `${team}_${type}_${row}_${col}`,
                team: team,
                type: type,
                symbol: baseStats.symbol,
                attack: teamStats.attack,
                defense: teamStats.defense,
                health: teamStats.health,
                maxHealth: teamStats.health,
                range: baseStats.range,
                movement: baseStats.movement,
                row: row,
                col: col,
                hasMoved: false,
                hasAttacked: false
            };
        }

        // Render board
        function renderBoard() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);
                const unit = gameState.gameBoard[row][col];

                cell.innerHTML = '';
                cell.className = 'cell';

                if (unit) {
                    const unitElement = document.createElement('div');
                    unitElement.className = `unit ${unit.team}-unit`;
                    unitElement.innerHTML = unit.symbol;
                    unitElement.title = `${unit.type} - HP: ${unit.health}/${unit.maxHealth}`;
                    cell.appendChild(unitElement);
                }
            });
        }

        // Handle cell click
        function handleCellClick(row, col) {
            if (gameState.gameOver) return;

            const clickedUnit = gameState.gameBoard[row][col];

            // If no unit is selected
            if (!gameState.selectedUnit) {
                if (clickedUnit && clickedUnit.team === gameState.currentPlayer) {
                    selectUnit(clickedUnit, row, col);
                }
                return;
            }

            // If clicking on the same unit, deselect
            if (gameState.selectedUnit &&
                gameState.selectedCell.row === row &&
                gameState.selectedCell.col === col) {
                deselectUnit();
                return;
            }

            // If clicking on another friendly unit, select it
            if (clickedUnit && clickedUnit.team === gameState.currentPlayer) {
                selectUnit(clickedUnit, row, col);
                return;
            }

            // If clicking on empty cell or enemy unit, try to move/attack
            if (gameState.selectedUnit) {
                if (clickedUnit && clickedUnit.team !== gameState.currentPlayer) {
                    // Attack enemy unit
                    attemptAttack(gameState.selectedUnit, clickedUnit, row, col);
                } else if (!clickedUnit) {
                    // Move to empty cell
                    attemptMove(gameState.selectedUnit, row, col);
                }
            }
        }

        // Select unit
        function selectUnit(unit, row, col) {
            deselectUnit();
            gameState.selectedUnit = unit;
            gameState.selectedCell = { row, col };

            // Highlight selected cell
            const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            cell.classList.add('selected');

            // Show possible moves and attacks
            showPossibleActions(unit, row, col);

            // Update selected unit info
            updateSelectedUnitInfo(unit);
        }

        // Deselect unit
        function deselectUnit() {
            // Remove all highlights
            document.querySelectorAll('.cell').forEach(cell => {
                cell.classList.remove('selected', 'possible-move', 'enemy-target');
            });

            gameState.selectedUnit = null;
            gameState.selectedCell = null;
            updateSelectedUnitInfo(null);
        }

        // Show possible actions
        function showPossibleActions(unit, row, col) {
            // Show movement options
            if (!unit.hasMoved) {
                for (let r = Math.max(0, row - unit.movement); r <= Math.min(5, row + unit.movement); r++) {
                    for (let c = Math.max(0, col - unit.movement); c <= Math.min(7, col + unit.movement); c++) {
                        if (r === row && c === col) continue;
                        if (Math.abs(r - row) + Math.abs(c - col) <= unit.movement) {
                            if (!gameState.gameBoard[r][c]) {
                                const cell = document.querySelector(`[data-row="${r}"][data-col="${c}"]`);
                                cell.classList.add('possible-move');
                            }
                        }
                    }
                }
            }

            // Show attack options
            if (!unit.hasAttacked) {
                for (let r = Math.max(0, row - unit.range); r <= Math.min(5, row + unit.range); r++) {
                    for (let c = Math.max(0, col - unit.range); c <= Math.min(7, col + unit.range); c++) {
                        if (r === row && c === col) continue;
                        if (Math.abs(r - row) + Math.abs(c - col) <= unit.range) {
                            const target = gameState.gameBoard[r][c];
                            if (target && target.team !== unit.team) {
                                const cell = document.querySelector(`[data-row="${r}"][data-col="${c}"]`);
                                cell.classList.add('enemy-target');
                            }
                        }
                    }
                }
            }
        }

        // Attempt move
        function attemptMove(unit, targetRow, targetCol) {
            const distance = Math.abs(targetRow - unit.row) + Math.abs(targetCol - unit.col);

            if (unit.hasMoved) {
                addToLog(`${unit.type} قد تحرك بالفعل في هذا الدور.`);
                return;
            }

            if (distance > unit.movement) {
                addToLog(`المسافة بعيدة جداً للحركة.`);
                return;
            }

            // Move unit
            gameState.gameBoard[unit.row][unit.col] = null;
            unit.row = targetRow;
            unit.col = targetCol;
            unit.hasMoved = true;
            gameState.gameBoard[targetRow][targetCol] = unit;

            addToLog(`${teams[unit.team].name}: ${unit.type} تحرك إلى موقع جديد.`);

            deselectUnit();
            renderBoard();

            // Check if turn should end
            checkTurnEnd();
        }

        // Attempt attack
        function attemptAttack(attacker, defender, targetRow, targetCol) {
            const distance = Math.abs(targetRow - attacker.row) + Math.abs(targetCol - attacker.col);

            if (attacker.hasAttacked) {
                addToLog(`${attacker.type} قد هاجم بالفعل في هذا الدور.`);
                return;
            }

            if (distance > attacker.range) {
                addToLog(`الهدف بعيد جداً للهجوم.`);
                return;
            }

            // Calculate damage
            const damage = Math.max(1, attacker.attack - defender.defense + Math.floor(Math.random() * 10) - 5);
            defender.health -= damage;
            attacker.hasAttacked = true;

            addToLog(`${teams[attacker.team].name}: ${attacker.type} هاجم ${teams[defender.team].name}: ${defender.type} وألحق ${damage} ضرر.`);

            // Check if defender is defeated
            if (defender.health <= 0) {
                gameState.gameBoard[targetRow][targetCol] = null;
                addToLog(`${teams[defender.team].name}: ${defender.type} تم القضاء عليه!`);

                // Update score
                updateScore(attacker.team, 10);
            }

            deselectUnit();
            renderBoard();
            updateUI();

            // Check for victory
            if (checkVictory()) {
                return;
            }

            // Check if turn should end
            checkTurnEnd();
        }

        // Check turn end
        function checkTurnEnd() {
            const currentTeamUnits = getAllUnitsForTeam(gameState.currentPlayer);
            const allMoved = currentTeamUnits.every(unit => unit.hasMoved);
            const allAttacked = currentTeamUnits.every(unit => unit.hasAttacked);

            if (allMoved && allAttacked) {
                endTurn();
            }
        }

        // End turn
        function endTurn() {
            // Reset unit actions
            const currentTeamUnits = getAllUnitsForTeam(gameState.currentPlayer);
            currentTeamUnits.forEach(unit => {
                unit.hasMoved = false;
                unit.hasAttacked = false;
            });

            // Switch player
            gameState.currentPlayer = gameState.currentPlayer === 'hayaniya' ? 'abukhaseeb' : 'hayaniya';

            addToLog(`انتهى دور ${teams[gameState.currentPlayer === 'hayaniya' ? 'abukhaseeb' : 'hayaniya'].name}. دور ${teams[gameState.currentPlayer].name} الآن.`);

            updateUI();
            deselectUnit();

            // AI turn for Abu Khaseeb
            if (gameState.currentPlayer === 'abukhaseeb') {
                setTimeout(aiTurn, 1000);
            }
        }

        // Simple AI for Abu Khaseeb team
        function aiTurn() {
            if (gameState.gameOver) return;

            const aiUnits = getAllUnitsForTeam('abukhaseeb');

            for (let unit of aiUnits) {
                if (gameState.gameOver) break;

                // Try to attack first
                if (!unit.hasAttacked) {
                    const targets = findTargetsInRange(unit);
                    if (targets.length > 0) {
                        const target = targets[0]; // Attack first available target
                        const damage = Math.max(1, unit.attack - target.unit.defense + Math.floor(Math.random() * 10) - 5);
                        target.unit.health -= damage;
                        unit.hasAttacked = true;

                        addToLog(`${teams[unit.team].name}: ${unit.type} هاجم ${teams[target.unit.team].name}: ${target.unit.type} وألحق ${damage} ضرر.`);

                        if (target.unit.health <= 0) {
                            gameState.gameBoard[target.row][target.col] = null;
                            addToLog(`${teams[target.unit.team].name}: ${target.unit.type} تم القضاء عليه!`);
                            updateScore('abukhaseeb', 10);
                        }

                        if (checkVictory()) return;
                    }
                }

                // Then try to move towards enemies
                if (!unit.hasMoved) {
                    const moveOptions = findMoveOptions(unit);
                    if (moveOptions.length > 0) {
                        const move = moveOptions[0]; // Take first available move
                        gameState.gameBoard[unit.row][unit.col] = null;
                        unit.row = move.row;
                        unit.col = move.col;
                        unit.hasMoved = true;
                        gameState.gameBoard[move.row][move.col] = unit;

                        addToLog(`${teams[unit.team].name}: ${unit.type} تحرك إلى موقع جديد.`);
                    }
                }
            }

            renderBoard();
            updateUI();

            setTimeout(endTurn, 500);
        }

        // Find targets in range for AI
        function findTargetsInRange(unit) {
            const targets = [];
            for (let r = Math.max(0, unit.row - unit.range); r <= Math.min(5, unit.row + unit.range); r++) {
                for (let c = Math.max(0, unit.col - unit.range); c <= Math.min(7, unit.col + unit.range); c++) {
                    if (r === unit.row && c === unit.col) continue;
                    if (Math.abs(r - unit.row) + Math.abs(c - unit.col) <= unit.range) {
                        const target = gameState.gameBoard[r][c];
                        if (target && target.team !== unit.team) {
                            targets.push({ unit: target, row: r, col: c });
                        }
                    }
                }
            }
            return targets;
        }

        // Find move options for AI
        function findMoveOptions(unit) {
            const moves = [];
            for (let r = Math.max(0, unit.row - unit.movement); r <= Math.min(5, unit.row + unit.movement); r++) {
                for (let c = Math.max(0, unit.col - unit.movement); c <= Math.min(7, unit.col + unit.movement); c++) {
                    if (r === unit.row && c === unit.col) continue;
                    if (Math.abs(r - unit.row) + Math.abs(c - unit.col) <= unit.movement) {
                        if (!gameState.gameBoard[r][c]) {
                            moves.push({ row: r, col: c });
                        }
                    }
                }
            }
            return moves;
        }

        // Get all units for a team
        function getAllUnitsForTeam(team) {
            const units = [];
            for (let row = 0; row < 6; row++) {
                for (let col = 0; col < 8; col++) {
                    const unit = gameState.gameBoard[row][col];
                    if (unit && unit.team === team) {
                        units.push(unit);
                    }
                }
            }
            return units;
        }

        // Update score
        function updateScore(team, points) {
            if (team === 'hayaniya') {
                const current = parseInt(document.getElementById('hayaniyaScore').textContent);
                document.getElementById('hayaniyaScore').textContent = current + points;
            } else {
                const current = parseInt(document.getElementById('abuKhaseebScore').textContent);
                document.getElementById('abuKhaseebScore').textContent = current + points;
            }
        }

        // Check victory conditions
        function checkVictory() {
            const hayaniyaUnits = getAllUnitsForTeam('hayaniya');
            const abuKhaseebUnits = getAllUnitsForTeam('abukhaseeb');

            if (hayaniyaUnits.length === 0) {
                showVictory('abukhaseeb', 'فريق أبو الخصيب انتصر!', 'تم القضاء على جميع وحدات فريق الحيانية. أبو الخصيب يحكم الميدان!');
                return true;
            } else if (abuKhaseebUnits.length === 0) {
                showVictory('hayaniya', 'فريق الحيانية انتصر!', 'تم القضاء على جميع وحدات فريق أبو الخصيب. انتصار مذهل للحيانية!');
                return true;
            }

            return false;
        }

        // Show victory modal
        function showVictory(winner, title, message) {
            gameState.gameOver = true;
            document.getElementById('victoryTitle').textContent = title;
            document.getElementById('victoryMessage').textContent = message;
            document.getElementById('victoryModal').style.display = 'flex';

            addToLog(`انتهت المعركة! ${teams[winner].name} انتصر!`);
        }

        // Close victory modal
        function closeVictoryModal() {
            document.getElementById('victoryModal').style.display = 'none';
        }

        // Update UI
        function updateUI() {
            const hayaniyaUnits = getAllUnitsForTeam('hayaniya');
            const abuKhaseebUnits = getAllUnitsForTeam('abukhaseeb');

            // Update unit counts
            document.getElementById('hayaniyaUnits').textContent = hayaniyaUnits.length;
            document.getElementById('abuKhaseebUnits').textContent = abuKhaseebUnits.length;

            // Update power levels
            const hayaniyaPower = hayaniyaUnits.reduce((total, unit) => total + unit.health, 0);
            const abuKhaseebPower = abuKhaseebUnits.reduce((total, unit) => total + unit.health, 0);
            document.getElementById('hayaniyaPower').textContent = hayaniyaPower;
            document.getElementById('abuKhaseebPower').textContent = abuKhaseebPower;

            // Update turn indicator
            const turnIndicator = document.getElementById('turnIndicator');
            if (gameState.currentPlayer === 'hayaniya') {
                turnIndicator.textContent = 'دور فريق الحيانية';
                turnIndicator.className = 'turn-indicator hayaniya-turn';
            } else {
                turnIndicator.textContent = 'دور فريق أبو الخصيب';
                turnIndicator.className = 'turn-indicator abu-khaseeb-turn';
            }
        }

        // Update selected unit info
        function updateSelectedUnitInfo(unit) {
            const info = document.getElementById('selectedUnitInfo');
            if (unit) {
                info.textContent = `${unit.type} - صحة: ${unit.health}/${unit.maxHealth} - هجوم: ${unit.attack} - دفاع: ${unit.defense}`;
            } else {
                info.textContent = 'لا توجد وحدة محددة';
            }
        }

        // Add to game log
        function addToLog(message) {
            const log = document.getElementById('gameLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = message;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // Reset game
        function resetGame() {
            gameState = {
                currentPlayer: 'hayaniya',
                selectedUnit: null,
                selectedCell: null,
                gameBoard: Array(6).fill().map(() => Array(8).fill(null)),
                gameOver: false,
                soundEnabled: gameState.soundEnabled
            };

            document.getElementById('gameLog').innerHTML = '';
            document.getElementById('hayaniyaScore').textContent = '0';
            document.getElementById('abuKhaseebScore').textContent = '0';

            setupInitialUnits();
            updateUI();
            addToLog('بدأت معركة جديدة! فريق الحيانية يبدأ أولاً.');
        }

        // Show instructions
        function showInstructions() {
            alert(`تعليمات اللعبة:

1. اختر وحدة من فريقك بالنقر عليها
2. انقر على مربع فارغ للحركة أو على وحدة عدو للهجوم
3. كل وحدة يمكنها الحركة والهجوم مرة واحدة في الدور
4. أنواع الوحدات:
   🛡️ مشاة: دفاع قوي
   ⚔️ مقاتلين: هجوم سريع
   🏹 رماة: مدى بعيد
   🐎 فرسان: حركة سريعة

5. فريق أبو الخصيب لديه قوة أكبر ووحدات محسنة
6. اهزم جميع وحدات العدو للفوز!

حظاً موفقاً في المعركة!`);
        }

        // Toggle sound
        function toggleSound() {
            gameState.soundEnabled = !gameState.soundEnabled;
            const btn = event.target;
            btn.textContent = gameState.soundEnabled ? 'الصوت: تشغيل' : 'الصوت: إيقاف';
        }

        // Initialize game when page loads
        window.onload = function() {
            initGame();
        };
    </script>
</body>
</html>