#!/usr/bin/env python3
"""
Quick script to enhance the provided image for TikTok
"""

import os
import requests
from PIL import Image
from io import BytesIO
from tiktok_image_enhancer import TikTokImageEnhancer

def save_image_from_description():
    """
    Since we have the image from the conversation, we'll create a placeholder
    and guide the user to provide the actual image file
    """
    
    print("🎯 TikTok Image Enhancement Tool")
    print("=" * 50)
    
    # Check if user has an image file in the current directory
    image_files = [f for f in os.listdir('.') if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
    
    if image_files:
        print("📁 Found these image files in the current directory:")
        for i, file in enumerate(image_files, 1):
            print(f"   {i}. {file}")
        
        choice = input(f"\nSelect an image (1-{len(image_files)}) or press Enter to specify a different path: ").strip()
        
        if choice.isdigit() and 1 <= int(choice) <= len(image_files):
            selected_file = image_files[int(choice) - 1]
            return enhance_selected_image(selected_file)
    
    # Ask user for image path or URL
    image_path = input("\n📷 Please provide the image file path or URL: ").strip()
    
    if not image_path:
        print("❌ No image path provided. Please run the script again with an image.")
        return
    
    return enhance_selected_image(image_path)

def enhance_selected_image(image_path):
    """Enhance the selected image for TikTok"""
    
    print(f"\n🔄 Processing image: {image_path}")
    
    # Initialize the enhancer
    enhancer = TikTokImageEnhancer()
    
    # Generate output filename
    if image_path.startswith('http'):
        output_name = "enhanced_tiktok_image.jpg"
    else:
        name, ext = os.path.splitext(os.path.basename(image_path))
        output_name = f"{name}_tiktok_enhanced.jpg"
    
    # Enhance the image
    result = enhancer.enhance_for_tiktok(image_path, output_name)
    
    if result:
        print(f"\n✅ SUCCESS! Enhanced image saved as: {result}")
        print("\n📱 TikTok Optimization Features Applied:")
        print("   ✓ Converted to 9:16 vertical format (1080x1920)")
        print("   ✓ Enhanced brightness, contrast, and saturation")
        print("   ✓ Added subtle gradient overlay for better text visibility")
        print("   ✓ Added golden border frame for engagement")
        print("   ✓ Added AliToucan branding")
        print("   ✓ Optimized for mobile viewing")
        print("   ✓ High-quality JPEG output (95% quality)")
        
        print(f"\n🎬 Your image is now ready for TikTok upload!")
        print(f"📂 File location: {os.path.abspath(result)}")
        
        return result
    else:
        print("❌ Failed to enhance image. Please check the file path and try again.")
        return None

def main():
    """Main function"""
    try:
        # Install requirements if needed
        print("🔧 Checking dependencies...")
        
        # Try to import required modules
        try:
            from PIL import Image
            import arabic_reshaper
            import bidi
            import requests
            import numpy
            print("✅ All dependencies are available")
        except ImportError as e:
            print(f"❌ Missing dependency: {e}")
            print("📦 Please install requirements with: pip install -r requirements.txt")
            return
        
        # Enhance the image
        enhance_selected_image("enhanced_image.jpg")
        
    except KeyboardInterrupt:
        print("\n\n👋 Process cancelled by user")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")

if __name__ == "__main__":
    main()
