#!/usr/bin/env python3
"""
Test script for Professional Image Editor
Verifies that all dependencies are installed and components work correctly
"""

import sys
import os
import traceback

def test_imports():
    """Test all required imports"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter - GUI framework")
    except ImportError as e:
        print(f"✗ tkinter - {e}")
        return False
    
    try:
        from PIL import Image, ImageDraw, ImageFont, ImageEnhance, ImageFilter
        print("✓ Pillow (PIL) - Image processing")
    except ImportError as e:
        print(f"✗ Pillow - {e}")
        return False
    
    try:
        import cv2
        print("✓ OpenCV - Computer vision")
    except ImportError as e:
        print(f"✗ OpenCV - {e}")
        return False
    
    try:
        import numpy as np
        print("✓ NumPy - Numerical computing")
    except ImportError as e:
        print(f"✗ NumPy - {e}")
        return False
    
    try:
        import arabic_reshaper
        print("✓ arabic-reshaper - Arabic text processing")
    except ImportError as e:
        print(f"✗ arabic-reshaper - {e}")
        return False
    
    try:
        from bidi.algorithm import get_display
        print("✓ python-bidi - Bidirectional text")
    except ImportError as e:
        print(f"✗ python-bidi - {e}")
        return False
    
    return True

def test_application_modules():
    """Test application-specific modules"""
    print("\nTesting application modules...")
    
    modules = [
        'config',
        'utils',
        'image_processor',
        'filters',
        'layer_manager',
        'drawing_tools',
        'batch_processor',
        'arabic_support',
        'ui_components',
        'dialogs'
    ]
    
    for module in modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module} - {e}")
            return False
        except Exception as e:
            print(f"⚠ {module} - Warning: {e}")
    
    return True

def test_image_processing():
    """Test basic image processing functionality"""
    print("\nTesting image processing...")
    
    try:
        from PIL import Image
        from image_processor import ImageProcessor
        
        # Create test image
        test_image = Image.new('RGB', (100, 100), 'red')
        print("✓ Created test image")
        
        # Test processor
        processor = ImageProcessor()
        
        # Test resize
        resized = processor.resize_image(test_image, (50, 50))
        assert resized.size == (50, 50)
        print("✓ Image resize")
        
        # Test brightness
        bright = processor.adjust_brightness(test_image, 1.5)
        print("✓ Brightness adjustment")
        
        # Test contrast
        contrast = processor.adjust_contrast(test_image, 1.2)
        print("✓ Contrast adjustment")
        
        return True
        
    except Exception as e:
        print(f"✗ Image processing test failed: {e}")
        return False

def test_arabic_support():
    """Test Arabic text support"""
    print("\nTesting Arabic support...")
    
    try:
        from arabic_support import ArabicTextRenderer
        
        renderer = ArabicTextRenderer()
        print(f"✓ Arabic renderer created")
        print(f"✓ Available fonts: {len(renderer.available_fonts)}")
        
        # Test text processing
        test_text = "مرحبا"
        processed = renderer.process_arabic_text(test_text)
        print("✓ Arabic text processing")
        
        # Test font loading
        font = renderer.get_font(size=20)
        print("✓ Font loading")
        
        return True
        
    except Exception as e:
        print(f"✗ Arabic support test failed: {e}")
        return False

def test_filters():
    """Test filter functionality"""
    print("\nTesting filters...")
    
    try:
        from PIL import Image
        from filters import FilterEngine
        
        # Create test image
        test_image = Image.new('RGB', (100, 100), 'blue')
        
        filter_engine = FilterEngine()
        print("✓ Filter engine created")
        
        # Test sepia
        sepia = filter_engine.sepia_effect(test_image)
        print("✓ Sepia filter")
        
        # Test vintage
        vintage = filter_engine.vintage_effect(test_image)
        print("✓ Vintage filter")
        
        # Test black and white
        bw = filter_engine.black_and_white_effect(test_image)
        print("✓ Black & white filter")
        
        return True
        
    except Exception as e:
        print(f"✗ Filter test failed: {e}")
        return False

def test_layer_management():
    """Test layer management"""
    print("\nTesting layer management...")
    
    try:
        from PIL import Image
        from layer_manager import LayerManager, Layer
        
        # Create test setup
        canvas_size = (200, 200)
        layer_manager = LayerManager(canvas_size)
        
        # Create test layer
        test_image = Image.new('RGB', canvas_size, 'green')
        layer = Layer("Test Layer", test_image)
        
        # Test layer operations
        layer_id = layer_manager.add_layer(layer)
        print("✓ Layer added")
        
        layer_manager.set_active_layer(layer_id)
        print("✓ Layer selection")
        
        # Test layer properties
        layer.set_opacity(0.5)
        print("✓ Layer opacity")
        
        # Test flattening
        flattened = layer_manager.flatten_image()
        print("✓ Layer flattening")
        
        return True
        
    except Exception as e:
        print(f"✗ Layer management test failed: {e}")
        return False

def test_gui_components():
    """Test GUI components"""
    print("\nTesting GUI components...")
    
    try:
        import tkinter as tk
        from ui_components import ColorPicker, SliderControl, ImagePreview
        
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Test color picker
        color_picker = ColorPicker(root)
        print("✓ Color picker")
        
        # Test slider
        slider = SliderControl(root, "Test", 0, 100, 50)
        print("✓ Slider control")
        
        # Test image preview
        preview = ImagePreview(root)
        print("✓ Image preview")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ GUI components test failed: {e}")
        return False

def test_file_operations():
    """Test file operations"""
    print("\nTesting file operations...")
    
    try:
        from PIL import Image
        from utils import FileUtils, ImageUtils
        import tempfile
        import os
        
        # Test file utilities
        test_filename = "test_image.png"
        safe_name = FileUtils.get_safe_filename(test_filename)
        print("✓ Safe filename generation")
        
        # Test image info
        test_image = Image.new('RGB', (150, 150), 'yellow')
        
        # Save and load test
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            test_image.save(tmp.name)
            
            info = ImageUtils.get_image_info(tmp.name)
            assert info['width'] == 150
            assert info['height'] == 150
            print("✓ Image info extraction")
            
            # Clean up
            os.unlink(tmp.name)
        
        return True
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Professional Image Editor - Installation Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_application_modules,
        test_image_processing,
        test_arabic_support,
        test_filters,
        test_layer_management,
        test_gui_components,
        test_file_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("Test failed!")
        except Exception as e:
            print(f"Test error: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        print("\nTo start the application, run:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        print("\nCommon solutions:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check Python version (3.7+ required)")
        print("3. Verify all application files are present")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
