"""
Drawing tools for the Professional Image Editor
Includes brush, pencil, shapes, text overlay, and annotation tools
"""

from PIL import Image, ImageDraw, ImageFont
import numpy as np
from typing import Tuple, List, Optional, Union
from enum import Enum
import math
from arabic_support import ArabicTextRenderer
from config import DRAWING_TOOLS

class DrawingTool(Enum):
    """Available drawing tools"""
    BRUSH = "brush"
    PENCIL = "pencil"
    ERASER = "eraser"
    RECTANGLE = "rectangle"
    CIRCLE = "circle"
    LINE = "line"
    ARROW = "arrow"
    TEXT = "text"
    POLYGON = "polygon"

class DrawingEngine:
    """Main drawing engine for all drawing operations"""
    
    def __init__(self):
        self.arabic_renderer = ArabicTextRenderer()
        self.brush_sizes = DRAWING_TOOLS['brush_sizes']
        self.default_colors = DRAWING_TOOLS['default_colors']
        
    def draw_brush_stroke(self, image: Image.Image, points: List[Tuple[int, int]], 
                         brush_size: int, color: str, opacity: float = 1.0) -> Image.Image:
        """Draw smooth brush stroke through multiple points"""
        if len(points) < 2:
            return image
        
        # Create drawing layer
        draw_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(draw_layer)
        
        # Draw smooth lines between points
        for i in range(len(points) - 1):
            x1, y1 = points[i]
            x2, y2 = points[i + 1]
            
            # Draw line with rounded ends
            draw.line([x1, y1, x2, y2], fill=color, width=brush_size)
            
            # Draw circles at points for smooth connection
            radius = brush_size // 2
            draw.ellipse([x1 - radius, y1 - radius, x1 + radius, y1 + radius], 
                        fill=color)
        
        # Draw final point
        if points:
            x, y = points[-1]
            radius = brush_size // 2
            draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                        fill=color)
        
        # Apply opacity
        if opacity < 1.0:
            draw_layer = self._apply_opacity(draw_layer, opacity)
        
        # Composite with original image
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        return Image.alpha_composite(image, draw_layer)
    
    def draw_pencil_stroke(self, image: Image.Image, points: List[Tuple[int, int]], 
                          pencil_size: int, color: str, opacity: float = 0.8) -> Image.Image:
        """Draw pencil stroke with texture"""
        if len(points) < 2:
            return image
        
        # Create drawing layer
        draw_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
        
        # Add texture to pencil stroke
        for i, (x, y) in enumerate(points):
            # Vary opacity and size slightly for texture
            texture_opacity = opacity * (0.8 + 0.4 * np.random.random())
            texture_size = max(1, pencil_size + np.random.randint(-1, 2))
            
            # Draw small circles for texture
            draw = ImageDraw.Draw(draw_layer)
            radius = texture_size // 2
            
            # Convert color to RGBA with texture opacity
            if isinstance(color, str):
                if color.startswith('#'):
                    r = int(color[1:3], 16)
                    g = int(color[3:5], 16)
                    b = int(color[5:7], 16)
                    texture_color = (r, g, b, int(255 * texture_opacity))
                else:
                    texture_color = color
            else:
                texture_color = color
            
            draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                        fill=texture_color)
        
        # Composite with original image
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        return Image.alpha_composite(image, draw_layer)
    
    def draw_rectangle(self, image: Image.Image, start_point: Tuple[int, int], 
                      end_point: Tuple[int, int], outline_color: str, 
                      fill_color: Optional[str] = None, line_width: int = 2) -> Image.Image:
        """Draw rectangle"""
        draw_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(draw_layer)
        
        x1, y1 = start_point
        x2, y2 = end_point
        
        # Ensure correct order
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        
        # Draw rectangle
        draw.rectangle([left, top, right, bottom], 
                      outline=outline_color, fill=fill_color, width=line_width)
        
        # Composite with original image
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        return Image.alpha_composite(image, draw_layer)
    
    def draw_circle(self, image: Image.Image, center: Tuple[int, int], 
                   radius: int, outline_color: str, 
                   fill_color: Optional[str] = None, line_width: int = 2) -> Image.Image:
        """Draw circle"""
        draw_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(draw_layer)
        
        x, y = center
        
        # Draw circle
        draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                    outline=outline_color, fill=fill_color, width=line_width)
        
        # Composite with original image
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        return Image.alpha_composite(image, draw_layer)
    
    def draw_line(self, image: Image.Image, start_point: Tuple[int, int], 
                 end_point: Tuple[int, int], color: str, line_width: int = 2) -> Image.Image:
        """Draw straight line"""
        draw_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(draw_layer)
        
        draw.line([start_point, end_point], fill=color, width=line_width)
        
        # Composite with original image
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        return Image.alpha_composite(image, draw_layer)
    
    def draw_arrow(self, image: Image.Image, start_point: Tuple[int, int], 
                  end_point: Tuple[int, int], color: str, line_width: int = 2,
                  arrow_size: int = 10) -> Image.Image:
        """Draw arrow"""
        draw_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(draw_layer)
        
        x1, y1 = start_point
        x2, y2 = end_point
        
        # Draw main line
        draw.line([start_point, end_point], fill=color, width=line_width)
        
        # Calculate arrow head
        angle = math.atan2(y2 - y1, x2 - x1)
        
        # Arrow head points
        arrow_angle = math.pi / 6  # 30 degrees
        
        # Left arrow point
        left_x = x2 - arrow_size * math.cos(angle - arrow_angle)
        left_y = y2 - arrow_size * math.sin(angle - arrow_angle)
        
        # Right arrow point
        right_x = x2 - arrow_size * math.cos(angle + arrow_angle)
        right_y = y2 - arrow_size * math.sin(angle + arrow_angle)
        
        # Draw arrow head
        arrow_points = [(x2, y2), (left_x, left_y), (right_x, right_y)]
        draw.polygon(arrow_points, fill=color)
        
        # Composite with original image
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        return Image.alpha_composite(image, draw_layer)
    
    def draw_polygon(self, image: Image.Image, points: List[Tuple[int, int]], 
                    outline_color: str, fill_color: Optional[str] = None, 
                    line_width: int = 2) -> Image.Image:
        """Draw polygon"""
        if len(points) < 3:
            return image
        
        draw_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(draw_layer)
        
        draw.polygon(points, outline=outline_color, fill=fill_color, width=line_width)
        
        # Composite with original image
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        return Image.alpha_composite(image, draw_layer)
    
    def add_text(self, image: Image.Image, position: Tuple[int, int], text: str,
                font_size: int = 20, font_name: str = None, color: str = 'black',
                is_arabic: bool = False, align: str = 'left') -> Image.Image:
        """Add text to image with Arabic support"""
        draw_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
        
        if is_arabic:
            # Use Arabic text renderer
            font = self.arabic_renderer.get_font(font_name, font_size)
            self.arabic_renderer.draw_text(draw_layer, position, text, font, color, align)
        else:
            # Use standard text rendering
            draw = ImageDraw.Draw(draw_layer)
            
            try:
                if font_name:
                    font = ImageFont.truetype(font_name, font_size)
                else:
                    font = ImageFont.truetype("arial.ttf", font_size)
            except Exception:
                font = ImageFont.load_default()
            
            # Adjust position for alignment
            if align == 'center':
                text_width = draw.textlength(text, font=font)
                position = (position[0] - text_width // 2, position[1])
            elif align == 'right':
                text_width = draw.textlength(text, font=font)
                position = (position[0] - text_width, position[1])
            
            draw.text(position, text, font=font, fill=color)
        
        # Composite with original image
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        return Image.alpha_composite(image, draw_layer)
    
    def erase_area(self, image: Image.Image, points: List[Tuple[int, int]], 
                  eraser_size: int) -> Image.Image:
        """Erase area of image"""
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        # Create eraser mask
        mask = Image.new('L', image.size, 255)
        mask_draw = ImageDraw.Draw(mask)
        
        # Draw eraser strokes on mask (black = transparent)
        for i in range(len(points) - 1):
            x1, y1 = points[i]
            x2, y2 = points[i + 1]
            
            mask_draw.line([x1, y1, x2, y2], fill=0, width=eraser_size)
            
            # Draw circles at points for smooth erasing
            radius = eraser_size // 2
            mask_draw.ellipse([x1 - radius, y1 - radius, x1 + radius, y1 + radius], 
                            fill=0)
        
        # Apply final point
        if points:
            x, y = points[-1]
            radius = eraser_size // 2
            mask_draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                            fill=0)
        
        # Apply mask to image alpha channel
        r, g, b, a = image.split()
        new_alpha = Image.composite(a, Image.new('L', image.size, 0), mask)
        
        return Image.merge('RGBA', (r, g, b, new_alpha))
    
    def _apply_opacity(self, image: Image.Image, opacity: float) -> Image.Image:
        """Apply opacity to image"""
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        r, g, b, a = image.split()
        a = a.point(lambda x: int(x * opacity))
        
        return Image.merge('RGBA', (r, g, b, a))
    
    def create_gradient(self, size: Tuple[int, int], start_color: str, 
                       end_color: str, direction: str = 'horizontal') -> Image.Image:
        """Create gradient image"""
        width, height = size
        
        # Parse colors
        if start_color.startswith('#'):
            start_rgb = tuple(int(start_color[i:i+2], 16) for i in (1, 3, 5))
        else:
            start_rgb = (0, 0, 0)  # Default
        
        if end_color.startswith('#'):
            end_rgb = tuple(int(end_color[i:i+2], 16) for i in (1, 3, 5))
        else:
            end_rgb = (255, 255, 255)  # Default
        
        # Create gradient
        gradient = Image.new('RGB', size)
        
        if direction == 'horizontal':
            for x in range(width):
                ratio = x / width
                r = int(start_rgb[0] * (1 - ratio) + end_rgb[0] * ratio)
                g = int(start_rgb[1] * (1 - ratio) + end_rgb[1] * ratio)
                b = int(start_rgb[2] * (1 - ratio) + end_rgb[2] * ratio)
                
                for y in range(height):
                    gradient.putpixel((x, y), (r, g, b))
        
        elif direction == 'vertical':
            for y in range(height):
                ratio = y / height
                r = int(start_rgb[0] * (1 - ratio) + end_rgb[0] * ratio)
                g = int(start_rgb[1] * (1 - ratio) + end_rgb[1] * ratio)
                b = int(start_rgb[2] * (1 - ratio) + end_rgb[2] * ratio)
                
                for x in range(width):
                    gradient.putpixel((x, y), (r, g, b))
        
        return gradient
