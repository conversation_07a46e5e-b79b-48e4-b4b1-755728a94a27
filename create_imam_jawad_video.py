#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Video Generator for Imam <PERSON> Martyrdom Anniversary
Converts PowerPoint presentation to high-quality MP4 video
For Shia Muslim Community Commemorations
"""

import os
import sys
from moviepy.editor import ImageClip, concatenate_videoclips
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
import numpy as np

class ImamJawadVideoGenerator:
    def __init__(self):
        self.video_width = 1920
        self.video_height = 1080
        self.fps = 30
        self.slide_duration = 5  # 5 seconds per slide for quick social media sharing

        # Shia mourning colors
        self.colors = {
            'black': (0, 0, 0),
            'dark_navy': (26, 26, 46),
            'dark_green': (15, 52, 96),
            'white': (255, 255, 255),
            'gold': (212, 175, 55)
        }

        # Slide content data
        self.slides_data = [
            {
                'title': 'ذكرى استشهاد الإمام محمد الجواد (عليه السلام)',
                'subtitle': 'Martyrdom Anniversary of Imam <PERSON> (Peace be upon him)\nThe 9th Imam of Shia Islam',
                'date': '29 ذو القعدة 220 هجرية',
                'bg_color': 'black'
            },
            {
                'title': 'نبذة عن الإمام محمد الجواد (عليه السلام)',
                'content': [
                    '• الإمام التاسع من الأئمة الاثني عشر (عليهم السلام)',
                    '• ولد في المدينة المنورة سنة 195 هجرية',
                    '• تولى الإمامة وهو في السابعة من عمره',
                    '• لُقب بالجواد لكرمه وسخائه',
                    '• استشهد مسموماً في بغداد سنة 220 هجرية',
                    '• دُفن في مقبرة قريش ببغداد (الكاظمية)'
                ],
                'eng_content': 'Biography of Imam Muhammad al-Jawad (Peace be upon him)\n• The 9th Imam of the Twelve Imams\n• Born in Medina in 195 AH\n• Became Imam at age 7\n• Known for his generosity\n• Martyred by poison in Baghdad 220 AH',
                'bg_color': 'dark_navy'
            },
            {
                'title': 'ظروف الاستشهاد',
                'content': [
                    '• استشهد في 29 ذو القعدة سنة 220 هجرية',
                    '• دُس له السم من قبل المعتصم العباسي',
                    '• كان عمره الشريف 25 سنة',
                    '• مدة إمامته 17 سنة',
                    '• دُفن مع جده الإمام موسى الكاظم (ع)',
                    '• مرقده الشريف في الكاظمية ببغداد'
                ],
                'bg_color': 'dark_green'
            },
            {
                'title': 'آيات قرآنية كريمة في فضل أهل البيت (عليهم السلام)',
                'verse1': 'إِنَّمَا يُرِيدُ ٱللَّهُ لِيُذْهِبَ عَنكُمُ ٱلرِّجْسَ أَهْلَ ٱلْبَيْتِ وَيُطَهِّرَكُمْ تَطْهِيرًا',
                'ref1': 'سُورَةُ الأَحْزَابِ - آيَة ٣٣',
                'trans1': 'Allah only intends to keep ˹the causes of˺ evil away from you and purify you completely, O members of the ˹Prophet\'s˺ household!',
                'verse2': 'قُل لَّآ أَسْـَٔلُكُمْ عَلَيْهِ أَجْرًا إِلَّا ٱلْمَوَدَّةَ فِى ٱلْقُرْبَىٰ',
                'ref2': 'سُورَةُ الشُّورَىٰ - آيَة ٢٣',
                'trans2': 'Say, ˹O Prophet,˺ \'I do not ask you for a reward for this ˹message˺—only love for ˹our˺ kinship.\'',
                'bg_color': 'black'
            },
            {
                'title': 'التفسير الشيعي للآيات الكريمة',
                'content': [
                    'آية التطهير (الأحزاب: ٣٣)',
                    '• نزلت في أهل البيت الخمسة: النبي وعلي وفاطمة والحسن والحسين (عليهم السلام)',
                    '• تؤكد طهارة أهل البيت من الذنوب والأخطاء',
                    '• تثبت عصمة الأئمة من أهل البيت',
                    '• الإمام الجواد (ع) من أهل البيت المطهرين المشمولين بهذه الآية',
                    '',
                    'آية المودة (الشورى: ٢٣)',
                    '• القربى هم أهل بيت النبي (صلى الله عليه وآله)',
                    '• وجوب محبة أهل البيت على جميع المسلمين',
                    '• الإمام الجواد (ع) من القربى الواجبة المحبة',
                    '• محبة أهل البيت شرط لقبول الأعمال'
                ],
                'bg_color': 'dark_green'
            },
            {
                'title': 'أحاديث شريفة في فضل الإمام الجواد (عليه السلام)',
                'hadith': 'عن الإمام الرضا (عليه السلام):\n"إن ابني هذا - يعني أبا جعفر - وصيي وخليفتي من بعدي،\nوهو أعلم الناس بكتاب الله وسنة رسوله"',
                'source': 'المصدر: الكافي للكليني - كتاب الحجة',
                'eng_hadith': 'From Imam al-Ridha (peace be upon him):\n"This son of mine - meaning Abu Ja\'far - is my successor and caliph after me,\nand he is the most knowledgeable of people in the Book of Allah and the Sunnah of His Messenger."',
                'bg_color': 'dark_navy'
            },
            {
                'title': 'من حكم وتعاليم الإمام الجواد (عليه السلام)',
                'teachings': [
                    '"من أصلح سريرته أصلح الله علانيته"',
                    '"العلم خير من المال، العلم يحرسك وأنت تحرس المال"',
                    '"من كان الورع سجيته كان الخير عادته"',
                    '"الصبر عند المصيبة يعدل الشكر عند النعمة"'
                ],
                'eng_teachings': 'Wisdom and Teachings of Imam al-Jawad (Peace be upon him):\n• "Whoever reforms his inner self, Allah will reform his outward appearance"\n• "Knowledge is better than wealth; knowledge guards you while you guard wealth"\n• "Whoever makes piety his nature, goodness becomes his habit"\n• "Patience during calamity equals gratitude during blessing"',
                'bg_color': 'dark_green'
            },
            {
                'title': 'دعاء وزيارة الإمام الجواد (عليه السلام)',
                'prayer': 'السلام عليك يا أبا جعفر محمد بن علي الجواد\nالسلام عليك يا إمام الهدى ونور الدجى\nالسلام عليك يا حجة الله على خلقه\nالسلام عليك يا شهيد المظلوم\nأشهد أنك قد أقمت الصلاة وآتيت الزكاة\nوأمرت بالمعروف ونهيت عن المنكر',
                'eng_prayer': 'Ziyarat (Visitation Prayer) for Imam al-Jawad:\nPeace be upon you, O Abu Ja\'far Muhammad ibn Ali al-Jawad\nPeace be upon you, O Imam of guidance and light in darkness\nPeace be upon you, O proof of Allah upon His creation\nPeace be upon you, O martyred oppressed one\nI bear witness that you established prayer and gave charity\nAnd commanded good and forbade evil',
                'bg_color': 'black'
            },
            {
                'title': 'دروس معاصرة من حياة الإمام الجواد (عليه السلام)',
                'content': [
                    '• العلم والحكمة لا يقيدهما العمر',
                    '• الصبر والثبات في وجه الظلم',
                    '• الكرم والسخاء مع المحتاجين',
                    '• التواضع رغم المكانة العالية',
                    '• الاهتمام بالعدالة الاجتماعية',
                    '• القيادة الروحية والأخلاقية'
                ],
                'eng_content': 'Contemporary Lessons from Imam al-Jawad\'s Life:\n• Knowledge and wisdom are not limited by age\n• Patience and steadfastness against oppression\n• Generosity and charity with those in need\n• Humility despite high status\n• Concern for social justice\n• Spiritual and moral leadership',
                'bg_color': 'dark_navy'
            },
            {
                'title': 'عظم الله أجوركم بذكرى استشهاد الإمام الجواد (عليه السلام)',
                'condolence': 'أحيا الله ذكراهم وأعلى مقامهم\nوجعلنا من المتمسكين بولايتهم\nوالسائرين على نهجهم القويم',
                'eng_condolence': 'May Allah magnify your reward on the martyrdom anniversary of Imam al-Jawad (Peace be upon him)\nMay Allah revive their memory and elevate their status\nAnd make us among those who hold fast to their guardianship\nAnd follow their righteous path',
                'bg_color': 'black'
            }
        ]

    def format_arabic_text(self, text):
        """Format Arabic text for proper RTL display"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text

    def create_slide_image(self, slide_data, slide_num):
        """Create an image for a single slide"""
        # Create image with background color
        bg_color = self.colors[slide_data['bg_color']]
        img = Image.new('RGB', (self.video_width, self.video_height), bg_color)
        draw = ImageDraw.Draw(img)

        try:
            # Try to load Amiri font, fallback to default if not available
            title_font = ImageFont.truetype("arial.ttf", 60)
            content_font = ImageFont.truetype("arial.ttf", 40)
            subtitle_font = ImageFont.truetype("arial.ttf", 35)
        except:
            # Fallback to default font
            title_font = ImageFont.load_default()
            content_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()

        # Draw title
        if 'title' in slide_data:
            title_text = self.format_arabic_text(slide_data['title'])
            # Calculate text position for center alignment
            bbox = draw.textbbox((0, 0), title_text, font=title_font)
            text_width = bbox[2] - bbox[0]
            x = (self.video_width - text_width) // 2
            draw.text((x, 100), title_text, fill=self.colors['gold'], font=title_font)

        # Draw content based on slide type
        if slide_num == 1:  # Title slide
            if 'subtitle' in slide_data:
                lines = slide_data['subtitle'].split('\n')
                y_pos = 300
                for line in lines:
                    bbox = draw.textbbox((0, 0), line, font=subtitle_font)
                    text_width = bbox[2] - bbox[0]
                    x = (self.video_width - text_width) // 2
                    draw.text((x, y_pos), line, fill=self.colors['white'], font=subtitle_font)
                    y_pos += 60

            if 'date' in slide_data:
                date_text = self.format_arabic_text(slide_data['date'])
                bbox = draw.textbbox((0, 0), date_text, font=content_font)
                text_width = bbox[2] - bbox[0]
                x = (self.video_width - text_width) // 2
                draw.text((x, 600), date_text, fill=self.colors['white'], font=content_font)

        elif slide_num == 4:  # Quranic verses slide
            y_pos = 250

            # First verse
            verse1_text = self.format_arabic_text(slide_data['verse1'])
            bbox = draw.textbbox((0, 0), verse1_text, font=content_font)
            text_width = bbox[2] - bbox[0]
            x = (self.video_width - text_width) // 2
            draw.text((x, y_pos), verse1_text, fill=self.colors['gold'], font=content_font)
            y_pos += 80

            # First reference
            ref1_text = self.format_arabic_text(slide_data['ref1'])
            bbox = draw.textbbox((0, 0), ref1_text, font=subtitle_font)
            text_width = bbox[2] - bbox[0]
            x = (self.video_width - text_width) // 2
            draw.text((x, y_pos), ref1_text, fill=self.colors['white'], font=subtitle_font)
            y_pos += 100

            # Second verse
            verse2_text = self.format_arabic_text(slide_data['verse2'])
            bbox = draw.textbbox((0, 0), verse2_text, font=content_font)
            text_width = bbox[2] - bbox[0]
            x = (self.video_width - text_width) // 2
            draw.text((x, y_pos), verse2_text, fill=self.colors['gold'], font=content_font)
            y_pos += 80

            # Second reference
            ref2_text = self.format_arabic_text(slide_data['ref2'])
            bbox = draw.textbbox((0, 0), ref2_text, font=subtitle_font)
            text_width = bbox[2] - bbox[0]
            x = (self.video_width - text_width) // 2
            draw.text((x, y_pos), ref2_text, fill=self.colors['white'], font=subtitle_font)

        elif slide_num == 6:  # Hadith slide
            if 'hadith' in slide_data:
                hadith_lines = slide_data['hadith'].split('\n')
                y_pos = 250
                for line in hadith_lines:
                    formatted_text = self.format_arabic_text(line)
                    bbox = draw.textbbox((0, 0), formatted_text, font=content_font)
                    text_width = bbox[2] - bbox[0]
                    x = (self.video_width - text_width) // 2
                    draw.text((x, y_pos), formatted_text, fill=self.colors['white'], font=content_font)
                    y_pos += 60

                # Source
                if 'source' in slide_data:
                    source_text = self.format_arabic_text(slide_data['source'])
                    bbox = draw.textbbox((0, 0), source_text, font=subtitle_font)
                    text_width = bbox[2] - bbox[0]
                    x = (self.video_width - text_width) // 2
                    draw.text((x, y_pos + 40), source_text, fill=self.colors['gold'], font=subtitle_font)

        elif slide_num == 7:  # Teachings slide
            if 'teachings' in slide_data:
                y_pos = 250
                for teaching in slide_data['teachings']:
                    formatted_text = self.format_arabic_text(teaching)
                    bbox = draw.textbbox((0, 0), formatted_text, font=content_font)
                    text_width = bbox[2] - bbox[0]
                    x = (self.video_width - text_width) // 2
                    draw.text((x, y_pos), formatted_text, fill=self.colors['white'], font=content_font)
                    y_pos += 80

        elif slide_num == 8:  # Prayer slide
            if 'prayer' in slide_data:
                prayer_lines = slide_data['prayer'].split('\n')
                y_pos = 250
                for line in prayer_lines:
                    formatted_text = self.format_arabic_text(line)
                    bbox = draw.textbbox((0, 0), formatted_text, font=content_font)
                    text_width = bbox[2] - bbox[0]
                    x = (self.video_width - text_width) // 2
                    draw.text((x, y_pos), formatted_text, fill=self.colors['white'], font=content_font)
                    y_pos += 50

        elif slide_num == 10:  # Closing slide
            if 'condolence' in slide_data:
                condolence_lines = slide_data['condolence'].split('\n')
                y_pos = 300
                for line in condolence_lines:
                    formatted_text = self.format_arabic_text(line)
                    bbox = draw.textbbox((0, 0), formatted_text, font=content_font)
                    text_width = bbox[2] - bbox[0]
                    x = (self.video_width - text_width) // 2
                    draw.text((x, y_pos), formatted_text, fill=self.colors['white'], font=content_font)
                    y_pos += 60

        else:  # Content slides
            if 'content' in slide_data:
                y_pos = 250
                for item in slide_data['content']:
                    if item.strip():  # Skip empty lines
                        formatted_text = self.format_arabic_text(item)
                        # For bullet points, align to the right
                        if item.startswith('•'):
                            x = self.video_width - 100
                            draw.text((x, y_pos), formatted_text, fill=self.colors['white'],
                                    font=content_font, anchor="ra")
                        else:
                            bbox = draw.textbbox((0, 0), formatted_text, font=content_font)
                            text_width = bbox[2] - bbox[0]
                            x = (self.video_width - text_width) // 2
                            draw.text((x, y_pos), formatted_text, fill=self.colors['gold'], font=content_font)
                        y_pos += 60

        # Add AliToucan branding
        draw.text((self.video_width - 200, self.video_height - 50), "AliToucan",
                 fill=self.colors['gold'], font=subtitle_font)

        return img

    def create_video(self):
        """Create the complete video from slides"""
        print("🎬 Creating Imam Muhammad al-Jawad Martyrdom Anniversary Video...")

        # Create slide images
        slide_images = []
        for i, slide_data in enumerate(self.slides_data, 1):
            print(f"📄 Creating slide {i}...")
            img = self.create_slide_image(slide_data, i)

            # Save temporary image
            temp_filename = f"temp_slide_{i}.png"
            img.save(temp_filename)
            slide_images.append(temp_filename)

        # Create video clips from images
        clips = []
        for i, img_path in enumerate(slide_images):
            print(f"🎞️ Processing slide {i+1} for video...")
            clip = ImageClip(img_path, duration=self.slide_duration)
            clips.append(clip)

        # Concatenate all clips with fade transitions
        print("🔗 Concatenating slides with transitions...")
        final_clips = []
        for i, clip in enumerate(clips):
            if i > 0:
                # Add fade transition between slides
                clip = clip.crossfadein(1.0)
            final_clips.append(clip)

        # Create final video
        print("🎥 Creating final video...")
        final_video = concatenate_videoclips(final_clips, method="compose")

        # Set video properties
        final_video = final_video.set_fps(self.fps)

        # Export video
        output_filename = "Imam_Muhammad_al_Jawad_Martyrdom_Video.mp4"
        print(f"💾 Exporting video as {output_filename}...")

        final_video.write_videofile(
            output_filename,
            fps=self.fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            ffmpeg_params=['-crf', '18']  # High quality
        )

        # Clean up temporary files
        print("🧹 Cleaning up temporary files...")
        for img_path in slide_images:
            try:
                os.remove(img_path)
            except:
                pass

        print(f"✅ Video created successfully: {output_filename}")
        print(f"📊 Video specifications:")
        print(f"   • Resolution: {self.video_width}x{self.video_height}")
        print(f"   • Frame Rate: {self.fps} FPS")
        print(f"   • Duration: ~{len(self.slides_data) * self.slide_duration} seconds ({len(self.slides_data) * self.slide_duration / 60:.1f} minutes)")
        print(f"   • Format: MP4 (H.264)")
        print(f"   • Slides: {len(self.slides_data)}")
        print(f"   • Slide Duration: {self.slide_duration} seconds each")

        return output_filename

def main():
    """Main function to create the video"""
    try:
        generator = ImamJawadVideoGenerator()
        video_file = generator.create_video()

        print(f"\n🎉 Successfully created video: {video_file}")
        print("\n🕌 This video is ready for:")
        print("• Shia religious gatherings and commemorations")
        print("• Social media sharing (WhatsApp, Telegram, Instagram, Facebook)")
        print("• Educational purposes in Islamic institutions")
        print("• Digital archives for community use")

        print(f"\n📍 File location: {os.path.abspath(video_file)}")
        print("\n🌟 Video features:")
        print("• High-quality 1080p resolution")
        print("• Proper Arabic RTL text formatting")
        print("• Shia mourning color scheme")
        print("• Smooth slide transitions (1-second crossfades)")
        print("• Quick 5-second slides for social media sharing")
        print("• Optimized for WhatsApp, Telegram, Instagram, Facebook")
        print("• Culturally appropriate for Islamic commemorations")

    except Exception as e:
        print(f"❌ Error creating video: {str(e)}")
        print("Please ensure all required packages are installed:")
        print("pip install moviepy pillow arabic-reshaper python-bidi")

if __name__ == "__main__":
    main()
