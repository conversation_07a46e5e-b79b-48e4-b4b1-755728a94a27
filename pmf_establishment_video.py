#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Video Generator for PMF Establishment Commemoration
Converts PowerPoint presentation to high-quality MP4 video
For Iraqi Patriotic Community Commemorations
"""

import os
import sys
from moviepy.editor import ImageClip, concatenate_videoclips, CompositeVideoClip
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
import numpy as np

class PMFEstablishmentVideoGenerator:
    def __init__(self):
        self.video_width = 1920
        self.video_height = 1080
        self.fps = 30
        self.slide_duration = 5  # 5 seconds per slide as requested

        # Iraqi flag patriotic colors with gold accents
        self.colors = {
            'red': (206, 17, 38),      # Iraqi flag red #CE1126
            'white': (255, 255, 255),  # Iraqi flag white #FFFFFF
            'black': (0, 0, 0),        # Iraqi flag black #000000
            'gold': (255, 215, 0),     # Gold accents #FFD700
            'dark_green': (0, 100, 0), # Islamic green for religious content
            'navy_blue': (25, 25, 112) # Professional navy for backgrounds
        }

        # Slide content data matching the PowerPoint presentation
        self.slides_data = [
            {
                'title': 'تأسيس الحشد الشعبي المقدس',
                'subtitle': 'دفاعاً عن العراق وأرضه المقدسة',
                'eng_subtitle': 'Popular Mobilization Forces Establishment\nDefending Iraq and Its Sacred Land',
                'date': '١٣ حزيران ٢٠١٤ - June 13, 2014',
                'slogan': 'العراق أولاً - Iraq First',
                'bg_color': 'black'
            },
            {
                'title': 'الخلفية التاريخية لتأسيس الحشد الشعبي',
                'content': [
                    '• سقوط الموصل وأجزاء واسعة من العراق بيد تنظيم داعش الإرهابي',
                    '• تهديد العاصمة بغداد والمقدسات الدينية في سامراء وكربلاء والنجف',
                    '• فتوى المرجع الأعلى السيد علي السيستاني للجهاد الكفائي',
                    '• استجابة الشعب العراقي الواسعة للدفاع عن الوطن',
                    '• تشكيل قوات شعبية متطوعة للدفاع عن العراق',
                    '• الاعتراف الرسمي بالحشد الشعبي كجزء من القوات المسلحة العراقية',
                    '• دور الحشد في حماية الوحدة الوطنية العراقية'
                ],
                'eng_content': 'Historical Context of PMF Formation:\n• Fall of Mosul and vast parts of Iraq to ISIS terrorist organization\n• Threat to Baghdad capital and religious shrines in Samarra, Karbala, and Najaf\n• Grand Ayatollah Ali al-Sistani\'s fatwa for sufficient jihad (defensive jihad)\n• Wide Iraqi popular response to defend the homeland\n• Formation of volunteer popular forces to defend Iraq\n• Official recognition of PMF as part of Iraqi Armed Forces\n• PMF\'s role in protecting Iraqi national unity',
                'bg_color': 'navy_blue'
            },
            {
                'title': 'التسلسل الزمني لتأسيس الحشد الشعبي',
                'content': [
                    '١٠ يونيو ٢٠١٤: سقوط الموصل بيد داعش',
                    '١٣ يونيو ٢٠١٤: فتوى المرجع السيستاني للجهاد الكفائي',
                    '١٥ يونيو ٢٠١٤: بداية تشكيل الوحدات التطوعية',
                    '٢٦ نوفمبر ٢٠١٦: إقرار قانون الحشد الشعبي رقم ٤٠',
                    '٢٠١٧: دمج الحشد رسمياً في القوات المسلحة العراقية',
                    '٢٠١٨: تشكيل قيادة عمليات الحشد الشعبي',
                    '٢٠١٩: تعيين قائد عام للحشد الشعبي',
                    '٢٠٢٠-٢٠٢٤: استمرار دور الحشد في الأمن والاستقرار'
                ],
                'eng_content': 'PMF Formation Timeline:\nJune 10, 2014: Fall of Mosul to ISIS\nJune 13, 2014: Grand Ayatollah Sistani\'s fatwa for sufficient jihad\nJune 15, 2014: Beginning of volunteer units formation\nNovember 26, 2016: Approval of PMF Law No. 40\n2017: Official integration of PMF into Iraqi Armed Forces\n2018: Formation of PMF Operations Command\n2019: Appointment of PMF Commander-in-Chief\n2020-2024: Continued PMF role in security and stability',
                'bg_color': 'dark_green'
            },
            {
                'title': 'العمليات العسكرية الكبرى للحشد الشعبي',
                'content': [
                    '• عملية تحرير تكريت (مارس ٢٠١٥)',
                    '• عملية تحرير بيجي ومصفاة بيجي (٢٠١٥)',
                    '• عملية تحرير الرمادي والفلوجة (٢٠١٦)',
                    '• عملية تحرير الموصل (٢٠١٦-٢٠١٧)',
                    '• عملية تحرير تلعفر (٢٠١٧)',
                    '• عملية تحرير الحويجة (٢٠١٧)',
                    '• عملية تحرير القائم وراوة (٢٠١٧)',
                    '• عمليات تأمين الحدود العراقية السورية',
                    '• عمليات مكافحة الإرهاب في الصحراء الغربية'
                ],
                'eng_content': 'Major PMF Military Operations:\n• Operation Liberation of Tikrit (March 2015)\n• Operation Liberation of Baiji and Baiji Refinery (2015)\n• Operation Liberation of Ramadi and Fallujah (2016)\n• Operation Liberation of Mosul (2016-2017)\n• Operation Liberation of Tal Afar (2017)\n• Operation Liberation of Hawija (2017)\n• Operation Liberation of Al-Qaim and Rawa (2017)\n• Iraqi-Syrian border security operations\n• Counter-terrorism operations in Western Desert',
                'bg_color': 'red'
            },
            {
                'title': 'المدن والمناطق المحررة من داعش',
                'content': [
                    '• محافظة صلاح الدين: تكريت، بيجي، سامراء، الدور',
                    '• محافظة الأنبار: الرمادي، الفلوجة، هيت، القائم، راوة',
                    '• محافظة نينوى: الموصل، تلعفر، سنجار، الحمدانية',
                    '• محافظة ديالى: المقدادية، خانقين، جلولاء',
                    '• محافظة كركوك: الحويجة، الرياض، الرشاد',
                    '• المناطق الحدودية مع سوريا والأردن',
                    '• الصحراء الغربية ومناطق البادية',
                    '• أكثر من ١٠٠ ألف كيلومتر مربع من الأراضي العراقية'
                ],
                'eng_content': 'Cities and Regions Liberated from ISIS:\n• Salah al-Din Province: Tikrit, Baiji, Samarra, Al-Dur\n• Anbar Province: Ramadi, Fallujah, Hit, Al-Qaim, Rawa\n• Nineveh Province: Mosul, Tal Afar, Sinjar, Hamdaniya\n• Diyala Province: Muqdadiyah, Khanaqin, Jalawla\n• Kirkuk Province: Hawija, Al-Riyadh, Al-Rashad\n• Border areas with Syria and Jordan\n• Western Desert and Badiya regions\n• More than 100,000 square kilometers of Iraqi territory',
                'bg_color': 'black'
            },
            {
                'title': 'تكريم شهداء الحشد الشعبي الأبرار',
                'content': [
                    '• أكثر من ٧٠٠٠ شهيد من أبناء العراق الأبرار',
                    '• قادة وجنود ضحوا بأرواحهم دفاعاً عن الوطن',
                    '• من جميع محافظات العراق ومن مختلف الطوائف والقوميات',
                    '• استشهدوا في معارك التحرير ضد الإرهاب',
                    '• أرواحهم الطاهرة ترفرف في جنان الخلد',
                    '• ذكراهم العطرة محفورة في قلوب العراقيين',
                    '• دماؤهم الزكية سقت أرض العراق الطاهرة',
                    '• رمز للتضحية والفداء من أجل الوطن'
                ],
                'prayer': 'رحمهم الله وأسكنهم فسيح جناته',
                'eng_content': 'Honoring PMF Righteous Martyrs:\n• More than 7000 martyrs from the noble sons of Iraq\n• Leaders and soldiers who sacrificed their lives defending the homeland\n• From all provinces of Iraq and from different sects and ethnicities\n• Martyred in liberation battles against terrorism\n• Their pure souls soar in the gardens of eternity\n• Their fragrant memory is engraved in the hearts of Iraqis\n• Their pure blood watered the sacred land of Iraq\n• Symbol of sacrifice and devotion for the homeland\n\nMay Allah have mercy on them and grant them spacious gardens',
                'bg_color': 'navy_blue'
            },
            {
                'title': 'الدور الحالي للحشد الشعبي في الأمن العراقي',
                'content': [
                    '• حماية الحدود العراقية من التهديدات الخارجية',
                    '• مكافحة الإرهاب والخلايا النائمة لداعش',
                    '• تأمين المناطق المحررة ومنع عودة الإرهاب',
                    '• المشاركة في عمليات حفظ الأمن والاستقرار',
                    '• حماية المقدسات الدينية والمواقع الحساسة',
                    '• دعم القوات المسلحة العراقية في المهام الأمنية',
                    '• المساهمة في إعادة الإعمار والتنمية',
                    '• تعزيز الوحدة الوطنية والتماسك المجتمعي'
                ],
                'eng_content': 'PMF\'s Current Role in Iraqi Security:\n• Protecting Iraqi borders from external threats\n• Counter-terrorism and fighting ISIS sleeper cells\n• Securing liberated areas and preventing terrorism return\n• Participating in security and stability operations\n• Protecting religious shrines and sensitive sites\n• Supporting Iraqi Armed Forces in security missions\n• Contributing to reconstruction and development\n• Strengthening national unity and social cohesion',
                'bg_color': 'dark_green'
            },
            {
                'title': 'دور الحشد في تعزيز السيادة العراقية',
                'content': [
                    '• تمثيل جميع مكونات الشعب العراقي',
                    '• تعزيز الوحدة الوطنية بين العراقيين',
                    '• حماية الأقليات الدينية والعرقية',
                    '• الدفاع عن السيادة الوطنية العراقية',
                    '• رفض التدخلات الخارجية في الشؤون العراقية',
                    '• تأكيد الهوية العراقية الموحدة',
                    '• بناء جيش شعبي يمثل كل العراق',
                    '• تعزيز الثقة بين المواطنين والدولة'
                ],
                'eng_content': 'PMF\'s Role in Strengthening Iraqi Sovereignty:\n• Representing all components of the Iraqi people\n• Strengthening national unity among Iraqis\n• Protecting religious and ethnic minorities\n• Defending Iraqi national sovereignty\n• Rejecting foreign interference in Iraqi affairs\n• Affirming unified Iraqi identity\n• Building a popular army representing all of Iraq\n• Strengthening trust between citizens and the state',
                'bg_color': 'red'
            },
            {
                'title': 'الاعتراف الدولي بدور الحشد الشعبي',
                'content': [
                    '• اعتراف الأمم المتحدة بدور الحشد في مكافحة الإرهاب',
                    '• تقدير المجتمع الدولي لجهود التحرير',
                    '• دعم الدول الصديقة لجهود الحشد الشعبي',
                    '• الاعتراف بالحشد كقوة شرعية ضمن الدولة العراقية',
                    '• تقدير دور الحشد في حماية الأقليات',
                    '• الإشادة الدولية بانضباط وحدات الحشد',
                    '• الاعتراف بدور الحشد في استقرار المنطقة',
                    '• تقدير المساهمة في هزيمة داعش عالمياً'
                ],
                'eng_content': 'International Recognition of PMF\'s Role:\n• UN recognition of PMF\'s role in counter-terrorism\n• International community appreciation for liberation efforts\n• Friendly nations\' support for PMF efforts\n• Recognition of PMF as legitimate force within Iraqi state\n• Appreciation of PMF\'s role in protecting minorities\n• International praise for PMF units\' discipline\n• Recognition of PMF\'s role in regional stability\n• Appreciation of contribution to defeating ISIS globally',
                'bg_color': 'navy_blue'
            },
            {
                'title': 'الحشد الشعبي درع العراق وسيفه',
                'patriotic_text': 'حماة الوطن وحراس السيادة\nرجال صدقوا ما عاهدوا الله عليه\nالعراق محفوظ بسواعد أبنائه الأبرار',
                'quranic_verse': 'وَمَن قُتِلَ مَظْلُومًا فَقَدْ جَعَلْنَا لِوَلِيِّهِ سُلْطَانًا',
                'eng_patriotic': 'PMF: Iraq\'s Shield and Sword\n\nProtectors of the homeland and guardians of sovereignty\nMen who were true to their covenant with Allah\nIraq is protected by the arms of its righteous sons\n\n"And whoever is killed unjustly - We have given his heir authority" (Quran 17:33)',
                'bg_color': 'black'
            }
        ]

    def format_arabic_text(self, text):
        """Format Arabic text for proper RTL display"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text

    def get_font_path(self, font_name, size):
        """Get font path with fallback options"""
        font_paths = [
            f"fonts/{font_name}",
            f"fonts/Amiri-Regular.ttf",
            f"fonts/NotoSansArabic-Regular.ttf",
            "arial.ttf",
            "Arial.ttf"
        ]
        
        for font_path in font_paths:
            try:
                return ImageFont.truetype(font_path, size)
            except:
                continue
        
        # Fallback to default font
        return ImageFont.load_default()

    def create_slide_image(self, slide_data, slide_num):
        """Create an image for a single slide"""
        # Create image with background color
        bg_color = self.colors[slide_data['bg_color']]
        img = Image.new('RGB', (self.video_width, self.video_height), bg_color)
        draw = ImageDraw.Draw(img)

        # Load fonts
        title_font = self.get_font_path("Amiri-Regular.ttf", 80)
        content_font = self.get_font_path("Amiri-Regular.ttf", 50)
        subtitle_font = self.get_font_path("Amiri-Regular.ttf", 45)
        eng_font = self.get_font_path("Arial.ttf", 35)

        y_position = 100

        # Title
        if 'title' in slide_data:
            title_text = self.format_arabic_text(slide_data['title'])
            
            # Get text size for centering
            bbox = draw.textbbox((0, 0), title_text, font=title_font)
            text_width = bbox[2] - bbox[0]
            x_position = (self.video_width - text_width) // 2
            
            draw.text((x_position, y_position), title_text, 
                     font=title_font, fill=self.colors['gold'])
            y_position += 150

        # Handle different slide types
        if slide_num == 1:  # Title slide
            if 'subtitle' in slide_data:
                subtitle_text = self.format_arabic_text(slide_data['subtitle'])
                bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), subtitle_text, 
                         font=subtitle_font, fill=self.colors['white'])
                y_position += 100

            if 'eng_subtitle' in slide_data:
                eng_lines = slide_data['eng_subtitle'].split('\n')
                for line in eng_lines:
                    bbox = draw.textbbox((0, 0), line, font=eng_font)
                    text_width = bbox[2] - bbox[0]
                    x_position = (self.video_width - text_width) // 2
                    draw.text((x_position, y_position), line, 
                             font=eng_font, fill=self.colors['white'])
                    y_position += 50

            if 'date' in slide_data:
                date_text = self.format_arabic_text(slide_data['date'])
                bbox = draw.textbbox((0, 0), date_text, font=subtitle_font)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position + 50), date_text, 
                         font=subtitle_font, fill=self.colors['red'])

            if 'slogan' in slide_data:
                slogan_text = self.format_arabic_text(slide_data['slogan'])
                bbox = draw.textbbox((0, 0), slogan_text, font=eng_font)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position + 150), slogan_text,
                         font=eng_font, fill=self.colors['gold'])

        elif slide_num == 10:  # Closing slide
            if 'patriotic_text' in slide_data:
                patriotic_lines = slide_data['patriotic_text'].split('\n')
                for line in patriotic_lines:
                    formatted_line = self.format_arabic_text(line)
                    bbox = draw.textbbox((0, 0), formatted_line, font=content_font)
                    text_width = bbox[2] - bbox[0]
                    x_position = (self.video_width - text_width) // 2
                    draw.text((x_position, y_position), formatted_line,
                             font=content_font, fill=self.colors['white'])
                    y_position += 80

            if 'quranic_verse' in slide_data:
                y_position += 50
                verse_text = self.format_arabic_text(slide_data['quranic_verse'])
                bbox = draw.textbbox((0, 0), verse_text, font=subtitle_font)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), verse_text,
                         font=subtitle_font, fill=self.colors['gold'])

        else:  # Content slides
            if 'content' in slide_data:
                for item in slide_data['content']:
                    formatted_item = self.format_arabic_text(item)
                    # Right-align Arabic content
                    bbox = draw.textbbox((0, 0), formatted_item, font=content_font)
                    text_width = bbox[2] - bbox[0]
                    x_position = self.video_width - text_width - 100
                    draw.text((x_position, y_position), formatted_item,
                             font=content_font, fill=self.colors['white'])
                    y_position += 60

            if 'prayer' in slide_data:
                y_position += 50
                prayer_text = self.format_arabic_text(slide_data['prayer'])
                bbox = draw.textbbox((0, 0), prayer_text, font=subtitle_font)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), prayer_text,
                         font=subtitle_font, fill=self.colors['gold'])

        return img

    def create_video(self):
        """Create the complete video from slides"""
        print("🎬 Creating PMF Establishment Commemoration Video...")

        # Create slide images
        slide_images = []
        for i, slide_data in enumerate(self.slides_data, 1):
            print(f"📄 Creating slide {i}...")
            img = self.create_slide_image(slide_data, i)

            # Save temporary image
            temp_filename = f"temp_pmf_slide_{i}.png"
            img.save(temp_filename)
            slide_images.append(temp_filename)

        # Create video clips from images
        clips = []
        for i, img_path in enumerate(slide_images):
            print(f"🎞️ Processing slide {i+1} for video...")
            clip = ImageClip(img_path, duration=self.slide_duration)
            clips.append(clip)

        # Add smooth transitions (crossfade)
        print("🎨 Adding smooth transitions...")
        final_clips = []
        for i, clip in enumerate(clips):
            if i == 0:
                # First clip - no transition
                final_clips.append(clip)
            else:
                # Add crossfade transition
                transition_duration = 1.0  # 1 second crossfade

                # Adjust previous clip to overlap
                if len(final_clips) > 0:
                    prev_clip = final_clips[-1]
                    # Reduce previous clip duration by transition time
                    prev_clip = prev_clip.set_duration(prev_clip.duration - transition_duration/2)
                    final_clips[-1] = prev_clip

                # Add current clip with fade in
                current_clip = clip.fadein(transition_duration/2)
                final_clips.append(current_clip)

        # Create final video
        print("🎥 Creating final video...")
        final_video = concatenate_videoclips(final_clips, method="compose")

        # Set video properties
        final_video = final_video.set_fps(self.fps)

        # Export video
        output_filename = "PMF_Establishment_Video.mp4"
        print(f"💾 Exporting video as {output_filename}...")

        final_video.write_videofile(
            output_filename,
            fps=self.fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            ffmpeg_params=['-crf', '18']  # High quality
        )

        # Clean up temporary files
        print("🧹 Cleaning up temporary files...")
        for img_path in slide_images:
            try:
                os.remove(img_path)
            except:
                pass

        print(f"✅ Video created successfully: {output_filename}")
        print(f"📊 Video specifications:")
        print(f"   • Resolution: {self.video_width}x{self.video_height}")
        print(f"   • Frame Rate: {self.fps} FPS")
        print(f"   • Duration: ~{len(self.slides_data) * self.slide_duration} seconds ({len(self.slides_data) * self.slide_duration / 60:.1f} minutes)")
        print(f"   • Format: MP4 (H.264)")
        print(f"   • Slides: {len(self.slides_data)}")
        print(f"   • Slide Duration: {self.slide_duration} seconds each")

        return output_filename

def main():
    """Main function to create the video"""
    try:
        generator = PMFEstablishmentVideoGenerator()
        video_file = generator.create_video()

        print(f"\n🎉 Successfully created video: {video_file}")
        print("\n🇮🇶 This video is ready for:")
        print("• Official government commemorative events")
        print("• Educational institutions and universities")
        print("• Military academies and training centers")
        print("• Community centers and cultural organizations")
        print("• Social media sharing (WhatsApp, Telegram, Instagram, Facebook)")
        print("• International conferences on counter-terrorism")
        print("• Media presentations and documentaries")
        print("• Digital archives for historical preservation")
        print("• Patriotic gatherings and national celebrations")

        print(f"\n📍 File location: {os.path.abspath(video_file)}")
        print("\n🌟 Video features:")
        print("• High-quality 1080p Full HD resolution")
        print("• Proper Arabic RTL text formatting with diacritical marks")
        print("• Iraqi flag patriotic color scheme")
        print("• Smooth slide transitions (crossfade effects)")
        print("• 5-second slides optimized for commemorative viewing")
        print("• Culturally appropriate for Iraqi/Arab patriotic audiences")
        print("• Professional H.264 encoding for broad compatibility")
        print("• Optimized for social media and presentation systems")
        print("• Bilingual content (Arabic RTL + English)")
        print("• Comprehensive historical coverage of PMF establishment")

        print("\n📋 Video Content Summary:")
        print("1. Title Slide - PMF Establishment")
        print("2. Historical Context - Background of Formation")
        print("3. Formation Timeline - Key Dates and Milestones")
        print("4. Major Operations - Military Achievements")
        print("5. Territorial Liberation - Cities and Regions Freed")
        print("6. Martyrs Memorial - Honoring the Fallen Heroes")
        print("7. Current Role - Ongoing Security Contributions")
        print("8. National Unity - Strengthening Iraqi Sovereignty")
        print("9. International Recognition - Global Acknowledgment")
        print("10. Patriotic Conclusion - Iraq's Shield and Sword")

    except Exception as e:
        print(f"❌ Error creating video: {str(e)}")
        print("Please ensure all required packages are installed:")
        print("pip install moviepy pillow arabic-reshaper python-bidi")

if __name__ == "__main__":
    main()
