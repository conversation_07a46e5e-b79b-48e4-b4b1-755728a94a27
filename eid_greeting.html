<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهنئة عيد الأضحى المبارك</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;500;700&display=swap');
        
        :root {
            --primary-color: #2E7D32;
            --secondary-color: #1B5E20;
            --accent-color: #81C784;
            --text-color: #333;
            --light-color: #f5f5f5;
            --gold-color: #FFD700;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', 'Amiri', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }
        
        body::before {
            content: '🌙';
            position: fixed;
            top: 20px;
            right: 20px;
            font-size: 2rem;
            animation: float 3s ease-in-out infinite;
            opacity: 0.3;
        }
        
        body::after {
            content: '🕌';
            position: fixed;
            bottom: 20px;
            left: 20px;
            font-size: 2rem;
            animation: float 3s ease-in-out infinite reverse;
            opacity: 0.3;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }
        
        .greeting-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 800px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: fadeIn 1s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .greeting-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }
        
        .greeting-card::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
        }
        
        .greeting-title {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            display: inline-block;
        }
        
        .greeting-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: var(--gold-color);
            border-radius: 3px;
        }
        
        .greeting-text {
            color: var(--text-color);
            font-size: 1.2rem;
            line-height: 1.8;
            margin-bottom: 30px;
        }
        
        .greeting-text p {
            margin: 15px 0;
            transition: transform 0.3s ease;
        }
        
        .greeting-text p:hover {
            transform: scale(1.05);
            color: var(--primary-color);
        }
        
        .decoration {
            font-size: 3rem;
            color: var(--accent-color);
            margin: 20px 0;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .download-section {
            margin-top: 30px;
            text-align: center;
        }
        
        .download-btn {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .download-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transition: 0.5s;
        }
        
        .download-btn:hover::before {
            left: 100%;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            display: none;
            font-weight: 500;
            animation: fadeIn 0.3s ease-out;
        }
        
        .success {
            background: rgba(144, 238, 144, 0.2);
            border: 1px solid rgba(144, 238, 144, 0.5);
            color: #2e8b57;
        }
        
        .error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff0000;
        }
        
        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .floating-element {
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.1;
            animation: float-around 15s linear infinite;
        }
        
        @keyframes float-around {
            0% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(100px, 100px) rotate(90deg); }
            50% { transform: translate(0, 200px) rotate(180deg); }
            75% { transform: translate(-100px, 100px) rotate(270deg); }
            100% { transform: translate(0, 0) rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .greeting-card {
                padding: 20px;
            }
            
            .greeting-title {
                font-size: 2rem;
            }
            
            .greeting-text {
                font-size: 1rem;
            }
            
            .decoration {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floating-elements"></div>
    
    <div class="greeting-card animate__animated animate__fadeIn" id="greeting-card">
        <div class="decoration">🌙</div>
        <h1 class="greeting-title">عيد الأضحى المبارك</h1>
        <div class="greeting-text">
            <p>كل عام وأنتم بخير</p>
            <p>نسأل الله تعالى أن يتقبل منا ومنكم صالح الأعمال</p>
            <p>وأن يمن علينا جميعاً بالصحة والعافية</p>
            <p>وأن يجعل هذا العيد فرحة وسعادة على جميع المسلمين</p>
        </div>
        <div class="decoration">🕌</div>
    </div>
    
    <div class="download-section">
        <button class="download-btn" onclick="downloadGreeting()">
            تحميل التهنئة
        </button>
        <div class="status" id="status"></div>
    </div>
    
    <script>
        // إضافة العناصر المتحركة في الخلفية
        function createFloatingElements() {
            const container = document.getElementById('floating-elements');
            const elements = ['🌙', '🕌', '✨', '🌟'];
            
            for (let i = 0; i < 10; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.textContent = elements[Math.floor(Math.random() * elements.length)];
                element.style.left = Math.random() * 100 + 'vw';
                element.style.top = Math.random() * 100 + 'vh';
                element.style.animationDelay = Math.random() * 5 + 's';
                container.appendChild(element);
            }
        }
        
        createFloatingElements();
        
        async function downloadGreeting() {
            try {
                const status = document.getElementById('status');
                status.textContent = 'جاري إنشاء الصورة...';
                status.className = 'status success';
                status.style.display = 'block';
                
                const downloadSection = document.querySelector('.download-section');
                const originalDisplay = downloadSection.style.display;
                downloadSection.style.display = 'none';
                
                await new Promise(resolve => setTimeout(resolve, 100));
                
                const element = document.getElementById('greeting-card');
                const canvas = await html2canvas(element, {
                    scale: 2,
                    backgroundColor: '#ffffff',
                    allowTaint: true,
                    useCORS: true,
                    logging: false
                });
                
                canvas.toBlob(function(blob) {
                    if (!blob) {
                        throw new Error('فشل في إنشاء الصورة');
                    }
                    
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'تهنئة_عيد_الأضحى.png';
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    setTimeout(() => {
                        URL.revokeObjectURL(url);
                    }, 1000);
                    
                    downloadSection.style.display = originalDisplay;
                    status.textContent = 'تم تحميل التهنئة بنجاح! ✅';
                    
                    setTimeout(() => {
                        status.style.display = 'none';
                    }, 5000);
                    
                }, 'image/png', 0.95);
                
            } catch (error) {
                console.error('خطأ في التحميل:', error);
                
                document.querySelector('.download-section').style.display = 'block';
                
                const status = document.getElementById('status');
                status.textContent = 'حدث خطأ في تحميل الصورة: ' + error.message;
                status.className = 'status error';
                status.style.display = 'block';
                
                setTimeout(() => {
                    status.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html> 