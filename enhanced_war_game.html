<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معركة الحيانية ضد أبو الخصيب - النسخة المحسنة</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>', 'Scheherazade New', serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #f4f1e8;
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        .game-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 15px;
            display: grid;
            grid-template-columns: 280px 1fr 280px;
            grid-template-rows: auto 1fr auto;
            gap: 15px;
            min-height: 100vh;
        }

        .game-header {
            grid-column: 1 / -1;
            text-align: center;
            background: linear-gradient(45deg, #2c1810, #4a3728);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.4);
            border: 3px solid #d4af37;
            position: relative;
            overflow: hidden;
        }

        .game-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(212,175,55,0.1) 50%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        .game-title {
            font-size: 2.8rem;
            font-weight: 700;
            color: #d4af37;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.7);
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .game-subtitle {
            font-size: 1.4rem;
            color: #f4f1e8;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .team-panel {
            background: rgba(0,0,0,0.5);
            border-radius: 20px;
            padding: 20px;
            border: 3px solid;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .hayaniya-panel {
            border-color: #4169e1;
            background: linear-gradient(135deg, rgba(65,105,225,0.3), rgba(30,144,255,0.2));
        }

        .abu-khaseeb-panel {
            border-color: #dc143c;
            background: linear-gradient(135deg, rgba(220,20,60,0.3), rgba(178,34,34,0.2));
        }

        .team-name {
            font-size: 1.6rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .hayaniya-panel .team-name {
            background: linear-gradient(45deg, #4169e1, #1e90ff);
            color: white;
        }

        .abu-khaseeb-panel .team-name {
            background: linear-gradient(45deg, #dc143c, #b22222);
            color: white;
        }

        .battlefield {
            background: linear-gradient(45deg, #2c1810, #4a3728);
            border-radius: 20px;
            padding: 25px;
            border: 4px solid #8b4513;
            box-shadow: inset 0 0 60px rgba(0,0,0,0.6);
            position: relative;
        }

        .battlefield::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(212,175,55,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(139,69,19,0.1) 0%, transparent 50%);
            border-radius: 16px;
            pointer-events: none;
        }

        .game-board {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(6, 1fr);
            gap: 4px;
            background: #1a0f0a;
            padding: 20px;
            border-radius: 15px;
            aspect-ratio: 4/3;
            max-height: 550px;
            position: relative;
            z-index: 1;
        }

        .cell {
            background: linear-gradient(135deg, #8d6e63, #a1887f);
            border: 2px solid #5d4037;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            min-height: 65px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
        }

        .cell:hover {
            background: linear-gradient(135deg, #a1887f, #bcaaa4);
            transform: scale(1.08);
            box-shadow: 0 6px 20px rgba(212,175,55,0.5);
            z-index: 10;
        }

        .cell.selected {
            background: linear-gradient(135deg, #d4af37, #ffd700);
            box-shadow: 0 0 25px rgba(212,175,55,0.9);
            animation: selectedPulse 2s infinite;
        }

        @keyframes selectedPulse {
            0%, 100% { box-shadow: 0 0 25px rgba(212,175,55,0.9); }
            50% { box-shadow: 0 0 35px rgba(212,175,55,1); }
        }

        .cell.possible-move {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
            animation: movePulse 1.5s infinite;
        }

        .cell.enemy-target {
            background: linear-gradient(135deg, #f44336, #ef5350);
            animation: attackPulse 1.5s infinite;
        }

        @keyframes movePulse {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        @keyframes attackPulse {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .unit {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            border: 3px solid;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .unit:hover {
            transform: scale(1.15);
            box-shadow: 0 6px 15px rgba(0,0,0,0.4);
        }

        .hayaniya-unit {
            background: linear-gradient(135deg, #4169e1, #1e90ff);
            border-color: #0000cd;
            color: white;
        }

        .abu-khaseeb-unit {
            background: linear-gradient(135deg, #dc143c, #b22222);
            border-color: #8b0000;
            color: white;
        }

        .unit-stats {
            background: rgba(0,0,0,0.4);
            border-radius: 15px;
            padding: 18px;
            margin-bottom: 18px;
            border: 1px solid rgba(212,175,55,0.3);
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .stat-label {
            color: #d4af37;
            font-weight: 600;
        }

        .stat-value {
            color: #f4f1e8;
            font-weight: 700;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 25px;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-family: 'Amiri', serif;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s ease;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #d4af37, #ffd700);
            color: #2c1810;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(212,175,55,0.5);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #8b4513, #a0522d);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(45deg, #a0522d, #cd853f);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(139,69,19,0.4);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .game-log {
            grid-column: 1 / -1;
            background: rgba(0,0,0,0.5);
            border-radius: 20px;
            padding: 25px;
            border: 3px solid #8b4513;
            max-height: 180px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }

        .log-entry {
            margin-bottom: 10px;
            padding: 12px;
            border-radius: 8px;
            background: rgba(139,69,19,0.3);
            border-right: 5px solid #d4af37;
            font-size: 1rem;
            line-height: 1.4;
        }

        .turn-indicator {
            text-align: center;
            font-size: 1.4rem;
            font-weight: 700;
            padding: 18px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .hayaniya-turn {
            background: linear-gradient(45deg, #4169e1, #1e90ff);
            color: white;
        }

        .abu-khaseeb-turn {
            background: linear-gradient(45deg, #dc143c, #b22222);
            color: white;
        }

        .victory-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .victory-content {
            background: linear-gradient(135deg, #2c1810, #4a3728);
            padding: 50px;
            border-radius: 25px;
            text-align: center;
            border: 4px solid #d4af37;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        }

        .victory-title {
            font-size: 3rem;
            color: #d4af37;
            margin-bottom: 25px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .victory-message {
            font-size: 1.5rem;
            margin-bottom: 35px;
            line-height: 1.8;
        }

        @media (max-width: 1400px) {
            .game-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
            }
            
            .team-panel {
                order: 1;
            }
            
            .battlefield {
                order: 2;
            }
            
            .game-log {
                order: 3;
            }
        }

        @media (max-width: 768px) {
            .game-title {
                font-size: 2rem;
            }
            
            .game-board {
                gap: 3px;
                padding: 15px;
            }
            
            .cell {
                min-height: 50px;
            }
            
            .unit {
                width: 35px;
                height: 35px;
                font-size: 0.9rem;
            }
            
            .victory-title {
                font-size: 2.2rem;
            }
            
            .victory-message {
                font-size: 1.2rem;
            }
        }

        .branding {
            position: fixed;
            bottom: 25px;
            left: 25px;
            background: rgba(0,0,0,0.8);
            padding: 15px 20px;
            border-radius: 15px;
            font-size: 1rem;
            color: #d4af37;
            border: 2px solid #d4af37;
            backdrop-filter: blur(10px);
        }

        .special-effects {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #d4af37;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="special-effects" id="specialEffects"></div>

    <div class="game-container">
        <!-- Game Header -->
        <div class="game-header">
            <h1 class="game-title">معركة الحيانية ضد أبو الخصيب</h1>
            <p class="game-subtitle">النسخة المحسنة - لعبة الحرب الاستراتيجية التفاعلية</p>
        </div>

        <!-- Al-Hayaniya Team Panel -->
        <div class="team-panel hayaniya-panel">
            <div class="team-name">فريق الحيانية</div>

            <div class="turn-indicator" id="turnIndicator">
                دور فريق الحيانية
            </div>

            <div class="unit-stats">
                <h3 style="color: #d4af37; margin-bottom: 15px; text-align: center;">إحصائيات الفريق</h3>
                <div class="stat-row">
                    <span class="stat-label">الوحدات المتبقية:</span>
                    <span class="stat-value" id="hayaniyaUnits">8</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">النقاط:</span>
                    <span class="stat-value" id="hayaniyaScore">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">القوة الإجمالية:</span>
                    <span class="stat-value" id="hayaniyaPower">80</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">الدور:</span>
                    <span class="stat-value" id="turnCounter">1</span>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="resetGame()">لعبة جديدة</button>
                <button class="btn btn-secondary" onclick="showInstructions()">التعليمات</button>
                <button class="btn btn-secondary" onclick="toggleSound()" id="soundBtn">الصوت: تشغيل</button>
                <button class="btn btn-secondary" onclick="toggleEffects()" id="effectsBtn">التأثيرات: تشغيل</button>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: rgba(65,105,225,0.2); border-radius: 10px;">
                <h4 style="color: #4169e1; margin-bottom: 10px;">خصائص الفريق:</h4>
                <ul style="list-style: none; padding: 0; font-size: 0.95rem;">
                    <li>• سرعة حركة عادية</li>
                    <li>• قوة هجوم متوسطة</li>
                    <li>• دفاع متوازن</li>
                    <li>• تكتيكات مرنة</li>
                </ul>
            </div>
        </div>

        <!-- Battlefield -->
        <div class="battlefield">
            <div class="game-board" id="gameBoard">
                <!-- Game cells will be generated by JavaScript -->
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <div style="background: rgba(0,0,0,0.4); padding: 15px; border-radius: 12px; display: inline-block; border: 1px solid rgba(212,175,55,0.3);">
                    <span style="color: #d4af37; font-weight: 600; font-size: 1.1rem;">الوحدة المحددة: </span>
                    <span id="selectedUnitInfo" style="color: #f4f1e8; font-size: 1rem;">لا توجد وحدة محددة</span>
                </div>
            </div>
        </div>

        <!-- Abu Al-Khaseeb Team Panel -->
        <div class="team-panel abu-khaseeb-panel">
            <div class="team-name">فريق أبو الخصيب</div>

            <div class="unit-stats">
                <h3 style="color: #d4af37; margin-bottom: 15px; text-align: center;">إحصائيات الفريق</h3>
                <div class="stat-row">
                    <span class="stat-label">الوحدات المتبقية:</span>
                    <span class="stat-value" id="abuKhaseebUnits">8</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">النقاط:</span>
                    <span class="stat-value" id="abuKhaseebScore">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">القوة الإجمالية:</span>
                    <span class="stat-value" id="abuKhaseebPower">120</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">معدل الفوز:</span>
                    <span class="stat-value" id="winRate">75%</span>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: rgba(220,20,60,0.2); border-radius: 10px;">
                <h4 style="color: #dc143c; margin-bottom: 10px;">مزايا الفريق:</h4>
                <ul style="list-style: none; padding: 0; font-size: 0.95rem;">
                    <li>• قوة هجوم عالية (+25%)</li>
                    <li>• دفاع محصن (+30%)</li>
                    <li>• وحدات متخصصة</li>
                    <li>• تكتيكات متقدمة</li>
                    <li>• موارد إضافية</li>
                </ul>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: rgba(139,69,19,0.3); border-radius: 10px;">
                <h4 style="color: #d4af37; margin-bottom: 10px;">أنواع الوحدات:</h4>
                <div style="font-size: 0.9rem; line-height: 1.6;">
                    <div><span style="color: #4169e1;">🛡️ مشاة:</span> دفاع قوي، حركة بطيئة</div>
                    <div><span style="color: #dc143c;">⚔️ مقاتلين:</span> هجوم سريع، دفاع ضعيف</div>
                    <div><span style="color: #ffd700;">🏹 رماة:</span> مدى بعيد، ضرر عالي</div>
                    <div><span style="color: #ff6b35;">🐎 فرسان:</span> حركة سريعة، هجوم متوسط</div>
                </div>
            </div>
        </div>

        <!-- Game Log -->
        <div class="game-log">
            <h3 style="color: #d4af37; margin-bottom: 15px; text-align: center;">سجل المعركة</h3>
            <div id="gameLog">
                <div class="log-entry">مرحباً بكم في النسخة المحسنة من معركة الحيانية ضد أبو الخصيب! اختر وحدة للبدء.</div>
            </div>
        </div>
    </div>

    <!-- Victory Modal -->
    <div class="victory-modal" id="victoryModal">
        <div class="victory-content">
            <h2 class="victory-title" id="victoryTitle">انتهت المعركة!</h2>
            <p class="victory-message" id="victoryMessage"></p>
            <div style="margin: 30px 0;">
                <div style="background: rgba(0,0,0,0.3); padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <h4 style="color: #d4af37; margin-bottom: 15px;">إحصائيات المعركة:</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; text-align: right;">
                        <div>الأدوار المنجزة: <span id="finalTurns">0</span></div>
                        <div>الوحدات المهزومة: <span id="finalDefeated">0</span></div>
                        <div>النقاط النهائية: <span id="finalScore">0</span></div>
                        <div>مدة المعركة: <span id="battleDuration">0</span> دقيقة</div>
                    </div>
                </div>
            </div>
            <button class="btn btn-primary" onclick="resetGame(); closeVictoryModal();">معركة جديدة</button>
        </div>
    </div>

    <!-- Branding -->
    <div class="branding">
        AliToucan Gaming - Enhanced Edition
    </div>

    <script>
        // Enhanced Game State
        let gameState = {
            currentPlayer: 'hayaniya',
            selectedUnit: null,
            selectedCell: null,
            gameBoard: Array(6).fill().map(() => Array(8).fill(null)),
            gameOver: false,
            soundEnabled: true,
            effectsEnabled: true,
            turnCount: 1,
            startTime: Date.now(),
            totalDefeated: 0,
            battleHistory: []
        };

        // Enhanced Unit Types with special abilities
        const unitTypes = {
            infantry: {
                symbol: '🛡️',
                attack: 10,
                defense: 15,
                health: 100,
                range: 1,
                movement: 1,
                special: 'shield_wall' // Can boost nearby allies' defense
            },
            fighter: {
                symbol: '⚔️',
                attack: 20,
                defense: 8,
                health: 80,
                range: 1,
                movement: 2,
                special: 'berserker' // Extra damage when health is low
            },
            archer: {
                symbol: '🏹',
                attack: 15,
                defense: 5,
                health: 60,
                range: 3,
                movement: 1,
                special: 'piercing_shot' // Can attack through units
            },
            cavalry: {
                symbol: '🐎',
                attack: 18,
                defense: 10,
                health: 90,
                range: 1,
                movement: 3,
                special: 'charge' // Extra damage when moving before attack
            }
        };

        // Enhanced team configurations
        const teams = {
            hayaniya: {
                name: 'الحيانية',
                color: 'hayaniya',
                units: [
                    { type: 'infantry', attack: 10, defense: 15, health: 100 },
                    { type: 'fighter', attack: 20, defense: 8, health: 80 },
                    { type: 'archer', attack: 15, defense: 5, health: 60 },
                    { type: 'cavalry', attack: 18, defense: 10, health: 90 }
                ]
            },
            abukhaseeb: {
                name: 'أبو الخصيب',
                color: 'abu-khaseeb',
                units: [
                    { type: 'infantry', attack: 15, defense: 20, health: 120 }, // Enhanced stats
                    { type: 'fighter', attack: 25, defense: 12, health: 100 },
                    { type: 'archer', attack: 20, defense: 8, health: 80 },
                    { type: 'cavalry', attack: 22, defense: 15, health: 110 }
                ]
            }
        };

        // Sound effects (placeholder - would use actual audio files)
        const sounds = {
            move: () => gameState.soundEnabled && console.log('🔊 Move sound'),
            attack: () => gameState.soundEnabled && console.log('🔊 Attack sound'),
            victory: () => gameState.soundEnabled && console.log('🔊 Victory sound'),
            defeat: () => gameState.soundEnabled && console.log('🔊 Defeat sound')
        };

        // Initialize enhanced game
        function initGame() {
            createGameBoard();
            setupInitialUnits();
            updateUI();
            createParticleEffects();
            addToLog('بدأت المعركة المحسنة! فريق الحيانية يبدأ أولاً.');
            gameState.startTime = Date.now();
        }

        // Create particle effects
        function createParticleEffects() {
            if (!gameState.effectsEnabled) return;

            const effectsContainer = document.getElementById('specialEffects');

            // Create floating particles
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (4 + Math.random() * 4) + 's';
                    effectsContainer.appendChild(particle);

                    // Remove particle after animation
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 8000);
                }, i * 300);
            }
        }

        // Enhanced create game board
        function createGameBoard() {
            const board = document.getElementById('gameBoard');
            board.innerHTML = '';

            for (let row = 0; row < 6; row++) {
                for (let col = 0; col < 8; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    cell.onclick = () => handleCellClick(row, col);

                    // Add terrain variation
                    if (Math.random() < 0.1) {
                        cell.style.background = 'linear-gradient(135deg, #6d4c41, #8d6e63)';
                        cell.title = 'تضاريس صعبة - حركة أبطأ';
                    }

                    board.appendChild(cell);
                }
            }
        }

        // Enhanced setup initial units
        function setupInitialUnits() {
            gameState.gameBoard = Array(6).fill().map(() => Array(8).fill(null));

            // Place Hayaniya units (top rows)
            const hayaniyaUnits = ['infantry', 'fighter', 'archer', 'cavalry', 'infantry', 'fighter', 'archer', 'cavalry'];
            for (let i = 0; i < 8; i++) {
                gameState.gameBoard[0][i] = createEnhancedUnit('hayaniya', hayaniyaUnits[i], 0, i);
            }

            // Place Abu Khaseeb units (bottom rows)
            const abuKhaseebUnits = ['cavalry', 'archer', 'fighter', 'infantry', 'infantry', 'fighter', 'archer', 'cavalry'];
            for (let i = 0; i < 8; i++) {
                gameState.gameBoard[5][i] = createEnhancedUnit('abukhaseeb', abuKhaseebUnits[i], 5, i);
            }

            renderBoard();
        }

        // Create enhanced unit object
        function createEnhancedUnit(team, type, row, col) {
            const baseStats = unitTypes[type];
            const teamStats = teams[team].units.find(u => u.type === type);

            return {
                id: `${team}_${type}_${row}_${col}_${Date.now()}`,
                team: team,
                type: type,
                symbol: baseStats.symbol,
                attack: teamStats.attack,
                defense: teamStats.defense,
                health: teamStats.health,
                maxHealth: teamStats.health,
                range: baseStats.range,
                movement: baseStats.movement,
                special: baseStats.special,
                row: row,
                col: col,
                hasMoved: false,
                hasAttacked: false,
                experience: 0,
                kills: 0
            };
        }

        // Enhanced render board with animations
        function renderBoard() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);
                const unit = gameState.gameBoard[row][col];

                cell.innerHTML = '';
                cell.className = 'cell';

                if (unit) {
                    const unitElement = document.createElement('div');
                    unitElement.className = `unit ${unit.team}-unit`;
                    unitElement.innerHTML = unit.symbol;

                    // Enhanced tooltip with more info
                    unitElement.title = `${unit.type} - HP: ${unit.health}/${unit.maxHealth}\nهجوم: ${unit.attack} | دفاع: ${unit.defense}\nخبرة: ${unit.experience} | قتلى: ${unit.kills}`;

                    // Health bar
                    const healthBar = document.createElement('div');
                    healthBar.style.cssText = `
                        position: absolute;
                        bottom: -2px;
                        left: 2px;
                        right: 2px;
                        height: 4px;
                        background: rgba(255,0,0,0.3);
                        border-radius: 2px;
                    `;

                    const healthFill = document.createElement('div');
                    healthFill.style.cssText = `
                        height: 100%;
                        background: ${unit.health > unit.maxHealth * 0.6 ? '#4caf50' : unit.health > unit.maxHealth * 0.3 ? '#ff9800' : '#f44336'};
                        width: ${(unit.health / unit.maxHealth) * 100}%;
                        border-radius: 2px;
                        transition: all 0.3s ease;
                    `;

                    healthBar.appendChild(healthFill);
                    unitElement.appendChild(healthBar);

                    // Experience indicator
                    if (unit.experience > 0) {
                        const expIndicator = document.createElement('div');
                        expIndicator.style.cssText = `
                            position: absolute;
                            top: -2px;
                            right: -2px;
                            width: 8px;
                            height: 8px;
                            background: #ffd700;
                            border-radius: 50%;
                            border: 1px solid #fff;
                        `;
                        unitElement.appendChild(expIndicator);
                    }

                    cell.appendChild(unitElement);
                }
            });
        }

        // Enhanced cell click handler
        function handleCellClick(row, col) {
            if (gameState.gameOver) return;

            const clickedUnit = gameState.gameBoard[row][col];

            // If no unit is selected
            if (!gameState.selectedUnit) {
                if (clickedUnit && clickedUnit.team === gameState.currentPlayer) {
                    selectUnit(clickedUnit, row, col);
                }
                return;
            }

            // If clicking on the same unit, deselect
            if (gameState.selectedUnit &&
                gameState.selectedCell.row === row &&
                gameState.selectedCell.col === col) {
                deselectUnit();
                return;
            }

            // If clicking on another friendly unit, select it
            if (clickedUnit && clickedUnit.team === gameState.currentPlayer) {
                selectUnit(clickedUnit, row, col);
                return;
            }

            // If clicking on empty cell or enemy unit, try to move/attack
            if (gameState.selectedUnit) {
                if (clickedUnit && clickedUnit.team !== gameState.currentPlayer) {
                    attemptEnhancedAttack(gameState.selectedUnit, clickedUnit, row, col);
                } else if (!clickedUnit) {
                    attemptEnhancedMove(gameState.selectedUnit, row, col);
                }
            }
        }

        // Enhanced attack with special abilities
        function attemptEnhancedAttack(attacker, defender, targetRow, targetCol) {
            const distance = Math.abs(targetRow - attacker.row) + Math.abs(targetCol - attacker.col);

            if (attacker.hasAttacked) {
                addToLog(`${attacker.type} قد هاجم بالفعل في هذا الدور.`);
                return;
            }

            if (distance > attacker.range) {
                addToLog(`الهدف بعيد جداً للهجوم.`);
                return;
            }

            // Calculate enhanced damage with special abilities
            let baseDamage = attacker.attack - defender.defense;
            let specialBonus = 0;

            // Apply special abilities
            switch (attacker.special) {
                case 'berserker':
                    if (attacker.health < attacker.maxHealth * 0.5) {
                        specialBonus = 5;
                        addToLog(`${attacker.type} دخل في حالة جنون القتال! ضرر إضافي!`);
                    }
                    break;
                case 'charge':
                    if (attacker.hasMoved) {
                        specialBonus = 3;
                        addToLog(`${attacker.type} شن هجمة اندفاع قوية!`);
                    }
                    break;
                case 'piercing_shot':
                    specialBonus = 2;
                    addToLog(`${attacker.type} أطلق سهماً خارقاً!`);
                    break;
            }

            const totalDamage = Math.max(1, baseDamage + specialBonus + Math.floor(Math.random() * 8) - 4);
            defender.health -= totalDamage;
            attacker.hasAttacked = true;
            attacker.experience += 1;

            // Visual effect
            if (gameState.effectsEnabled) {
                createAttackEffect(targetRow, targetCol);
            }

            sounds.attack();

            addToLog(`${teams[attacker.team].name}: ${attacker.type} هاجم ${teams[defender.team].name}: ${defender.type} وألحق ${totalDamage} ضرر.`);

            // Check if defender is defeated
            if (defender.health <= 0) {
                gameState.gameBoard[targetRow][targetCol] = null;
                attacker.kills += 1;
                attacker.experience += 2;
                gameState.totalDefeated += 1;

                addToLog(`${teams[defender.team].name}: ${defender.type} تم القضاء عليه! ${attacker.type} حصل على خبرة إضافية.`);
                updateScore(attacker.team, 15);

                // Level up system
                if (attacker.experience >= 5) {
                    levelUpUnit(attacker);
                }
            }

            deselectUnit();
            renderBoard();
            updateUI();

            if (checkVictory()) return;
            checkTurnEnd();
        }

        // Enhanced move with terrain effects
        function attemptEnhancedMove(unit, targetRow, targetCol) {
            const distance = Math.abs(targetRow - unit.row) + Math.abs(targetCol - unit.col);

            if (unit.hasMoved) {
                addToLog(`${unit.type} قد تحرك بالفعل في هذا الدور.`);
                return;
            }

            if (distance > unit.movement) {
                addToLog(`المسافة بعيدة جداً للحركة.`);
                return;
            }

            // Move unit with animation
            gameState.gameBoard[unit.row][unit.col] = null;
            unit.row = targetRow;
            unit.col = targetCol;
            unit.hasMoved = true;
            gameState.gameBoard[targetRow][targetCol] = unit;

            sounds.move();
            addToLog(`${teams[unit.team].name}: ${unit.type} تحرك إلى موقع جديد.`);

            deselectUnit();
            renderBoard();
            checkTurnEnd();
        }

        // Create attack visual effect
        function createAttackEffect(row, col) {
            const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            if (!cell) return;

            const effect = document.createElement('div');
            effect.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                width: 20px;
                height: 20px;
                background: radial-gradient(circle, #ff4444, transparent);
                border-radius: 50%;
                transform: translate(-50%, -50%);
                animation: attackFlash 0.5s ease-out;
                pointer-events: none;
                z-index: 100;
            `;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes attackFlash {
                    0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
                    50% { transform: translate(-50%, -50%) scale(2); opacity: 0.8; }
                    100% { transform: translate(-50%, -50%) scale(4); opacity: 0; }
                }
            `;
            document.head.appendChild(style);

            cell.appendChild(effect);
            setTimeout(() => {
                if (effect.parentNode) effect.parentNode.removeChild(effect);
                if (style.parentNode) style.parentNode.removeChild(style);
            }, 500);
        }

        // Level up system
        function levelUpUnit(unit) {
            unit.experience = 0;
            unit.attack += 2;
            unit.defense += 1;
            unit.maxHealth += 10;
            unit.health = Math.min(unit.health + 10, unit.maxHealth);

            addToLog(`🌟 ${unit.type} ارتقى إلى مستوى أعلى! زيادة في القوة والدفاع!`);

            if (gameState.effectsEnabled) {
                // Level up visual effect would go here
            }
        }

        // Enhanced UI updates
        function updateUI() {
            const hayaniyaUnits = getAllUnitsForTeam('hayaniya');
            const abuKhaseebUnits = getAllUnitsForTeam('abukhaseeb');

            // Update unit counts
            document.getElementById('hayaniyaUnits').textContent = hayaniyaUnits.length;
            document.getElementById('abuKhaseebUnits').textContent = abuKhaseebUnits.length;

            // Update power levels
            const hayaniyaPower = hayaniyaUnits.reduce((total, unit) => total + unit.health, 0);
            const abuKhaseebPower = abuKhaseebUnits.reduce((total, unit) => total + unit.health, 0);
            document.getElementById('hayaniyaPower').textContent = hayaniyaPower;
            document.getElementById('abuKhaseebPower').textContent = abuKhaseebPower;

            // Update turn counter
            document.getElementById('turnCounter').textContent = gameState.turnCount;

            // Update turn indicator
            const turnIndicator = document.getElementById('turnIndicator');
            if (gameState.currentPlayer === 'hayaniya') {
                turnIndicator.textContent = 'دور فريق الحيانية';
                turnIndicator.className = 'turn-indicator hayaniya-turn';
            } else {
                turnIndicator.textContent = 'دور فريق أبو الخصيب';
                turnIndicator.className = 'turn-indicator abu-khaseeb-turn';
            }
        }

        // Enhanced end turn
        function endTurn() {
            const currentTeamUnits = getAllUnitsForTeam(gameState.currentPlayer);
            currentTeamUnits.forEach(unit => {
                unit.hasMoved = false;
                unit.hasAttacked = false;
            });

            gameState.currentPlayer = gameState.currentPlayer === 'hayaniya' ? 'abukhaseeb' : 'hayaniya';

            if (gameState.currentPlayer === 'hayaniya') {
                gameState.turnCount++;
            }

            addToLog(`الدور ${gameState.turnCount}: دور ${teams[gameState.currentPlayer].name}`);

            updateUI();
            deselectUnit();

            // Regenerate particle effects periodically
            if (gameState.turnCount % 3 === 0) {
                createParticleEffects();
            }

            if (gameState.currentPlayer === 'abukhaseeb') {
                setTimeout(enhancedAiTurn, 1000);
            }
        }

        // Enhanced AI with better strategy
        function enhancedAiTurn() {
            if (gameState.gameOver) return;

            const aiUnits = getAllUnitsForTeam('abukhaseeb');
            const playerUnits = getAllUnitsForTeam('hayaniya');

            // Prioritize targets
            const priorityTargets = playerUnits.sort((a, b) => {
                // Target low health units first, then high value units
                const aScore = (100 - a.health) + (a.type === 'archer' ? 20 : 0);
                const bScore = (100 - b.health) + (b.type === 'archer' ? 20 : 0);
                return bScore - aScore;
            });

            for (let unit of aiUnits) {
                if (gameState.gameOver) break;

                // Try to attack priority targets
                if (!unit.hasAttacked) {
                    for (let target of priorityTargets) {
                        const distance = Math.abs(target.row - unit.row) + Math.abs(target.col - unit.col);
                        if (distance <= unit.range) {
                            attemptEnhancedAttack(unit, target, target.row, target.col);
                            break;
                        }
                    }
                }

                // Move towards nearest enemy if haven't moved
                if (!unit.hasMoved && priorityTargets.length > 0) {
                    const nearestTarget = priorityTargets[0];
                    const moveOptions = findMoveOptions(unit);

                    if (moveOptions.length > 0) {
                        // Find move that gets closest to target
                        const bestMove = moveOptions.reduce((best, move) => {
                            const distToTarget = Math.abs(nearestTarget.row - move.row) + Math.abs(nearestTarget.col - move.col);
                            const bestDistToTarget = Math.abs(nearestTarget.row - best.row) + Math.abs(nearestTarget.col - best.col);
                            return distToTarget < bestDistToTarget ? move : best;
                        });

                        attemptEnhancedMove(unit, bestMove.row, bestMove.col);
                    }
                }
            }

            renderBoard();
            updateUI();
            setTimeout(endTurn, 800);
        }

        // Enhanced victory with statistics
        function showVictory(winner, title, message) {
            gameState.gameOver = true;
            const battleDuration = Math.round((Date.now() - gameState.startTime) / 60000);

            document.getElementById('victoryTitle').textContent = title;
            document.getElementById('victoryMessage').textContent = message;
            document.getElementById('finalTurns').textContent = gameState.turnCount;
            document.getElementById('finalDefeated').textContent = gameState.totalDefeated;
            document.getElementById('battleDuration').textContent = battleDuration;

            const winnerScore = winner === 'hayaniya' ?
                document.getElementById('hayaniyaScore').textContent :
                document.getElementById('abuKhaseebScore').textContent;
            document.getElementById('finalScore').textContent = winnerScore;

            document.getElementById('victoryModal').style.display = 'flex';

            sounds.victory();
            addToLog(`انتهت المعركة بعد ${gameState.turnCount} دور! ${teams[winner].name} انتصر!`);

            // Save battle to history
            gameState.battleHistory.push({
                winner: winner,
                turns: gameState.turnCount,
                duration: battleDuration,
                defeated: gameState.totalDefeated,
                date: new Date().toLocaleDateString('ar')
            });
        }

        // Enhanced reset game
        function resetGame() {
            gameState = {
                currentPlayer: 'hayaniya',
                selectedUnit: null,
                selectedCell: null,
                gameBoard: Array(6).fill().map(() => Array(8).fill(null)),
                gameOver: false,
                soundEnabled: gameState.soundEnabled,
                effectsEnabled: gameState.effectsEnabled,
                turnCount: 1,
                startTime: Date.now(),
                totalDefeated: 0,
                battleHistory: gameState.battleHistory
            };

            document.getElementById('gameLog').innerHTML = '';
            document.getElementById('hayaniyaScore').textContent = '0';
            document.getElementById('abuKhaseebScore').textContent = '0';

            setupInitialUnits();
            updateUI();
            createParticleEffects();
            addToLog('بدأت معركة جديدة محسنة! فريق الحيانية يبدأ أولاً.');
        }

        // Toggle effects
        function toggleEffects() {
            gameState.effectsEnabled = !gameState.effectsEnabled;
            const btn = document.getElementById('effectsBtn');
            btn.textContent = gameState.effectsEnabled ? 'التأثيرات: تشغيل' : 'التأثيرات: إيقاف';

            if (gameState.effectsEnabled) {
                createParticleEffects();
            } else {
                document.getElementById('specialEffects').innerHTML = '';
            }
        }

        // Enhanced instructions
        function showInstructions() {
            alert(`تعليمات اللعبة المحسنة:

🎮 الأساسيات:
1. اختر وحدة من فريقك بالنقر عليها
2. انقر على مربع فارغ للحركة أو على وحدة عدو للهجوم
3. كل وحدة يمكنها الحركة والهجوم مرة واحدة في الدور

⚔️ أنواع الوحدات والقدرات الخاصة:
🛡️ مشاة: دفاع قوي، جدار الدروع (يعزز دفاع الحلفاء المجاورين)
⚔️ مقاتلين: هجوم سريع، جنون القتال (ضرر إضافي عند انخفاض الصحة)
🏹 رماة: مدى بعيد، سهم خارق (يخترق الدروع)
🐎 فرسان: حركة سريعة، هجمة اندفاع (ضرر إضافي بعد الحركة)

🌟 نظام الخبرة:
- الوحدات تكتسب خبرة من القتال
- عند 5 نقاط خبرة، ترتقي الوحدة وتصبح أقوى
- الوحدات المتقدمة لها إحصائيات محسنة

💪 مزايا فريق أبو الخصيب:
- قوة هجوم أعلى بـ 25%
- دفاع محصن بـ 30%
- ذكاء اصطناعي متقدم
- وحدات متخصصة

🏆 شروط الفوز:
اهزم جميع وحدات العدو للفوز!

حظاً موفقاً في المعركة المحسنة!`);
        }

        // Utility functions (same as before but with enhancements)
        function selectUnit(unit, row, col) {
            deselectUnit();
            gameState.selectedUnit = unit;
            gameState.selectedCell = { row, col };

            const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            cell.classList.add('selected');

            showPossibleActions(unit, row, col);
            updateSelectedUnitInfo(unit);
        }

        function deselectUnit() {
            document.querySelectorAll('.cell').forEach(cell => {
                cell.classList.remove('selected', 'possible-move', 'enemy-target');
            });

            gameState.selectedUnit = null;
            gameState.selectedCell = null;
            updateSelectedUnitInfo(null);
        }

        function showPossibleActions(unit, row, col) {
            if (!unit.hasMoved) {
                for (let r = Math.max(0, row - unit.movement); r <= Math.min(5, row + unit.movement); r++) {
                    for (let c = Math.max(0, col - unit.movement); c <= Math.min(7, col + unit.movement); c++) {
                        if (r === row && c === col) continue;
                        if (Math.abs(r - row) + Math.abs(c - col) <= unit.movement) {
                            if (!gameState.gameBoard[r][c]) {
                                const cell = document.querySelector(`[data-row="${r}"][data-col="${c}"]`);
                                cell.classList.add('possible-move');
                            }
                        }
                    }
                }
            }

            if (!unit.hasAttacked) {
                for (let r = Math.max(0, row - unit.range); r <= Math.min(5, row + unit.range); r++) {
                    for (let c = Math.max(0, col - unit.range); c <= Math.min(7, col + unit.range); c++) {
                        if (r === row && c === col) continue;
                        if (Math.abs(r - row) + Math.abs(c - col) <= unit.range) {
                            const target = gameState.gameBoard[r][c];
                            if (target && target.team !== unit.team) {
                                const cell = document.querySelector(`[data-row="${r}"][data-col="${c}"]`);
                                cell.classList.add('enemy-target');
                            }
                        }
                    }
                }
            }
        }

        function getAllUnitsForTeam(team) {
            const units = [];
            for (let row = 0; row < 6; row++) {
                for (let col = 0; col < 8; col++) {
                    const unit = gameState.gameBoard[row][col];
                    if (unit && unit.team === team) {
                        units.push(unit);
                    }
                }
            }
            return units;
        }

        function findMoveOptions(unit) {
            const moves = [];
            for (let r = Math.max(0, unit.row - unit.movement); r <= Math.min(5, unit.row + unit.movement); r++) {
                for (let c = Math.max(0, unit.col - unit.movement); c <= Math.min(7, unit.col + unit.movement); c++) {
                    if (r === unit.row && c === unit.col) continue;
                    if (Math.abs(r - unit.row) + Math.abs(c - unit.col) <= unit.movement) {
                        if (!gameState.gameBoard[r][c]) {
                            moves.push({ row: r, col: c });
                        }
                    }
                }
            }
            return moves;
        }

        function updateScore(team, points) {
            if (team === 'hayaniya') {
                const current = parseInt(document.getElementById('hayaniyaScore').textContent);
                document.getElementById('hayaniyaScore').textContent = current + points;
            } else {
                const current = parseInt(document.getElementById('abuKhaseebScore').textContent);
                document.getElementById('abuKhaseebScore').textContent = current + points;
            }
        }

        function checkVictory() {
            const hayaniyaUnits = getAllUnitsForTeam('hayaniya');
            const abuKhaseebUnits = getAllUnitsForTeam('abukhaseeb');

            if (hayaniyaUnits.length === 0) {
                showVictory('abukhaseeb', 'فريق أبو الخصيب انتصر!', 'تم القضاء على جميع وحدات فريق الحيانية. أبو الخصيب يحكم الميدان بقوته المتفوقة!');
                return true;
            } else if (abuKhaseebUnits.length === 0) {
                showVictory('hayaniya', 'فريق الحيانية انتصر!', 'انتصار مذهل! تم القضاء على جميع وحدات فريق أبو الخصيب رغم قوتهم المتفوقة!');
                return true;
            }

            return false;
        }

        function checkTurnEnd() {
            const currentTeamUnits = getAllUnitsForTeam(gameState.currentPlayer);
            const allMoved = currentTeamUnits.every(unit => unit.hasMoved);
            const allAttacked = currentTeamUnits.every(unit => unit.hasAttacked);

            if (allMoved && allAttacked) {
                endTurn();
            }
        }

        function updateSelectedUnitInfo(unit) {
            const info = document.getElementById('selectedUnitInfo');
            if (unit) {
                info.textContent = `${unit.type} - صحة: ${unit.health}/${unit.maxHealth} - هجوم: ${unit.attack} - دفاع: ${unit.defense} - خبرة: ${unit.experience}`;
            } else {
                info.textContent = 'لا توجد وحدة محددة';
            }
        }

        function addToLog(message) {
            const log = document.getElementById('gameLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = message;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function closeVictoryModal() {
            document.getElementById('victoryModal').style.display = 'none';
        }

        function toggleSound() {
            gameState.soundEnabled = !gameState.soundEnabled;
            const btn = document.getElementById('soundBtn');
            btn.textContent = gameState.soundEnabled ? 'الصوت: تشغيل' : 'الصوت: إيقاف';
        }

        // Initialize enhanced game when page loads
        window.onload = function() {
            initGame();
        };
    </script>
</body>
</html>
