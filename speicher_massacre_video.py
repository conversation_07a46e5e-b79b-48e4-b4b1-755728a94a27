#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Video Generator for Speicher Massacre Memorial
Converts PowerPoint presentation to high-quality MP4 video
For Iraqi/Arab Community Memorial Commemorations
"""

import os
import sys
from moviepy.editor import ImageClip, concatenate_videoclips, CompositeVideoClip
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
import numpy as np

class SpecherMassacreVideoGenerator:
    def __init__(self):
        self.video_width = 1920
        self.video_height = 1080
        self.fps = 30
        self.slide_duration = 5  # 5 seconds per slide as requested

        # Memorial/mourning colors
        self.colors = {
            'black': (0, 0, 0),
            'dark_navy': (26, 26, 46),
            'dark_green': (15, 52, 96),
            'white': (255, 255, 255),
            'gold': (212, 175, 55),
            'blood_red': (139, 0, 0)
        }

        # Slide content data matching the PowerPoint presentation
        self.slides_data = [
            {
                'title': 'ذكرى مجزرة سبايكر',
                'subtitle': 'تخليداً لذكرى الشهداء الأبرار',
                'eng_subtitle': 'Camp Speicher Massacre Memorial\nIn Memory of the Martyred Heroes',
                'date': '١٢ حزيران ٢٠١٤ - June 12, 2014',
                'bg_color': 'black'
            },
            {
                'title': 'الخلفية التاريخية لمجزرة سبايكر',
                'content': [
                    '• قاعدة سبايكر العسكرية قرب تكريت في محافظة صلاح الدين',
                    '• كانت تضم آلاف الجنود العراقيين من مختلف المحافظات',
                    '• سقطت القاعدة بيد تنظيم داعش الإرهابي في يونيو ٢٠١٤',
                    '• تم أسر المئات من الجنود العراقيين العزل',
                    '• ارتكب التنظيم الإرهابي جريمة إبادة جماعية بحق الأسرى',
                    '• استهدف التنظيم بشكل خاص الجنود الشيعة والأقليات'
                ],
                'eng_content': 'Historical Background of the Speicher Massacre:\n• Camp Speicher military base near Tikrit in Salah al-Din province\n• Housed thousands of Iraqi soldiers from various provinces\n• The base fell to ISIS terrorist organization in June 2014\n• Hundreds of unarmed Iraqi soldiers were captured\n• The terrorist organization committed genocide against the prisoners\n• The organization specifically targeted Shia soldiers and minorities',
                'bg_color': 'dark_navy'
            },
            {
                'title': 'التسلسل الزمني للأحداث المأساوية',
                'content': [
                    '١٠ يونيو ٢٠١٤: سقوط الموصل بيد داعش',
                    '١١ يونيو ٢٠١٤: تقدم داعش نحو تكريت وسبايكر',
                    '١٢ يونيو ٢٠١٤: سقوط قاعدة سبايكر وأسر الجنود',
                    '١٢-١٥ يونيو ٢٠١٤: عمليات الإعدام الجماعي',
                    '١٦ يونيو ٢٠١٤: نشر داعش صور الجريمة',
                    '٢٠١٥-٢٠١٦: تحرير المنطقة واكتشاف المقابر الجماعية',
                    '٢٠١٨-٢٠٢١: محاكمات المتورطين في الجريمة'
                ],
                'eng_content': 'Timeline of Tragic Events:\nJune 10, 2014: Fall of Mosul to ISIS\nJune 11, 2014: ISIS advance toward Tikrit and Speicher\nJune 12, 2014: Fall of Camp Speicher and capture of soldiers\nJune 12-15, 2014: Mass execution operations\nJune 16, 2014: ISIS publishes images of the crime\n2015-2016: Liberation of the area and discovery of mass graves\n2018-2021: Trials of those involved in the crime',
                'bg_color': 'dark_green'
            },
            {
                'title': 'تكريم ذكرى الشهداء الأبرار',
                'content': [
                    '• أكثر من ١٧٠٠ شهيد من أبناء العراق الأبرار',
                    '• جنود شباب في مقتبل العمر ضحوا بأرواحهم للوطن',
                    '• من جميع محافظات العراق ومن مختلف الطوائف والقوميات',
                    '• استشهدوا دفاعاً عن كرامة العراق وأرضه المقدسة',
                    '• أرواحهم الطاهرة ترفرف في جنان الخلد',
                    '• ذكراهم العطرة محفورة في قلوب العراقيين',
                    '• دماؤهم الزكية لن تذهب هدراً'
                ],
                'prayer': 'رحمهم الله وأسكنهم فسيح جناته',
                'eng_content': 'Honoring the Memory of the Righteous Martyrs:\n• More than 1700 martyrs from the noble sons of Iraq\n• Young soldiers in the prime of life who sacrificed their souls for the homeland\n• From all provinces of Iraq and from different sects and ethnicities\n• They were martyred defending the dignity of Iraq and its sacred land\n• Their pure souls soar in the gardens of eternity\n• Their fragrant memory is engraved in the hearts of Iraqis\n• Their pure blood will not be shed in vain\n\nMay Allah have mercy on them and grant them spacious gardens',
                'bg_color': 'black'
            },
            {
                'title': 'الأثر والدلالات التاريخية للمجزرة',
                'content': [
                    '• نقطة تحول في تاريخ العراق المعاصر',
                    '• كشفت وحشية التنظيمات الإرهابية',
                    '• عززت الوحدة الوطنية العراقية ضد الإرهاب',
                    '• أدت إلى تشكيل الحشد الشعبي للدفاع عن الوطن',
                    '• حركت الضمير العالمي ضد جرائم داعش',
                    '• أصبحت رمزاً للمقاومة ضد الظلم والطغيان',
                    '• ألهمت الشعب العراقي للوقوف ضد الإرهاب',
                    '• تذكير دائم بأهمية الوحدة والتماسك الوطني'
                ],
                'eng_content': 'Historical Impact and Significance of the Massacre:\n• Turning point in contemporary Iraqi history\n• Revealed the brutality of terrorist organizations\n• Strengthened Iraqi national unity against terrorism\n• Led to the formation of Popular Mobilization Forces to defend the homeland\n• Moved the global conscience against ISIS crimes\n• Became a symbol of resistance against injustice and tyranny\n• Inspired the Iraqi people to stand against terrorism\n• Permanent reminder of the importance of national unity and cohesion',
                'bg_color': 'dark_navy'
            },
            {
                'title': 'لن ننسى شهداء سبايكر الأبرار',
                'memorial_text': 'خلدت أسماؤهم في سجل الخالدين\nوبقيت ذكراهم منارة للأجيال\nرحمهم الله وأسكنهم فسيح جناته',
                'eng_memorial': 'We Will Never Forget the Righteous Martyrs of Speicher\n\nTheir names are immortalized in the register of the eternal\nTheir memory remains a beacon for generations\nMay Allah have mercy on them and grant them spacious gardens',
                'bg_color': 'black'
            }
        ]

    def format_arabic_text(self, text):
        """Format Arabic text for proper RTL display"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text

    def get_font_path(self, font_name, size):
        """Get font path with fallback options"""
        font_paths = [
            f"fonts/{font_name}",
            f"fonts/Amiri-Regular.ttf",
            f"fonts/NotoSansArabic-Regular.ttf",
            "arial.ttf",
            "Arial.ttf"
        ]
        
        for font_path in font_paths:
            try:
                return ImageFont.truetype(font_path, size)
            except:
                continue
        
        # Fallback to default font
        return ImageFont.load_default()

    def create_slide_image(self, slide_data, slide_num):
        """Create an image for a single slide"""
        # Create image with background color
        bg_color = self.colors[slide_data['bg_color']]
        img = Image.new('RGB', (self.video_width, self.video_height), bg_color)
        draw = ImageDraw.Draw(img)

        # Load fonts
        title_font = self.get_font_path("Amiri-Regular.ttf", 80)
        content_font = self.get_font_path("Amiri-Regular.ttf", 50)
        subtitle_font = self.get_font_path("Amiri-Regular.ttf", 45)
        eng_font = self.get_font_path("Arial.ttf", 35)

        y_position = 100

        # Title
        if 'title' in slide_data:
            title_text = self.format_arabic_text(slide_data['title'])
            
            # Get text size for centering
            bbox = draw.textbbox((0, 0), title_text, font=title_font)
            text_width = bbox[2] - bbox[0]
            x_position = (self.video_width - text_width) // 2
            
            draw.text((x_position, y_position), title_text, 
                     font=title_font, fill=self.colors['gold'])
            y_position += 150

        # Handle different slide types
        if slide_num == 1:  # Title slide
            if 'subtitle' in slide_data:
                subtitle_text = self.format_arabic_text(slide_data['subtitle'])
                bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), subtitle_text, 
                         font=subtitle_font, fill=self.colors['white'])
                y_position += 100

            if 'eng_subtitle' in slide_data:
                eng_lines = slide_data['eng_subtitle'].split('\n')
                for line in eng_lines:
                    bbox = draw.textbbox((0, 0), line, font=eng_font)
                    text_width = bbox[2] - bbox[0]
                    x_position = (self.video_width - text_width) // 2
                    draw.text((x_position, y_position), line, 
                             font=eng_font, fill=self.colors['white'])
                    y_position += 50

            if 'date' in slide_data:
                date_text = self.format_arabic_text(slide_data['date'])
                bbox = draw.textbbox((0, 0), date_text, font=subtitle_font)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position + 100), date_text, 
                         font=subtitle_font, fill=self.colors['blood_red'])

        elif slide_num == 6:  # Closing slide
            if 'memorial_text' in slide_data:
                memorial_lines = slide_data['memorial_text'].split('\n')
                for line in memorial_lines:
                    formatted_line = self.format_arabic_text(line)
                    bbox = draw.textbbox((0, 0), formatted_line, font=content_font)
                    text_width = bbox[2] - bbox[0]
                    x_position = (self.video_width - text_width) // 2
                    draw.text((x_position, y_position), formatted_line, 
                             font=content_font, fill=self.colors['white'])
                    y_position += 80

            if 'eng_memorial' in slide_data:
                y_position += 50
                eng_lines = slide_data['eng_memorial'].split('\n')
                for line in eng_lines:
                    if line.strip():
                        bbox = draw.textbbox((0, 0), line, font=eng_font)
                        text_width = bbox[2] - bbox[0]
                        x_position = (self.video_width - text_width) // 2
                        draw.text((x_position, y_position), line, 
                                 font=eng_font, fill=self.colors['white'])
                    y_position += 45

        else:  # Content slides
            if 'content' in slide_data:
                for item in slide_data['content']:
                    formatted_item = self.format_arabic_text(item)
                    # Right-align Arabic content
                    bbox = draw.textbbox((0, 0), formatted_item, font=content_font)
                    text_width = bbox[2] - bbox[0]
                    x_position = self.video_width - text_width - 100
                    draw.text((x_position, y_position), formatted_item, 
                             font=content_font, fill=self.colors['white'])
                    y_position += 60

            if 'prayer' in slide_data:
                y_position += 50
                prayer_text = self.format_arabic_text(slide_data['prayer'])
                bbox = draw.textbbox((0, 0), prayer_text, font=subtitle_font)
                text_width = bbox[2] - bbox[0]
                x_position = (self.video_width - text_width) // 2
                draw.text((x_position, y_position), prayer_text, 
                         font=subtitle_font, fill=self.colors['gold'])

        return img

    def create_video(self):
        """Create the complete video from slides"""
        print("🎬 Creating Speicher Massacre Memorial Video...")

        # Create slide images
        slide_images = []
        for i, slide_data in enumerate(self.slides_data, 1):
            print(f"📄 Creating slide {i}...")
            img = self.create_slide_image(slide_data, i)

            # Save temporary image
            temp_filename = f"temp_speicher_slide_{i}.png"
            img.save(temp_filename)
            slide_images.append(temp_filename)

        # Create video clips from images
        clips = []
        for i, img_path in enumerate(slide_images):
            print(f"🎞️ Processing slide {i+1} for video...")
            clip = ImageClip(img_path, duration=self.slide_duration)
            clips.append(clip)

        # Add smooth transitions (crossfade)
        print("🎨 Adding smooth transitions...")
        final_clips = []
        for i, clip in enumerate(clips):
            if i == 0:
                # First clip - no transition
                final_clips.append(clip)
            else:
                # Add crossfade transition
                transition_duration = 1.0  # 1 second crossfade

                # Adjust previous clip to overlap
                if len(final_clips) > 0:
                    prev_clip = final_clips[-1]
                    # Reduce previous clip duration by transition time
                    prev_clip = prev_clip.set_duration(prev_clip.duration - transition_duration/2)
                    final_clips[-1] = prev_clip

                # Add current clip with fade in
                current_clip = clip.fadein(transition_duration/2)
                final_clips.append(current_clip)

        # Create final video
        print("🎥 Creating final video...")
        final_video = concatenate_videoclips(final_clips, method="compose")

        # Set video properties
        final_video = final_video.set_fps(self.fps)

        # Export video
        output_filename = "Speicher_Massacre_Memorial_Video.mp4"
        print(f"💾 Exporting video as {output_filename}...")

        final_video.write_videofile(
            output_filename,
            fps=self.fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            ffmpeg_params=['-crf', '18']  # High quality
        )

        # Clean up temporary files
        print("🧹 Cleaning up temporary files...")
        for img_path in slide_images:
            try:
                os.remove(img_path)
            except:
                pass

        print(f"✅ Video created successfully: {output_filename}")
        print(f"📊 Video specifications:")
        print(f"   • Resolution: {self.video_width}x{self.video_height}")
        print(f"   • Frame Rate: {self.fps} FPS")
        print(f"   • Duration: ~{len(self.slides_data) * self.slide_duration} seconds ({len(self.slides_data) * self.slide_duration / 60:.1f} minutes)")
        print(f"   • Format: MP4 (H.264)")
        print(f"   • Slides: {len(self.slides_data)}")
        print(f"   • Slide Duration: {self.slide_duration} seconds each")

        return output_filename

def main():
    """Main function to create the video"""
    try:
        generator = SpecherMassacreVideoGenerator()
        video_file = generator.create_video()

        print(f"\n🎉 Successfully created video: {video_file}")
        print("\n🕌 This video is ready for:")
        print("• Memorial services and commemorative events")
        print("• Social media sharing (WhatsApp, Telegram, Instagram, Facebook)")
        print("• Educational purposes in schools and institutions")
        print("• Digital archives for community use")
        print("• Religious gatherings and remembrance ceremonies")

        print(f"\n📍 File location: {os.path.abspath(video_file)}")
        print("\n🌟 Video features:")
        print("• High-quality 1080p Full HD resolution")
        print("• Proper Arabic RTL text formatting")
        print("• Memorial mourning color scheme")
        print("• Smooth slide transitions (crossfade effects)")
        print("• 5-second slides optimized for memorial viewing")
        print("• Culturally appropriate for Iraqi/Arab memorial audiences")
        print("• Professional H.264 encoding for broad compatibility")
        print("• Optimized for social media and presentation systems")

    except Exception as e:
        print(f"❌ Error creating video: {str(e)}")
        print("Please ensure all required packages are installed:")
        print("pip install moviepy pillow arabic-reshaper python-bidi")

if __name__ == "__main__":
    main()
