<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>إعلان تعزية - الإمام محمد الجواد (عليه السلام)</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- html2canvas Library for Image Generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#1a1a2e">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Amiri', 'Noto Sans Arabic', serif;
            background: linear-gradient(180deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #f5f5f5;
            direction: rtl;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        .mobile-container {
            max-width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .header-section {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            text-align: center;
            border-bottom: 3px solid #d4af37;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
        }

        .brand-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .brand-logo {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: 2px solid #d4af37;
        }

        .brand-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: #d4af37;
        }

        .announcement-badge {
            background: linear-gradient(90deg, #d4af37, #b8941f);
            color: #000;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }

        .main-content {
            flex: 1;
            padding: 2rem 1rem;
            text-align: center;
            position: relative;
        }

        .geometric-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            background-image:
                radial-gradient(circle at 25% 25%, #d4af37 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, #90ee90 1px, transparent 1px);
            background-size: 40px 40px;
            background-position: 0 0, 20px 20px;
            animation: float 15s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .verse-container {
            background: rgba(144, 238, 144, 0.15);
            border: 2px solid rgba(144, 238, 144, 0.4);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .verse-text {
            font-size: 1.2rem;
            color: #90ee90;
            font-weight: 600;
            line-height: 1.8;
        }

        .title-section {
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .main-title {
            font-size: 1.8rem;
            color: #f5f5f5;
            margin-bottom: 1rem;
            font-weight: 400;
        }

        .imam-name {
            font-size: 2.5rem;
            color: #d4af37;
            font-weight: 700;
            margin: 1rem 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            line-height: 1.2;
        }

        .honorific {
            display: block;
            font-size: 1.3rem;
            color: #90ee90;
            margin-top: 0.8rem;
            font-weight: 400;
        }

        .message-container {
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .condolence-message {
            font-size: 1.1rem;
            line-height: 1.9;
            text-align: justify;
            margin-bottom: 1rem;
        }

        .date-container {
            background: linear-gradient(90deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
            border: 2px solid rgba(212, 175, 55, 0.5);
            border-radius: 25px;
            padding: 1rem;
            margin: 2rem 0;
            position: relative;
            z-index: 2;
        }

        .date-text {
            font-size: 1.3rem;
            color: #d4af37;
            font-weight: 600;
        }

        .prayer-section {
            background: rgba(144, 238, 144, 0.1);
            border: 2px solid rgba(144, 238, 144, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .prayer-text {
            font-size: 1rem;
            color: #90ee90;
            font-weight: 600;
            line-height: 1.8;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
            position: relative;
            z-index: 2;
        }

        .action-btn {
            background: linear-gradient(90deg, #d4af37, #b8941f);
            color: #000;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
        }

        .action-btn:active {
            transform: translateY(2px);
            box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
        }

        .secondary-btn {
            background: rgba(144, 238, 144, 0.2);
            color: #90ee90;
            border: 2px solid rgba(144, 238, 144, 0.5);
        }

        .footer-section {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            text-align: center;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
            font-size: 0.9rem;
            color: #999;
        }

        .success-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(144, 238, 144, 0.9);
            color: #000;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .success-message.show {
            opacity: 1;
        }

        /* Touch-friendly sizing */
        @media (max-width: 375px) {
            .main-content {
                padding: 1.5rem 0.8rem;
            }

            .imam-name {
                font-size: 2.2rem;
            }

            .condolence-message {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Header -->
        <header class="header-section">
            <div class="brand-container">
                <img src="app_icon.png" alt="AliToucan" class="brand-logo">
                <span class="brand-name">AliToucan</span>
            </div>
            <div class="announcement-badge">إعلان تعزية للعالم الإسلامي</div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="geometric-bg"></div>

            <!-- Quranic Verse -->
            <div class="verse-container">
                <div class="verse-text">
                    "وَبَشِّرِ الصَّابِرِينَ * الَّذِينَ إِذَا أَصَابَتْهُم مُّصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ"
                </div>
            </div>

            <!-- Title Section -->
            <div class="title-section">
                <h1 class="main-title">في ذكرى استشهاد</h1>
                <h2 class="imam-name">
                    الإمام محمد الجواد
                    <span class="honorific">(عليه السلام)</span>
                </h2>
            </div>

            <!-- Message -->
            <div class="message-container">
                <p class="condolence-message">
                    نتقدم إلى العالم الإسلامي وجميع المؤمنين بأحر التعازي بمناسبة ذكرى استشهاد الإمام التاسع من أئمة أهل البيت الأطهار،
                    الذي استشهد مسموماً وهو في ريعان شبابه، تاركاً للأمة إرثاً عظيماً من العلم والحكمة والتقوى.
                </p>
            </div>

            <!-- Date -->
            <div class="date-container">
                <div class="date-text">٢٩ ذو القعدة الحرام - ذكرى الاستشهاد</div>
            </div>

            <!-- Prayer -->
            <div class="prayer-section">
                <div class="prayer-text">
                    اللهم صل على محمد وآل محمد، وعجل فرج وليك الحجة بن الحسن،
                    واجعلنا من أنصاره وأعوانه والذابين عنه
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="action-btn" onclick="shareMessage()">
                    📤 مشاركة الرسالة
                </button>
                <button class="action-btn secondary-btn" onclick="copyMessage()">
                    📋 نسخ النص
                </button>
                <button class="action-btn" onclick="downloadMobileImage('mobile', 1080, 1920)">
                    📱 تحميل كصورة
                </button>
                <button class="action-btn secondary-btn" onclick="downloadMobileImage('square', 1080, 1080)">
                    🖼️ مربع للمشاركة
                </button>
            </div>

            <!-- Download Progress -->
            <div class="download-progress" id="downloadProgress" style="display: none; margin-top: 1rem; padding: 1rem; background: rgba(144, 238, 144, 0.1); border-radius: 10px; border: 2px solid rgba(144, 238, 144, 0.3);">
                <div class="progress-text" id="progressText" style="color: #90ee90; font-weight: 600; text-align: center;">جاري إنشاء الصورة...</div>
                <div class="progress-bar" style="width: 100%; height: 6px; background: rgba(144, 238, 144, 0.2); border-radius: 3px; margin-top: 0.5rem; overflow: hidden;">
                    <div class="progress-fill" id="progressFill" style="height: 100%; background: linear-gradient(90deg, #90ee90, #d4af37); width: 0%; transition: width 0.3s ease; border-radius: 3px;"></div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer-section">
            © ٢٠٢٤ AliToucan - في ذكرى الإمام الجواد (عليه السلام)
        </footer>
    </div>

    <!-- Success Message -->
    <div class="success-message" id="successMessage">
        تم بنجاح! ✅
    </div>

    <script>
        const condolenceText = `
"وَبَشِّرِ الصَّابِرِينَ * الَّذِينَ إِذَا أَصَابَتْهُم مُّصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ"

في ذكرى استشهاد الإمام محمد الجواد (عليه السلام)

نتقدم إلى العالم الإسلامي وجميع المؤمنين بأحر التعازي بمناسبة ذكرى استشهاد الإمام التاسع من أئمة أهل البيت الأطهار، الذي استشهد مسموماً وهو في ريعان شبابه، تاركاً للأمة إرثاً عظيماً من العلم والحكمة والتقوى.

٢٩ ذو القعدة الحرام - ذكرى الاستشهاد

اللهم صل على محمد وآل محمد، وعجل فرج وليك الحجة بن الحسن، واجعلنا من أنصاره وأعوانه والذابين عنه

© AliToucan
        `.trim();

        function showSuccess(message = 'تم بنجاح! ✅') {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.classList.add('show');
            setTimeout(() => {
                successEl.classList.remove('show');
            }, 2000);
        }

        function copyMessage() {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(condolenceText).then(() => {
                    showSuccess('تم نسخ الرسالة! 📋');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = condolenceText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showSuccess('تم نسخ الرسالة! 📋');
            }
        }

        function shareMessage() {
            if (navigator.share) {
                navigator.share({
                    title: 'تعزية - الإمام محمد الجواد (عليه السلام)',
                    text: condolenceText,
                    url: window.location.href
                }).then(() => {
                    showSuccess('تم المشاركة! 📤');
                }).catch(() => {
                    copyMessage();
                });
            } else {
                copyMessage();
            }
        }

        // Image download functionality
        let isGenerating = false;

        function showDownloadProgress() {
            document.getElementById('downloadProgress').style.display = 'block';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = 'جاري إنشاء الصورة...';
        }

        function updateDownloadProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            if (text) {
                document.getElementById('progressText').textContent = text;
            }
        }

        function hideDownloadProgress() {
            setTimeout(() => {
                document.getElementById('downloadProgress').style.display = 'none';
            }, 1000);
        }

        function toggleActionButtons(disabled) {
            const buttons = document.querySelectorAll('.action-btn');
            buttons.forEach(btn => {
                btn.disabled = disabled;
                btn.style.opacity = disabled ? '0.6' : '1';
            });
        }

        function generateMobileFilename(format) {
            const date = new Date();
            const timestamp = date.getFullYear() + '-' +
                            String(date.getMonth() + 1).padStart(2, '0') + '-' +
                            String(date.getDate()).padStart(2, '0');

            const formatNames = {
                'mobile': 'موبايل_عمودي',
                'square': 'مربع_للمشاركة'
            };

            return `إعلان_تعزية_الإمام_الجواد_${formatNames[format]}_${timestamp}.png`;
        }

        async function downloadMobileImage(format, width, height) {
            if (isGenerating) return;

            isGenerating = true;
            toggleActionButtons(true);
            showDownloadProgress();

            try {
                updateDownloadProgress(10, 'تحضير الإعلان...');

                // Get the main content element
                const element = document.querySelector('.mobile-container');

                // Temporarily hide download progress for clean image
                const downloadProgress = document.getElementById('downloadProgress');
                downloadProgress.style.display = 'none';

                updateDownloadProgress(30, 'تحسين جودة الخطوط...');

                // Wait for fonts to load
                await document.fonts.ready;

                updateDownloadProgress(50, 'إنشاء الصورة...');

                // Configure html2canvas options for mobile
                const options = {
                    allowTaint: true,
                    useCORS: true,
                    scale: 2,
                    width: element.offsetWidth,
                    height: element.offsetHeight,
                    backgroundColor: null,
                    logging: false,
                    imageTimeout: 15000,
                    removeContainer: true,
                    foreignObjectRendering: true,
                    onclone: function(clonedDoc) {
                        const clonedElement = clonedDoc.querySelector('.mobile-container');
                        if (clonedElement) {
                            clonedElement.style.fontFamily = "'Amiri', 'Noto Sans Arabic', serif";
                            clonedElement.style.direction = 'rtl';
                        }
                    }
                };

                updateDownloadProgress(70, 'معالجة النص العربي...');

                // Generate canvas
                const canvas = await html2canvas(element, options);

                updateDownloadProgress(85, 'تحسين الصورة...');

                // Create final canvas with desired dimensions
                const finalCanvas = document.createElement('canvas');
                finalCanvas.width = width;
                finalCanvas.height = height;
                const ctx = finalCanvas.getContext('2d');

                // Fill background with gradient
                const gradient = ctx.createLinearGradient(0, 0, width, height);
                gradient.addColorStop(0, '#0a0a0a');
                gradient.addColorStop(0.5, '#1a1a2e');
                gradient.addColorStop(1, '#16213e');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, width, height);

                // Calculate scaling and positioning
                const scale = Math.min(width / canvas.width, height / canvas.height) * 0.95;
                const scaledWidth = canvas.width * scale;
                const scaledHeight = canvas.height * scale;
                const x = (width - scaledWidth) / 2;
                const y = (height - scaledHeight) / 2;

                // Draw the content
                ctx.drawImage(canvas, x, y, scaledWidth, scaledHeight);

                updateDownloadProgress(95, 'حفظ الملف...');

                // Convert to blob and download
                finalCanvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = generateMobileFilename(format);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    updateDownloadProgress(100, 'تم التحميل بنجاح! ✅');

                    setTimeout(() => {
                        hideDownloadProgress();
                        toggleActionButtons(false);
                        isGenerating = false;
                        showSuccess('تم تحميل الصورة! 📱');
                    }, 1500);

                }, 'image/png', 0.95);

            } catch (error) {
                console.error('Error generating image:', error);
                updateDownloadProgress(0, 'حدث خطأ في إنشاء الصورة ❌');

                setTimeout(() => {
                    hideDownloadProgress();
                    toggleActionButtons(false);
                    isGenerating = false;
                    showSuccess('حدث خطأ في التحميل ❌');
                }, 2000);
            }
        }

        // Add touch feedback
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });

            btn.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
