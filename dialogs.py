"""
Dialog windows for the Professional Image Editor
Contains various dialog boxes for user input and configuration
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Optional, Tuple, List, Any
from PIL import Image
from config import SOCIAL_MEDIA_PRESETS
from batch_processor import BatchProcessor
from utils import UIUtils

class ResizeDialog(tk.Toplevel):
    """Dialog for resizing images"""

    def __init__(self, parent, current_size: Tuple[int, int]):
        super().__init__(parent)
        self.title("Resize Image")
        self.transient(parent)
        self.grab_set()

        self.current_size = current_size
        self.result = None

        self.setup_ui()
        self.center_window()

    def setup_ui(self):
        """Setup resize dialog UI"""
        main_frame = tk.Frame(self, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # Current size info
        info_label = tk.Label(main_frame,
                             text=f"Current size: {self.current_size[0]} x {self.current_size[1]} pixels",
                             font=('Arial', 10))
        info_label.pack(pady=10)

        # Size input frame
        size_frame = tk.LabelFrame(main_frame, text="New Size", font=('Arial', 9, 'bold'))
        size_frame.pack(fill='x', pady=10)

        # Width
        width_frame = tk.Frame(size_frame)
        width_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(width_frame, text="Width:", width=8).pack(side='left')
        self.width_var = tk.StringVar(value=str(self.current_size[0]))
        self.width_entry = tk.Entry(width_frame, textvariable=self.width_var, width=10)
        self.width_entry.pack(side='left', padx=5)
        tk.Label(width_frame, text="pixels").pack(side='left')

        # Height
        height_frame = tk.Frame(size_frame)
        height_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(height_frame, text="Height:", width=8).pack(side='left')
        self.height_var = tk.StringVar(value=str(self.current_size[1]))
        self.height_entry = tk.Entry(height_frame, textvariable=self.height_var, width=10)
        self.height_entry.pack(side='left', padx=5)
        tk.Label(height_frame, text="pixels").pack(side='left')

        # Maintain aspect ratio
        self.maintain_aspect_var = tk.BooleanVar(value=True)
        aspect_check = tk.Checkbutton(size_frame, text="Maintain aspect ratio",
                                     variable=self.maintain_aspect_var,
                                     command=self.on_aspect_toggle)
        aspect_check.pack(padx=10, pady=5)

        # Bind events for aspect ratio calculation
        self.width_var.trace('w', self.on_width_change)
        self.height_var.trace('w', self.on_height_change)

        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=20)

        tk.Button(button_frame, text="OK", command=self.ok_clicked, width=10).pack(side='left', padx=5)
        tk.Button(button_frame, text="Cancel", command=self.cancel_clicked, width=10).pack(side='left', padx=5)

    def on_aspect_toggle(self):
        """Handle aspect ratio toggle"""
        if self.maintain_aspect_var.get():
            self.on_width_change()

    def on_width_change(self, *args):
        """Handle width change"""
        if self.maintain_aspect_var.get():
            try:
                new_width = int(self.width_var.get())
                aspect_ratio = self.current_size[0] / self.current_size[1]
                new_height = int(new_width / aspect_ratio)
                self.height_var.set(str(new_height))
            except ValueError:
                pass

    def on_height_change(self, *args):
        """Handle height change"""
        if self.maintain_aspect_var.get():
            try:
                new_height = int(self.height_var.get())
                aspect_ratio = self.current_size[0] / self.current_size[1]
                new_width = int(new_height * aspect_ratio)
                self.width_var.set(str(new_width))
            except ValueError:
                pass

    def ok_clicked(self):
        """Handle OK button"""
        try:
            width = int(self.width_var.get())
            height = int(self.height_var.get())

            if width <= 0 or height <= 0:
                messagebox.showerror("Error", "Width and height must be positive numbers")
                return

            self.result = ((width, height), self.maintain_aspect_var.get())
            self.destroy()
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for width and height")

    def cancel_clicked(self):
        """Handle Cancel button"""
        self.destroy()

    def center_window(self):
        """Center dialog on parent"""
        self.geometry("350x250")
        self.update_idletasks()

        x = (self.winfo_screenwidth() // 2) - (350 // 2)
        y = (self.winfo_screenheight() // 2) - (250 // 2)
        self.geometry(f"350x250+{x}+{y}")

class CropDialog(tk.Toplevel):
    """Dialog for cropping images"""

    def __init__(self, parent, image_size: Tuple[int, int]):
        super().__init__(parent)
        self.title("Crop Image")
        self.transient(parent)
        self.grab_set()

        self.image_size = image_size
        self.result = None

        self.setup_ui()
        self.center_window()

    def setup_ui(self):
        """Setup crop dialog UI"""
        main_frame = tk.Frame(self, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # Info
        info_label = tk.Label(main_frame,
                             text=f"Image size: {self.image_size[0]} x {self.image_size[1]} pixels",
                             font=('Arial', 10))
        info_label.pack(pady=10)

        # Crop area frame
        crop_frame = tk.LabelFrame(main_frame, text="Crop Area", font=('Arial', 9, 'bold'))
        crop_frame.pack(fill='x', pady=10)

        # X position
        x_frame = tk.Frame(crop_frame)
        x_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(x_frame, text="X:", width=8).pack(side='left')
        self.x_var = tk.StringVar(value="0")
        tk.Entry(x_frame, textvariable=self.x_var, width=10).pack(side='left', padx=5)

        # Y position
        y_frame = tk.Frame(crop_frame)
        y_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(y_frame, text="Y:", width=8).pack(side='left')
        self.y_var = tk.StringVar(value="0")
        tk.Entry(y_frame, textvariable=self.y_var, width=10).pack(side='left', padx=5)

        # Width
        width_frame = tk.Frame(crop_frame)
        width_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(width_frame, text="Width:", width=8).pack(side='left')
        self.width_var = tk.StringVar(value=str(self.image_size[0]))
        tk.Entry(width_frame, textvariable=self.width_var, width=10).pack(side='left', padx=5)

        # Height
        height_frame = tk.Frame(crop_frame)
        height_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(height_frame, text="Height:", width=8).pack(side='left')
        self.height_var = tk.StringVar(value=str(self.image_size[1]))
        tk.Entry(height_frame, textvariable=self.height_var, width=10).pack(side='left', padx=5)

        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=20)

        tk.Button(button_frame, text="OK", command=self.ok_clicked, width=10).pack(side='left', padx=5)
        tk.Button(button_frame, text="Cancel", command=self.cancel_clicked, width=10).pack(side='left', padx=5)

    def ok_clicked(self):
        """Handle OK button"""
        try:
            x = int(self.x_var.get())
            y = int(self.y_var.get())
            width = int(self.width_var.get())
            height = int(self.height_var.get())

            # Validate crop area
            if x < 0 or y < 0 or width <= 0 or height <= 0:
                messagebox.showerror("Error", "Invalid crop area")
                return

            if x + width > self.image_size[0] or y + height > self.image_size[1]:
                messagebox.showerror("Error", "Crop area exceeds image bounds")
                return

            self.result = (x, y, x + width, y + height)
            self.destroy()
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers")

    def cancel_clicked(self):
        """Handle Cancel button"""
        self.destroy()

    def center_window(self):
        """Center dialog on parent"""
        self.geometry("300x280")
        self.update_idletasks()

        x = (self.winfo_screenwidth() // 2) - (300 // 2)
        y = (self.winfo_screenheight() // 2) - (280 // 2)
        self.geometry(f"300x280+{x}+{y}")

class RotateDialog(tk.Toplevel):
    """Dialog for rotating images"""

    def __init__(self, parent):
        super().__init__(parent)
        self.title("Rotate Image")
        self.transient(parent)
        self.grab_set()

        self.result = None

        self.setup_ui()
        self.center_window()

    def setup_ui(self):
        """Setup rotate dialog UI"""
        main_frame = tk.Frame(self, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # Angle input
        angle_frame = tk.LabelFrame(main_frame, text="Rotation Angle", font=('Arial', 9, 'bold'))
        angle_frame.pack(fill='x', pady=10)

        # Angle entry
        entry_frame = tk.Frame(angle_frame)
        entry_frame.pack(fill='x', padx=10, pady=10)
        tk.Label(entry_frame, text="Angle:", width=8).pack(side='left')
        self.angle_var = tk.StringVar(value="0")
        tk.Entry(entry_frame, textvariable=self.angle_var, width=10).pack(side='left', padx=5)
        tk.Label(entry_frame, text="degrees").pack(side='left')

        # Quick rotation buttons
        quick_frame = tk.Frame(angle_frame)
        quick_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(quick_frame, text="Quick rotate:").pack(anchor='w')

        button_frame = tk.Frame(quick_frame)
        button_frame.pack(fill='x', pady=5)
        tk.Button(button_frame, text="90°", command=lambda: self.set_angle(90), width=8).pack(side='left', padx=2)
        tk.Button(button_frame, text="180°", command=lambda: self.set_angle(180), width=8).pack(side='left', padx=2)
        tk.Button(button_frame, text="270°", command=lambda: self.set_angle(270), width=8).pack(side='left', padx=2)
        tk.Button(button_frame, text="-90°", command=lambda: self.set_angle(-90), width=8).pack(side='left', padx=2)

        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=20)

        tk.Button(button_frame, text="OK", command=self.ok_clicked, width=10).pack(side='left', padx=5)
        tk.Button(button_frame, text="Cancel", command=self.cancel_clicked, width=10).pack(side='left', padx=5)

    def set_angle(self, angle: float):
        """Set rotation angle"""
        self.angle_var.set(str(angle))

    def ok_clicked(self):
        """Handle OK button"""
        try:
            angle = float(self.angle_var.get())
            self.result = angle
            self.destroy()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number for the angle")

    def cancel_clicked(self):
        """Handle Cancel button"""
        self.destroy()

    def center_window(self):
        """Center dialog on parent"""
        self.geometry("350x200")
        self.update_idletasks()

        x = (self.winfo_screenwidth() // 2) - (350 // 2)
        y = (self.winfo_screenheight() // 2) - (200 // 2)
        self.geometry(f"350x200+{x}+{y}")

class TextDialog(tk.Toplevel):
    """Dialog for adding text"""

    def __init__(self, parent):
        super().__init__(parent)
        self.title("Add Text")
        self.transient(parent)
        self.grab_set()

        self.result = None

        self.setup_ui()
        self.center_window()

    def setup_ui(self):
        """Setup text dialog UI"""
        main_frame = tk.Frame(self, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # Text input
        text_frame = tk.LabelFrame(main_frame, text="Text", font=('Arial', 9, 'bold'))
        text_frame.pack(fill='both', expand=True, pady=10)

        self.text_widget = tk.Text(text_frame, height=4, width=40)
        self.text_widget.pack(fill='both', expand=True, padx=10, pady=10)

        # Options frame
        options_frame = tk.LabelFrame(main_frame, text="Options", font=('Arial', 9, 'bold'))
        options_frame.pack(fill='x', pady=10)

        # Font size
        size_frame = tk.Frame(options_frame)
        size_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(size_frame, text="Font Size:", width=10).pack(side='left')
        self.size_var = tk.StringVar(value="20")
        tk.Entry(size_frame, textvariable=self.size_var, width=10).pack(side='left', padx=5)

        # Arabic text checkbox
        self.arabic_var = tk.BooleanVar()
        arabic_check = tk.Checkbutton(options_frame, text="Arabic text (RTL)",
                                     variable=self.arabic_var)
        arabic_check.pack(padx=10, pady=5, anchor='w')

        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=20)

        tk.Button(button_frame, text="OK", command=self.ok_clicked, width=10).pack(side='left', padx=5)
        tk.Button(button_frame, text="Cancel", command=self.cancel_clicked, width=10).pack(side='left', padx=5)

    def ok_clicked(self):
        """Handle OK button"""
        text = self.text_widget.get("1.0", tk.END).strip()
        if not text:
            messagebox.showerror("Error", "Please enter some text")
            return

        try:
            font_size = int(self.size_var.get())
            if font_size <= 0:
                messagebox.showerror("Error", "Font size must be positive")
                return
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid font size")
            return

        self.result = (text, font_size, self.arabic_var.get())
        self.destroy()

    def cancel_clicked(self):
        """Handle Cancel button"""
        self.destroy()

    def center_window(self):
        """Center dialog on parent"""
        self.geometry("400x300")
        self.update_idletasks()

        x = (self.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.winfo_screenheight() // 2) - (300 // 2)
        self.geometry(f"400x300+{x}+{y}")

class ExportDialog(tk.Toplevel):
    """Dialog for exporting images with options"""

    def __init__(self, parent, image: Image.Image):
        super().__init__(parent)
        self.title("Export Image")
        self.transient(parent)
        self.grab_set()

        self.image = image
        self.result = None

        self.setup_ui()
        self.center_window()

    def setup_ui(self):
        """Setup export dialog UI"""
        main_frame = tk.Frame(self, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # File format
        format_frame = tk.LabelFrame(main_frame, text="Format", font=('Arial', 9, 'bold'))
        format_frame.pack(fill='x', pady=10)

        self.format_var = tk.StringVar(value="PNG")
        formats = ["PNG", "JPEG", "GIF", "BMP", "TIFF"]
        for fmt in formats:
            tk.Radiobutton(format_frame, text=fmt, variable=self.format_var,
                          value=fmt, command=self.on_format_change).pack(anchor='w', padx=10)

        # Quality (for JPEG)
        self.quality_frame = tk.LabelFrame(main_frame, text="Quality", font=('Arial', 9, 'bold'))
        self.quality_frame.pack(fill='x', pady=10)

        self.quality_var = tk.IntVar(value=95)
        self.quality_scale = tk.Scale(self.quality_frame, from_=1, to=100,
                                     orient='horizontal', variable=self.quality_var)
        self.quality_scale.pack(fill='x', padx=10, pady=5)

        # Social media presets
        preset_frame = tk.LabelFrame(main_frame, text="Social Media Presets", font=('Arial', 9, 'bold'))
        preset_frame.pack(fill='x', pady=10)

        self.preset_var = tk.StringVar(value="None")
        preset_combo = ttk.Combobox(preset_frame, textvariable=self.preset_var,
                                   values=["None"] + list(SOCIAL_MEDIA_PRESETS.keys()),
                                   state="readonly")
        preset_combo.pack(fill='x', padx=10, pady=5)

        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=20)

        tk.Button(button_frame, text="Export", command=self.export_clicked, width=10).pack(side='left', padx=5)
        tk.Button(button_frame, text="Cancel", command=self.cancel_clicked, width=10).pack(side='left', padx=5)

        # Initialize UI state
        self.on_format_change()

    def on_format_change(self):
        """Handle format change"""
        if self.format_var.get() == "JPEG":
            self.quality_frame.pack(fill='x', pady=10, before=self.quality_frame.master.children[list(self.quality_frame.master.children.keys())[-2]])
        else:
            self.quality_frame.pack_forget()

    def export_clicked(self):
        """Handle export button"""
        file_format = self.format_var.get().lower()

        # Get file path
        file_path = filedialog.asksaveasfilename(
            title="Export Image",
            defaultextension=f".{file_format}",
            filetypes=[(f"{file_format.upper()} files", f"*.{file_format}")]
        )

        if not file_path:
            return

        try:
            export_image = self.image.copy()

            # Apply social media preset if selected
            preset = self.preset_var.get()
            if preset != "None" and preset in SOCIAL_MEDIA_PRESETS:
                size = SOCIAL_MEDIA_PRESETS[preset]
                export_image.thumbnail(size, Image.Resampling.LANCZOS)

                # Create background and center image
                background = Image.new('RGB', size, 'white')
                offset = ((size[0] - export_image.width) // 2,
                         (size[1] - export_image.height) // 2)
                background.paste(export_image, offset)
                export_image = background

            # Save with appropriate settings
            if file_format == "jpeg":
                if export_image.mode == 'RGBA':
                    rgb_image = Image.new('RGB', export_image.size, (255, 255, 255))
                    rgb_image.paste(export_image, mask=export_image.split()[-1])
                    export_image = rgb_image
                export_image.save(file_path, 'JPEG', quality=self.quality_var.get(), optimize=True)
            else:
                export_image.save(file_path)

            self.result = file_path
            messagebox.showinfo("Success", f"Image exported to {file_path}")
            self.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export image: {str(e)}")

    def cancel_clicked(self):
        """Handle cancel button"""
        self.destroy()

    def center_window(self):
        """Center dialog on parent"""
        self.geometry("400x450")
        self.update_idletasks()

        x = (self.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.winfo_screenheight() // 2) - (450 // 2)
        self.geometry(f"400x450+{x}+{y}")

class BatchDialog(tk.Toplevel):
    """Dialog for batch processing"""

    def __init__(self, parent, batch_processor: BatchProcessor):
        super().__init__(parent)
        self.title("Batch Processing")
        self.transient(parent)
        self.grab_set()

        self.batch_processor = batch_processor
        self.result = None
        self.input_files = []

        self.setup_ui()
        self.center_window()

    def setup_ui(self):
        """Setup batch dialog UI"""
        main_frame = tk.Frame(self, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # Input files
        files_frame = tk.LabelFrame(main_frame, text="Input Files", font=('Arial', 9, 'bold'))
        files_frame.pack(fill='both', expand=True, pady=10)

        # File list
        list_frame = tk.Frame(files_frame)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)

        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side='right', fill='y')

        self.file_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, height=6)
        self.file_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.file_listbox.yview)

        # File buttons
        file_buttons = tk.Frame(files_frame)
        file_buttons.pack(fill='x', padx=10, pady=5)

        tk.Button(file_buttons, text="Add Files", command=self.add_files, width=12).pack(side='left', padx=2)
        tk.Button(file_buttons, text="Add Folder", command=self.add_folder, width=12).pack(side='left', padx=2)
        tk.Button(file_buttons, text="Clear", command=self.clear_files, width=12).pack(side='left', padx=2)

        # Operations
        ops_frame = tk.LabelFrame(main_frame, text="Operations", font=('Arial', 9, 'bold'))
        ops_frame.pack(fill='x', pady=10)

        # Preset operations
        preset_frame = tk.Frame(ops_frame)
        preset_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(preset_frame, text="Preset:").pack(side='left')
        self.preset_var = tk.StringVar(value="Custom")
        preset_combo = ttk.Combobox(preset_frame, textvariable=self.preset_var,
                                   values=["Custom", "Web Optimization", "Print Optimization"] +
                                          list(SOCIAL_MEDIA_PRESETS.keys()),
                                   state="readonly")
        preset_combo.pack(side='left', padx=5, fill='x', expand=True)
        preset_combo.bind('<<ComboboxSelected>>', self.on_preset_change)

        # Output directory
        output_frame = tk.LabelFrame(main_frame, text="Output", font=('Arial', 9, 'bold'))
        output_frame.pack(fill='x', pady=10)

        dir_frame = tk.Frame(output_frame)
        dir_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(dir_frame, text="Directory:").pack(side='left')
        self.output_var = tk.StringVar()
        tk.Entry(dir_frame, textvariable=self.output_var).pack(side='left', fill='x', expand=True, padx=5)
        tk.Button(dir_frame, text="Browse", command=self.browse_output, width=8).pack(side='right')

        # Progress
        self.progress_frame = tk.LabelFrame(main_frame, text="Progress", font=('Arial', 9, 'bold'))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.progress_frame, variable=self.progress_var, maximum=100)

        self.status_label = tk.Label(self.progress_frame, text="Ready", font=('Arial', 9))

        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=20)

        self.process_button = tk.Button(button_frame, text="Process", command=self.start_processing, width=12)
        self.process_button.pack(side='left', padx=5)

        tk.Button(button_frame, text="Close", command=self.close_dialog, width=12).pack(side='left', padx=5)

    def add_files(self):
        """Add files to batch list"""
        files = filedialog.askopenfilenames(
            title="Select Images",
            filetypes=[
                ("All Images", "*.jpg *.jpeg *.png *.gif *.bmp *.tiff"),
                ("All files", "*.*")
            ]
        )

        for file in files:
            if file not in self.input_files:
                self.input_files.append(file)
                self.file_listbox.insert(tk.END, file)

    def add_folder(self):
        """Add all images from folder"""
        folder = filedialog.askdirectory(title="Select Folder")
        if folder:
            import os
            for file in os.listdir(folder):
                file_path = os.path.join(folder, file)
                if os.path.isfile(file_path) and file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff')):
                    if file_path not in self.input_files:
                        self.input_files.append(file_path)
                        self.file_listbox.insert(tk.END, file_path)

    def clear_files(self):
        """Clear file list"""
        self.input_files.clear()
        self.file_listbox.delete(0, tk.END)

    def browse_output(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_var.set(directory)

    def on_preset_change(self, event=None):
        """Handle preset change"""
        preset = self.preset_var.get()

        if preset == "Web Optimization":
            self.batch_processor.create_web_optimization_batch()
        elif preset == "Print Optimization":
            self.batch_processor.create_print_optimization_batch()
        elif preset in SOCIAL_MEDIA_PRESETS:
            self.batch_processor.create_social_media_batch(preset)
        else:
            self.batch_processor.clear_operations()

    def start_processing(self):
        """Start batch processing"""
        if not self.input_files:
            messagebox.showerror("Error", "Please add some files to process")
            return

        output_dir = self.output_var.get()
        if not output_dir:
            messagebox.showerror("Error", "Please select an output directory")
            return

        # Show progress
        self.progress_frame.pack(fill='x', pady=10, before=self.progress_frame.master.children[list(self.progress_frame.master.children.keys())[-1]])
        self.progress_bar.pack(fill='x', padx=10, pady=5)
        self.status_label.pack(padx=10, pady=5)

        # Disable process button
        self.process_button.config(state='disabled', text='Processing...')

        # Set progress callback
        self.batch_processor.set_progress_callback(self.update_progress)

        # Start processing in thread
        import threading
        thread = threading.Thread(target=self.process_files, args=(output_dir,))
        thread.daemon = True
        thread.start()

    def process_files(self, output_dir: str):
        """Process files in background thread"""
        try:
            results = self.batch_processor.process_files(self.input_files, output_dir)

            # Update UI in main thread
            self.after(0, self.processing_complete, results)

        except Exception as e:
            self.after(0, self.processing_error, str(e))

    def update_progress(self, current: int, total: int, status: str):
        """Update progress bar"""
        if total > 0:
            percentage = (current / total) * 100
            self.progress_var.set(percentage)

        self.status_label.config(text=status)
        self.update()

    def processing_complete(self, results: dict):
        """Handle processing completion"""
        self.process_button.config(state='normal', text='Process')

        message = f"Processing complete!\n\n"
        message += f"Processed: {results['processed']} files\n"
        message += f"Failed: {results['failed']} files"

        if results['errors']:
            message += f"\n\nErrors:\n" + "\n".join(results['errors'][:5])
            if len(results['errors']) > 5:
                message += f"\n... and {len(results['errors']) - 5} more"

        messagebox.showinfo("Batch Processing Complete", message)
        self.result = results

    def processing_error(self, error: str):
        """Handle processing error"""
        self.process_button.config(state='normal', text='Process')
        messagebox.showerror("Processing Error", f"An error occurred during processing:\n{error}")

    def close_dialog(self):
        """Close dialog"""
        self.batch_processor.cancel_processing()
        self.destroy()

    def center_window(self):
        """Center dialog on parent"""
        self.geometry("600x500")
        self.update_idletasks()

        x = (self.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.winfo_screenheight() // 2) - (500 // 2)
        self.geometry(f"600x500+{x}+{y}")
