#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Excel spreadsheet of world's most naturally beautiful countries
with Arabic and English content, proper RTL formatting
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.formatting.rule import CellIsRule
from openpyxl.utils import get_column_letter
import os

def create_beautiful_countries_excel():
    # Create workbook and worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "أجمل الدول طبيعياً"
    
    # Define fonts
    arabic_font = Font(name='Arabic Typesetting', size=12, bold=False)
    arabic_header_font = Font(name='Arabic Typesetting', size=14, bold=True, color='FFFFFF')
    english_font = Font(name='Cal<PERSON>ri', size=11, bold=False)
    english_header_font = Font(name='Calibri', size=12, bold=True, color='FFFFFF')
    
    # Define colors and fills
    header_fill = PatternFill(start_color='2E8B57', end_color='2E8B57', fill_type='solid')  # Sea Green
    even_row_fill = PatternFill(start_color='F0F8FF', end_color='F0F8FF', fill_type='solid')  # Alice Blue
    top_rated_fill = PatternFill(start_color='FFD700', end_color='FFD700', fill_type='solid')  # Gold
    
    # Define borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Headers
    headers = [
        'اسم الدولة (عربي)',
        'Country Name (English)',
        'القارة',
        'المعالم الطبيعية المشهورة',
        'تقييم الجمال الطبيعي (1-10)',
        'أفضل وقت للزيارة',
        'وصف المعالم الطبيعية'
    ]
    
    # Set headers
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        if col in [1, 3, 4, 6, 7]:  # Arabic columns
            cell.font = arabic_header_font
            cell.alignment = Alignment(horizontal='right', vertical='center', text_rotation=0)
        else:  # English columns
            cell.font = english_header_font
            cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.fill = header_fill
        cell.border = thin_border
    
    # Country data
    countries_data = [
        ['سويسرا', 'Switzerland', 'أوروبا', 'جبال الألب، بحيرة جنيف، ماترهورن', 10, 'يونيو - سبتمبر', 'جبال شاهقة، بحيرات صافية، مروج خضراء'],
        ['نيوزيلندا', 'New Zealand', 'أوقيانوسيا', 'فيوردلاند، جبال الألب الجنوبية، خليج الجزر', 10, 'ديسمبر - فبراير', 'فيوردات، جبال، شواطئ، غابات مطيرة'],
        ['النرويج', 'Norway', 'أوروبا', 'الفيوردات، الشفق القطبي، لوفوتين', 10, 'يونيو - أغسطس', 'فيوردات عميقة، جبال، شلالات، ظاهرة الشفق'],
        ['أيسلندا', 'Iceland', 'أوروبا', 'الينابيع الحارة، الأنهار الجليدية، الشفق القطبي', 9, 'يونيو - أغسطس', 'براكين، أنهار جليدية، ينابيع حارة، شلالات'],
        ['كندا', 'Canada', 'أمريكا الشمالية', 'جبال روكي، شلالات نياجرا، البحيرات العظمى', 9, 'يونيو - سبتمبر', 'جبال، غابات، بحيرات، شلالات ضخمة'],
        ['تشيلي', 'Chile', 'أمريكا الجنوبية', 'صحراء أتاكاما، باتاغونيا، جزيرة الفصح', 9, 'ديسمبر - مارس', 'صحاري، أنهار جليدية، سواحل، جبال الأنديز'],
        ['نيبال', 'Nepal', 'آسيا', 'جبل إيفرست، سلسلة الهيمالايا، وادي كاتماندو', 9, 'أكتوبر - ديسمبر', 'أعلى قمم العالم، وديان عميقة، غابات'],
        ['المالديف', 'Maldives', 'آسيا', 'الشعاب المرجانية، الشواطئ البيضاء، البحيرات الزرقاء', 9, 'نوفمبر - أبريل', 'جزر مرجانية، مياه فيروزية، شواطئ رملية'],
        ['الأرجنتين', 'Argentina', 'أمريكا الجنوبية', 'باتاغونيا، شلالات إجوازو، جبال الأنديز', 8, 'ديسمبر - مارس', 'أنهار جليدية، شلالات، سهول، جبال'],
        ['البرازيل', 'Brazil', 'أمريكا الجنوبية', 'شلالات إجوازو، غابات الأمازون، ريو دي جانيرو', 8, 'أبريل - سبتمبر', 'غابات مطيرة، شلالات، شواطئ، جبال'],
        ['أستراليا', 'Australia', 'أوقيانوسيا', 'الحاجز المرجاني العظيم، أولورو، الأوتلاند', 8, 'أبريل - سبتمبر', 'شعاب مرجانية، صحاري، غابات، سواحل'],
        ['كوستاريكا', 'Costa Rica', 'أمريكا الوسطى', 'مونتيفيردي، مانويل أنطونيو، أريانال', 8, 'ديسمبر - أبريل', 'غابات مطيرة، براكين، شواطئ، تنوع حيوي'],
        ['اليابان', 'Japan', 'آسيا', 'جبل فوجي، أزهار الكرز، الحدائق اليابانية', 8, 'مارس - مايو', 'جبال، غابات، حدائق، ينابيع حارة'],
        ['بيرو', 'Peru', 'أمريكا الجنوبية', 'ماchu بيchu، جبال الأنديز، غابات الأمازون', 8, 'مايو - سبتمبر', 'آثار قديمة، جبال عالية، غابات مطيرة'],
        ['كينيا', 'Kenya', 'أفريقيا', 'ماساي مارا، جبل كينيا، ساحل المحيط الهندي', 8, 'يوليو - أكتوبر', 'سافانا، جبال، شواطئ، حياة برية'],
        ['جنوب أفريقيا', 'South Africa', 'أفريقيا', 'كيب تاون، كروجر، دراكنزبرغ', 8, 'أبريل - مايو', 'جبال، سواحل، سافانا، كروم عنب'],
        ['تنزانيا', 'Tanzania', 'أفريقيا', 'سيرينجيتي، نجورونجورو، كليمنجارو', 8, 'يونيو - أكتوبر', 'سافانا، جبال، بحيرات، حياة برية'],
        ['إندونيسيا', 'Indonesia', 'آسيا', 'بالي، كومودو، بوروبودور، راجا أمبات', 7, 'أبريل - أكتوبر', 'جزر استوائية، براكين، شعاب مرجانية'],
        ['تايلاند', 'Thailand', 'آسيا', 'فوكيت، كرابي، شيانغ ماي', 7, 'نوفمبر - فبراير', 'شواطئ استوائية، جبال، معابد، غابات'],
        ['فيتنام', 'Vietnam', 'آسيا', 'خليج ها لونغ، سابا، دلتا الميكونغ', 7, 'أكتوبر - أبريل', 'خلجان، مدرجات أرز، دلتا، كهوف'],
        ['الفلبين', 'Philippines', 'آسيا', 'بالاوان، بوراكاي، شوكولات هيلز', 7, 'ديسمبر - مايو', 'جزر استوائية، شواطئ، بحيرات، تلال'],
        ['ماليزيا', 'Malaysia', 'آسيا', 'لانكاوي، كاميرون هايلاندز، بورنيو', 7, 'مارس - أكتوبر', 'جزر، مرتفعات، غابات مطيرة، كهوف'],
        ['سريلانكا', 'Sri Lanka', 'آسيا', 'سيجيريا، نوارا إليا، ميريسا', 7, 'ديسمبر - مارس', 'مرتفعات، شواطئ، معابد، مزارع شاي'],
        ['المغرب', 'Morocco', 'أفريقيا', 'جبال الأطلس، الصحراء الكبرى، الدار البيضاء', 7, 'أبريل - مايو', 'جبال، صحاري، سواحل، واحات'],
        ['تركيا', 'Turkey', 'آسيا/أوروبا', 'كابادوكيا، باموكالي، البوسفور', 7, 'أبريل - يونيو', 'تشكيلات صخرية، ينابيع حارة، سواحل'],
        ['اليونان', 'Greece', 'أوروبا', 'سانتوريني، ميكونوس، كريت', 7, 'أبريل - يونيو', 'جزر، سواحل، جبال، آثار قديمة'],
        ['إيطاليا', 'Italy', 'أوروبا', 'توسكانا، أمالفي، الدولوميت', 7, 'أبريل - يونيو', 'سواحل، جبال، بحيرات، تلال'],
        ['فرنسا', 'France', 'أوروبا', 'الألب الفرنسية، بروفانس، كورسيكا', 7, 'مايو - سبتمبر', 'جبال، حقول لافندر، سواحل، وديان'],
        ['إسبانيا', 'Spain', 'أوروبا', 'جزر الكناري، الأندلس، البيرينيه', 7, 'أبريل - يونيو', 'جزر، جبال، سواحل، سهول'],
        ['الولايات المتحدة', 'United States', 'أمريكا الشمالية', 'يلوستون، جراند كانيون، يوسمايت', 8, 'مايو - سبتمبر', 'حدائق وطنية، أخاديد، جبال، صحاري']
    ]
    
    # Add data to worksheet
    for row_idx, country_data in enumerate(countries_data, 2):
        for col_idx, value in enumerate(country_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            
            # Apply fonts and alignment
            if col_idx in [1, 3, 4, 6, 7]:  # Arabic columns
                cell.font = arabic_font
                cell.alignment = Alignment(horizontal='right', vertical='center', wrap_text=True)
            else:  # English columns
                cell.font = english_font
                cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            
            # Apply borders
            cell.border = thin_border
            
            # Apply alternating row colors
            if row_idx % 2 == 0:
                cell.fill = even_row_fill
    
    # Apply conditional formatting for top-rated countries (rating >= 9)
    rating_range = f"E2:E{len(countries_data) + 1}"
    rule = CellIsRule(operator='greaterThanOrEqual', formula=['9'], fill=top_rated_fill)
    ws.conditional_formatting.add(rating_range, rule)
    
    # Adjust column widths
    column_widths = {
        'A': 20,  # Arabic country name
        'B': 18,  # English country name
        'C': 15,  # Continent
        'D': 35,  # Landmarks
        'E': 12,  # Rating
        'F': 18,  # Best time
        'G': 40   # Description
    }
    
    for col_letter, width in column_widths.items():
        ws.column_dimensions[col_letter].width = width
    
    # Set row heights
    for row in range(1, len(countries_data) + 2):
        ws.row_dimensions[row].height = 25 if row == 1 else 35
    
    # Add summary section
    summary_start_row = len(countries_data) + 4
    
    # Summary headers
    ws.cell(row=summary_start_row, column=1, value='إحصائيات الجمال الطبيعي').font = arabic_header_font
    ws.cell(row=summary_start_row, column=1).fill = header_fill
    ws.cell(row=summary_start_row, column=1).alignment = Alignment(horizontal='right', vertical='center')
    
    # Summary data
    summary_data = [
        ['إجمالي الدول:', len(countries_data)],
        ['الدول ذات التقييم الأعلى (10/10):', len([c for c in countries_data if c[4] == 10])],
        ['الدول ذات التقييم العالي (9/10):', len([c for c in countries_data if c[4] == 9])],
        ['متوسط التقييم:', round(sum([c[4] for c in countries_data]) / len(countries_data), 1)]
    ]
    
    for i, (label, value) in enumerate(summary_data):
        row = summary_start_row + i + 1
        ws.cell(row=row, column=1, value=label).font = arabic_font
        ws.cell(row=row, column=1).alignment = Alignment(horizontal='right', vertical='center')
        ws.cell(row=row, column=2, value=value).font = english_font
        ws.cell(row=row, column=2).alignment = Alignment(horizontal='center', vertical='center')
    
    # Set print settings
    ws.page_setup.orientation = ws.ORIENTATION_LANDSCAPE
    ws.page_setup.paperSize = ws.PAPERSIZE_A4
    ws.page_margins.left = 0.5
    ws.page_margins.right = 0.5
    ws.page_margins.top = 0.75
    ws.page_margins.bottom = 0.75
    
    # Set print area
    ws.print_area = f'A1:G{len(countries_data) + 1}'
    
    # Freeze header row
    ws.freeze_panes = 'A2'
    
    # Save the file
    filename = 'أجمل_الدول_طبيعياً.xlsx'
    wb.save(filename)
    print(f"تم إنشاء ملف Excel بنجاح: {filename}")
    print(f"Excel file created successfully: {filename}")
    
    return filename

if __name__ == "__main__":
    create_beautiful_countries_excel()
